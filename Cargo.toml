[workspace]
members = [
    "orion-core",
    "orion-cli",
    "orion-knowledge"
]
resolver = "2"

[workspace.package]
version = "0.1.0"
edition = "2021"
authors = ["Orion Team"]
license = "MIT OR Apache-2.0"
repository = "https://github.com/orion-ai/orion"
description = "下一代AI Agent运行时和协作平台"
keywords = ["ai", "agent", "rust", "async", "llm"]
categories = ["command-line-utilities", "development-tools"]

[workspace.dependencies]
# 异步运行时
tokio = { version = "1.0", features = ["full"] }

# CLI 解析
clap = { version = "4.0", features = ["derive", "env"] }

# 序列化
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
serde_yaml = "0.9"
toml = "0.8"

# 错误处理
thiserror = "1.0"
anyhow = "1.0"

# 日志系统
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "json"] }
tracing-appender = "0.2"

# HTTP 客户端
reqwest = { version = "0.11", features = ["json", "stream"] }
url = "2.4"

# 数据库
rusqlite = { version = "0.29", features = ["bundled"] }

# UUID 生成
uuid = { version = "1.0", features = ["v4", "serde"] }

# 异步工具
futures = "0.3"
async-trait = "0.1"

# 时间处理
chrono = { version = "0.4", features = ["serde"] }

# 文件系统操作
tempfile = "3.0"

# 交互式CLI (用于orion-cli)
rustyline = "12.0"

# 终端检测
atty = "0.2"

# 随机数生成
rand = "0.8"

# 正则表达式
regex = "1.10"

# 目录操作
dirs = "5.0"

# 系统信息
num_cpus = "1.16"

# Windows API
winapi = { version = "0.3", features = ["sysinfoapi", "memoryapi"] }

# 测试工具
mockall = "0.11"
