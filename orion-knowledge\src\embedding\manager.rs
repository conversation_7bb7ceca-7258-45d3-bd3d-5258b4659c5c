//! # 嵌入模型管理器
//!
//! 统一管理不同类型的嵌入模型，提供模型切换、性能优化、监控等功能。

use crate::{
    config::EmbeddingConfig,
    error::Result,
    embedding::{
        Embedding, Embedding<PERSON><PERSON>ider, BatchEmbeddingResult, ModelInfo,
        local_embedder::LocalEmbedder,
        remote_embedder::{OpenAIEmbedder, CohereEmbedder, OllamaEmbedder},
        performance::{PerformanceEmbedder, PerformanceConfig, PerformanceMetrics},
    },
    KnowledgeError,
};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use std::time::Instant;
use tokio::sync::RwLock;
use tracing::{info, warn, error};

/// 嵌入模型类型
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum EmbedderType {
    /// 本地模型
    Local,
    /// OpenAI API
    OpenAI,
    /// Cohere API
    Cohere,
    /// Ollama 本地服务
    Ollama,
}

impl std::fmt::Display for EmbedderType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            EmbedderType::Local => write!(f, "local"),
            EmbedderType::OpenAI => write!(f, "openai"),
            EmbedderType::Cohere => write!(f, "cohere"),
            EmbedderType::Ollama => write!(f, "ollama"),
        }
    }
}

impl std::str::FromStr for EmbedderType {
    type Err = KnowledgeError;

    fn from_str(s: &str) -> Result<Self> {
        match s.to_lowercase().as_str() {
            "local" => Ok(EmbedderType::Local),
            "openai" => Ok(EmbedderType::OpenAI),
            "cohere" => Ok(EmbedderType::Cohere),
            "ollama" => Ok(EmbedderType::Ollama),
            _ => Err(KnowledgeError::embedding_error(&format!("不支持的嵌入器类型: {}", s))),
        }
    }
}

/// 嵌入器包装器
enum EmbedderWrapper {
    Local(PerformanceEmbedder<LocalEmbedder>),
    OpenAI(PerformanceEmbedder<OpenAIEmbedder>),
    Cohere(PerformanceEmbedder<CohereEmbedder>),
    Ollama(PerformanceEmbedder<OllamaEmbedder>),
}

impl EmbedderWrapper {
    async fn embed_text(&self, text: &str) -> Result<Embedding> {
        match self {
            EmbedderWrapper::Local(embedder) => embedder.embed_text_optimized(text).await,
            EmbedderWrapper::OpenAI(embedder) => embedder.embed_text_optimized(text).await,
            EmbedderWrapper::Cohere(embedder) => embedder.embed_text_optimized(text).await,
            EmbedderWrapper::Ollama(embedder) => embedder.embed_text_optimized(text).await,
        }
    }

    async fn embed_batch(&self, texts: &[String]) -> Result<BatchEmbeddingResult> {
        match self {
            EmbedderWrapper::Local(embedder) => embedder.embed_batch_optimized(texts).await,
            EmbedderWrapper::OpenAI(embedder) => embedder.embed_batch_optimized(texts).await,
            EmbedderWrapper::Cohere(embedder) => embedder.embed_batch_optimized(texts).await,
            EmbedderWrapper::Ollama(embedder) => embedder.embed_batch_optimized(texts).await,
        }
    }

    async fn get_metrics(&self) -> PerformanceMetrics {
        match self {
            EmbedderWrapper::Local(embedder) => embedder.get_metrics().await,
            EmbedderWrapper::OpenAI(embedder) => embedder.get_metrics().await,
            EmbedderWrapper::Cohere(embedder) => embedder.get_metrics().await,
            EmbedderWrapper::Ollama(embedder) => embedder.get_metrics().await,
        }
    }

    fn model_info(&self) -> ModelInfo {
        match self {
            EmbedderWrapper::Local(embedder) => embedder.provider_model_info().clone(),
            EmbedderWrapper::OpenAI(embedder) => embedder.provider_model_info().clone(),
            EmbedderWrapper::Cohere(embedder) => embedder.provider_model_info().clone(),
            EmbedderWrapper::Ollama(embedder) => embedder.provider_model_info().clone(),
        }
    }

    fn dimension(&self) -> usize {
        match self {
            EmbedderWrapper::Local(embedder) => embedder.provider_dimension(),
            EmbedderWrapper::OpenAI(embedder) => embedder.provider_dimension(),
            EmbedderWrapper::Cohere(embedder) => embedder.provider_dimension(),
            EmbedderWrapper::Ollama(embedder) => embedder.provider_dimension(),
        }
    }
}

/// 嵌入模型管理器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EmbeddingManagerConfig {
    /// 主要嵌入器类型
    pub primary_embedder: EmbedderType,
    /// 备用嵌入器类型列表
    pub fallback_embedders: Vec<EmbedderType>,
    /// 性能优化配置
    pub performance_config: PerformanceConfig,
    /// 是否启用自动故障转移
    pub enable_failover: bool,
    /// 故障转移超时时间（秒）
    pub failover_timeout_seconds: u64,
    /// 健康检查间隔（秒）
    pub health_check_interval_seconds: u64,
}

impl Default for EmbeddingManagerConfig {
    fn default() -> Self {
        Self {
            primary_embedder: EmbedderType::Local,
            fallback_embedders: vec![EmbedderType::OpenAI],
            performance_config: PerformanceConfig::default(),
            enable_failover: true,
            failover_timeout_seconds: 30,
            health_check_interval_seconds: 300, // 5分钟
        }
    }
}

/// 嵌入模型管理器
pub struct EmbeddingManager {
    /// 管理器配置
    config: EmbeddingManagerConfig,
    /// 当前活跃的嵌入器
    active_embedder: Arc<RwLock<Option<EmbedderWrapper>>>,
    /// 备用嵌入器列表
    fallback_embedders: Arc<RwLock<Vec<EmbedderWrapper>>>,
    /// 嵌入配置
    embedding_config: EmbeddingConfig,
}

impl EmbeddingManager {
    /// 创建新的嵌入模型管理器
    pub async fn new(
        embedding_config: EmbeddingConfig,
        manager_config: EmbeddingManagerConfig,
    ) -> Result<Self> {
        info!("初始化嵌入模型管理器，主要类型: {}", manager_config.primary_embedder);

        let manager = Self {
            config: manager_config,
            active_embedder: Arc::new(RwLock::new(None)),
            fallback_embedders: Arc::new(RwLock::new(Vec::new())),
            embedding_config,
        };

        // 初始化主要嵌入器
        manager.initialize_primary_embedder().await?;

        // 初始化备用嵌入器
        manager.initialize_fallback_embedders().await?;

        info!("嵌入模型管理器初始化完成");
        Ok(manager)
    }

    /// 计算单个文本的嵌入向量
    pub async fn embed_text(&self, text: &str) -> Result<Embedding> {
        let start_time = Instant::now();

        // 尝试使用主要嵌入器
        if let Some(result) = self.try_embed_text_with_active(text).await {
            return result;
        }

        // 如果启用故障转移，尝试备用嵌入器
        if self.config.enable_failover {
            if let Some(result) = self.try_embed_text_with_fallback(text).await {
                return result;
            }
        }

        let duration = start_time.elapsed();
        error!("所有嵌入器都失败了，耗时: {:?}", duration);
        Err(KnowledgeError::embedding_error("所有嵌入器都不可用"))
    }

    /// 批量计算嵌入向量
    pub async fn embed_batch(&self, texts: &[String]) -> Result<BatchEmbeddingResult> {
        let start_time = Instant::now();

        // 尝试使用主要嵌入器
        if let Some(result) = self.try_embed_batch_with_active(texts).await {
            return result;
        }

        // 如果启用故障转移，尝试备用嵌入器
        if self.config.enable_failover {
            if let Some(result) = self.try_embed_batch_with_fallback(texts).await {
                return result;
            }
        }

        let duration = start_time.elapsed();
        error!("所有嵌入器都失败了，耗时: {:?}", duration);
        Err(KnowledgeError::embedding_error("所有嵌入器都不可用"))
    }

    /// 获取当前活跃嵌入器的模型信息
    pub async fn model_info(&self) -> Option<ModelInfo> {
        let active = self.active_embedder.read().await;
        active.as_ref().map(|embedder| embedder.model_info())
    }

    /// 获取当前活跃嵌入器的维度
    pub async fn dimension(&self) -> Option<usize> {
        let active = self.active_embedder.read().await;
        active.as_ref().map(|embedder| embedder.dimension())
    }

    /// 获取性能统计信息
    pub async fn get_performance_metrics(&self) -> Option<PerformanceMetrics> {
        let active = self.active_embedder.read().await;
        if let Some(embedder) = active.as_ref() {
            Some(embedder.get_metrics().await)
        } else {
            None
        }
    }

    /// 切换到指定类型的嵌入器
    pub async fn switch_embedder(&self, embedder_type: EmbedderType) -> Result<()> {
        info!("切换嵌入器到: {}", embedder_type);

        let new_embedder = self.create_embedder(embedder_type).await?;
        
        let mut active = self.active_embedder.write().await;
        *active = Some(new_embedder);

        info!("嵌入器切换完成");
        Ok(())
    }

    /// 执行健康检查
    pub async fn health_check(&self) -> Result<bool> {
        let active = self.active_embedder.read().await;

        if let Some(embedder) = active.as_ref() {
            // 使用简单的测试文本进行健康检查
            match embedder.embed_text("health check").await {
                Ok(_) => {
                    info!("嵌入器健康检查通过");
                    Ok(true)
                }
                Err(e) => {
                    warn!("嵌入器健康检查失败: {}", e);
                    Ok(false)
                }
            }
        } else {
            warn!("没有活跃的嵌入器");
            Ok(false)
        }
    }

    /// 初始化主要嵌入器
    async fn initialize_primary_embedder(&self) -> Result<()> {
        let embedder = self.create_embedder(self.config.primary_embedder.clone()).await?;
        let mut active = self.active_embedder.write().await;
        *active = Some(embedder);
        Ok(())
    }

    /// 初始化备用嵌入器
    async fn initialize_fallback_embedders(&self) -> Result<()> {
        let mut fallbacks = Vec::new();

        for embedder_type in &self.config.fallback_embedders {
            match self.create_embedder(embedder_type.clone()).await {
                Ok(embedder) => {
                    fallbacks.push(embedder);
                    info!("备用嵌入器初始化成功: {}", embedder_type);
                }
                Err(e) => {
                    warn!("备用嵌入器初始化失败: {} - {}", embedder_type, e);
                }
            }
        }

        let mut fallback_list = self.fallback_embedders.write().await;
        *fallback_list = fallbacks;

        Ok(())
    }

    /// 创建指定类型的嵌入器
    async fn create_embedder(&self, embedder_type: EmbedderType) -> Result<EmbedderWrapper> {
        match embedder_type {
            EmbedderType::Local => {
                let provider = LocalEmbedder::new(&self.embedding_config).await?;
                let performance_embedder = PerformanceEmbedder::new(provider, self.config.performance_config.clone());
                Ok(EmbedderWrapper::Local(performance_embedder))
            }
            EmbedderType::OpenAI => {
                let provider = OpenAIEmbedder::new(&self.embedding_config).await?;
                let performance_embedder = PerformanceEmbedder::new(provider, self.config.performance_config.clone());
                Ok(EmbedderWrapper::OpenAI(performance_embedder))
            }
            EmbedderType::Cohere => {
                let provider = CohereEmbedder::new(&self.embedding_config).await?;
                let performance_embedder = PerformanceEmbedder::new(provider, self.config.performance_config.clone());
                Ok(EmbedderWrapper::Cohere(performance_embedder))
            }
            EmbedderType::Ollama => {
                let provider = OllamaEmbedder::new(&self.embedding_config).await?;
                let performance_embedder = PerformanceEmbedder::new(provider, self.config.performance_config.clone());
                Ok(EmbedderWrapper::Ollama(performance_embedder))
            }
        }
    }

    /// 尝试使用主要嵌入器计算单个文本嵌入
    async fn try_embed_text_with_active(&self, text: &str) -> Option<Result<Embedding>> {
        let active = self.active_embedder.read().await;
        if let Some(embedder) = active.as_ref() {
            Some(embedder.embed_text(text).await)
        } else {
            None
        }
    }

    /// 尝试使用备用嵌入器计算单个文本嵌入
    async fn try_embed_text_with_fallback(&self, text: &str) -> Option<Result<Embedding>> {
        let fallbacks = self.fallback_embedders.read().await;

        for embedder in fallbacks.iter() {
            match embedder.embed_text(text).await {
                Ok(embedding) => {
                    info!("备用嵌入器成功处理请求");
                    return Some(Ok(embedding));
                }
                Err(e) => {
                    warn!("备用嵌入器失败: {}", e);
                    continue;
                }
            }
        }

        None
    }

    /// 尝试使用主要嵌入器计算批量嵌入
    async fn try_embed_batch_with_active(&self, texts: &[String]) -> Option<Result<BatchEmbeddingResult>> {
        let active = self.active_embedder.read().await;
        if let Some(embedder) = active.as_ref() {
            Some(embedder.embed_batch(texts).await)
        } else {
            None
        }
    }

    /// 尝试使用备用嵌入器计算批量嵌入
    async fn try_embed_batch_with_fallback(&self, texts: &[String]) -> Option<Result<BatchEmbeddingResult>> {
        let fallbacks = self.fallback_embedders.read().await;

        for embedder in fallbacks.iter() {
            match embedder.embed_batch(texts).await {
                Ok(result) => {
                    info!("备用嵌入器成功处理批量请求");
                    return Some(Ok(result));
                }
                Err(e) => {
                    warn!("备用嵌入器批量处理失败: {}", e);
                    continue;
                }
            }
        }

        None
    }
}

#[async_trait::async_trait]
impl EmbeddingProvider for EmbeddingManager {
    async fn embed_text(&self, text: &str) -> Result<Embedding> {
        self.embed_text(text).await
    }

    async fn embed_batch(&self, texts: &[String]) -> Result<BatchEmbeddingResult> {
        self.embed_batch(texts).await
    }

    fn model_info(&self) -> &ModelInfo {
        // 这个方法需要同步返回，但我们的实现是异步的
        // 这里提供一个默认实现，实际使用时应该调用 model_info() 异步方法
        use std::sync::LazyLock;
        static DEFAULT_MODEL_INFO: LazyLock<ModelInfo> = LazyLock::new(|| ModelInfo {
            name: "embedding-manager".to_string(),
            version: "1.0.0".to_string(),
            model_type: "manager".to_string(),
        });
        &DEFAULT_MODEL_INFO
    }

    fn dimension(&self) -> usize {
        // 返回配置中的维度，实际使用时应该调用 dimension() 异步方法
        self.embedding_config.dimension
    }
}
