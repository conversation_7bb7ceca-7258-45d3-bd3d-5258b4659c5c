# Orion Knowledge Base

智能代码知识库系统，基于 RAG（Retrieval-Augmented Generation）架构，提供代码语义搜索和上下文合成功能。

## 🌟 特性

- **多语言支持**: 支持 Rust、JavaScript、TypeScript、Python、Java、C/C++、Go 等主流编程语言
- **智能解析**: 基于 AST 的语义分析和代码分块
- **向量搜索**: 使用嵌入向量进行语义相似度搜索
- **混合搜索**: 结合向量搜索和关键词搜索，提高搜索准确性
- **上下文合成**: 智能组织搜索结果，生成结构化上下文信息
- **灵活配置**: 支持多种嵌入模型（本地、OpenAI、Ollama、自定义API）
- **高性能存储**: 基于 SQLite 的向量数据库，支持大规模代码库索引

## 🚀 快速开始

### 1. 安装依赖

```toml
[dependencies]
orion-knowledge = { path = "../orion-knowledge" }
tokio = { version = "1.0", features = ["full"] }
tracing-subscriber = "0.3"
```

### 2. 基本使用

```rust
use orion_knowledge::{Config, KnowledgeBase};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化知识库
    let mut kb = KnowledgeBase::from_config_file("knowledge.toml").await?;
    
    // 索引代码目录
    kb.index_directory("./src").await?;
    
    // 执行搜索
    let results = kb.search("error handling function").await?;
    
    // 获取上下文
    let context = kb.get_context(&results).await?;
    
    println!("找到 {} 个相关结果", results.len());
    println!("上下文长度: {} tokens", context.token_count);
    
    Ok(())
}
```

### 3. 配置文件

创建 `knowledge.toml` 文件：

```toml
[database]
path = "./.orion/knowledge.db"
pool_size = 10
enable_wal = true

[parser]
supported_languages = ["rust", "javascript", "python"]
chunking_strategy = "Semantic"
chunk_size = 1000

[embedding]
model_type = "Ollama"
model_path = "http://localhost:11434"
dimension = 384

[query]
default_top_k = 10
similarity_threshold = 0.7

[context]
max_context_length = 4096
enable_compression = true
```

## 📖 功能详解

### 代码解析

支持多种分块策略：

- **Semantic**: 基于 AST 的语义分块，按函数、类、结构体等分割
- **FixedSize**: 固定大小分块，适用于大文件
- **SlidingWindow**: 滑动窗口分块，保持上下文连续性
- **Hybrid**: 混合策略，结合语义和大小分块的优点

### 搜索功能

支持多种搜索模式：

```rust
// 语义搜索
let results = kb.search("find async function that handles HTTP requests").await?;

// 语言过滤
let results = kb.search("lang:rust error handling").await?;

// 文件过滤
let results = kb.search("file:main.rs initialization code").await?;

// 精确匹配
let results = kb.search("exact:\"pub fn new\"").await?;

// 符号搜索
let results = kb.search("function main").await?;
```

### 嵌入模型

支持多种嵌入模型：

1. **本地模型**: 简化的 TF-IDF 向量化（用于测试）
2. **OpenAI**: 使用 OpenAI 的 text-embedding-ada-002
3. **Ollama**: 本地部署的开源模型
4. **自定义 API**: 支持自定义嵌入服务

### 上下文合成

智能合成上下文信息：

- **重要性排序**: 根据相似度和代码特征排序
- **智能压缩**: 在 token 限制内保留关键信息
- **结构化输出**: 生成易于理解的上下文格式
- **多样性过滤**: 避免重复和冗余信息

## 🔧 高级配置

### 自定义嵌入模型

```rust
use orion_knowledge::{Config, EmbeddingConfig, EmbeddingModelType};

let config = Config {
    embedding: EmbeddingConfig {
        model_type: EmbeddingModelType::Custom,
        model_path: "https://your-api-endpoint.com/embed".to_string(),
        dimension: 512,
        api_key: Some("your-api-key".to_string()),
        ..Default::default()
    },
    ..Default::default()
};
```

### 数据库优化

```rust
use orion_knowledge::{DatabaseConfig};

let db_config = DatabaseConfig {
    path: "./knowledge.db".into(),
    pool_size: 20,           // 增加连接池
    cache_size_mb: 128,      // 增加缓存
    enable_wal: true,        // 启用 WAL 模式
    query_timeout: 60,       // 增加超时时间
};
```

## 📊 性能特性

- **并发处理**: 支持多线程并发索引和搜索
- **增量更新**: 支持增量索引，避免重复处理
- **缓存机制**: 嵌入向量缓存，减少重复计算
- **批量处理**: 批量嵌入向量计算，提高效率
- **内存优化**: 流式处理大文件，控制内存使用

## 🛠️ 开发工具

### 索引管理

```rust
// 重建索引
kb.clear().await?;
kb.index_directory("./src").await?;

// 优化数据库
kb.optimize().await?;

// 获取统计信息
let stats = kb.get_stats().await?;
println!("总代码块: {}", stats.total_chunks);
```

### 调试功能

```rust
// 启用详细日志
std::env::set_var("RUST_LOG", "orion_knowledge=debug");
tracing_subscriber::init();

// 检查解析结果
let chunks = parser.parse_file("src/main.rs").await?;
for chunk in chunks {
    println!("符号: {:?}, 类型: {:?}", chunk.symbol_name, chunk.chunk_type);
}
```

## 🧪 测试

运行测试套件：

```bash
cd orion-knowledge
cargo test
```

运行示例：

```bash
cargo run --example basic_usage
```

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📝 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🔗 相关项目

- [Orion CLI](../orion-cli) - 智能命令行工具
- [Orion Core](../orion-core) - 核心代理系统

---

*由 Orion AI Agent 开发团队维护*