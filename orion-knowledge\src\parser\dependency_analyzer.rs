//! # 代码依赖分析器
//!
//! 分析代码块之间的依赖关系，包括导入语句、函数调用、类继承等关系。

use crate::{
    error::Result,
    parser::ast_parser::{AstNode, SyntaxTree, SymbolInfo},
};
use serde::{Deserialize, Serialize};
use std::collections::{HashMap, HashSet};

/// 依赖关系类型
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum DependencyType {
    /// 导入依赖
    Import,
    /// 函数调用依赖
    FunctionCall,
    /// 类继承依赖
    Inheritance,
    /// 接口实现依赖
    Implementation,
    /// 类型引用依赖
    TypeReference,
    /// 模块依赖
    Module,
    /// 宏调用依赖
    MacroCall,
}

/// 依赖关系
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Dependency {
    /// 依赖类型
    pub dependency_type: DependencyType,
    /// 源符号（依赖者）
    pub source_symbol: String,
    /// 目标符号（被依赖者）
    pub target_symbol: String,
    /// 依赖的文件路径
    pub target_file: Option<String>,
    /// 依赖强度（0.0-1.0）
    pub strength: f32,
    /// 依赖位置（行号）
    pub location: usize,
}

/// 依赖图
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DependencyGraph {
    /// 所有依赖关系
    pub dependencies: Vec<Dependency>,
    /// 符号到依赖的映射
    pub symbol_dependencies: HashMap<String, Vec<Dependency>>,
    /// 反向依赖映射（被依赖者到依赖者）
    pub reverse_dependencies: HashMap<String, Vec<String>>,
}

/// 依赖分析器
pub struct DependencyAnalyzer {
    /// 语言特定的分析器
    language_analyzers: HashMap<String, Box<dyn LanguageDependencyAnalyzer>>,
}

/// 语言特定的依赖分析器接口
pub trait LanguageDependencyAnalyzer {
    /// 分析 AST 中的依赖关系
    fn analyze_dependencies(&self, ast: &SyntaxTree, symbols: &[SymbolInfo]) -> Result<Vec<Dependency>>;
    
    /// 提取导入语句
    fn extract_imports(&self, ast: &SyntaxTree) -> Result<Vec<Dependency>>;
    
    /// 提取函数调用
    fn extract_function_calls(&self, ast: &SyntaxTree, symbols: &[SymbolInfo]) -> Result<Vec<Dependency>>;
    
    /// 提取类型引用
    fn extract_type_references(&self, ast: &SyntaxTree) -> Result<Vec<Dependency>>;
}

impl DependencyAnalyzer {
    /// 创建新的依赖分析器
    pub fn new() -> Self {
        let mut language_analyzers: HashMap<String, Box<dyn LanguageDependencyAnalyzer>> = HashMap::new();
        
        // 注册语言特定的分析器
        language_analyzers.insert("rust".to_string(), Box::new(RustDependencyAnalyzer::new()));
        language_analyzers.insert("javascript".to_string(), Box::new(JavaScriptDependencyAnalyzer::new()));
        language_analyzers.insert("python".to_string(), Box::new(PythonDependencyAnalyzer::new()));
        
        Self {
            language_analyzers,
        }
    }

    /// 分析代码依赖关系
    pub fn analyze(&self, ast: &SyntaxTree, symbols: &[SymbolInfo]) -> Result<DependencyGraph> {
        let language = &ast.language;
        
        let dependencies = if let Some(analyzer) = self.language_analyzers.get(language) {
            analyzer.analyze_dependencies(ast, symbols)?
        } else {
            // 如果没有特定语言的分析器，使用通用分析
            self.generic_analyze(ast, symbols)?
        };

        // 构建依赖图
        let mut symbol_dependencies: HashMap<String, Vec<Dependency>> = HashMap::new();
        let mut reverse_dependencies: HashMap<String, Vec<String>> = HashMap::new();

        for dep in &dependencies {
            // 正向依赖
            symbol_dependencies
                .entry(dep.source_symbol.clone())
                .or_insert_with(Vec::new)
                .push(dep.clone());

            // 反向依赖
            reverse_dependencies
                .entry(dep.target_symbol.clone())
                .or_insert_with(Vec::new)
                .push(dep.source_symbol.clone());
        }

        Ok(DependencyGraph {
            dependencies,
            symbol_dependencies,
            reverse_dependencies,
        })
    }

    /// 通用依赖分析（当没有特定语言分析器时使用）
    fn generic_analyze(&self, ast: &SyntaxTree, symbols: &[SymbolInfo]) -> Result<Vec<Dependency>> {
        let mut dependencies = Vec::new();
        
        // 基于文本模式的简单分析
        for symbol in symbols {
            // 查找可能的函数调用
            let calls = self.find_function_calls_in_text(&ast.source_code, symbol);
            dependencies.extend(calls);
        }

        Ok(dependencies)
    }

    /// 在文本中查找函数调用
    fn find_function_calls_in_text(&self, content: &str, symbol: &SymbolInfo) -> Vec<Dependency> {
        let mut dependencies = Vec::new();
        
        // 简化的函数调用检测
        for (line_num, line) in content.lines().enumerate() {
            if line_num + 1 >= symbol.start_line && line_num + 1 <= symbol.end_line {
                // 在符号范围内查找函数调用模式
                let calls = self.extract_calls_from_line(line, line_num + 1);
                for call in calls {
                    dependencies.push(Dependency {
                        dependency_type: DependencyType::FunctionCall,
                        source_symbol: symbol.name.clone(),
                        target_symbol: call,
                        target_file: None,
                        strength: 0.5,
                        location: line_num + 1,
                    });
                }
            }
        }

        dependencies
    }

    /// 从代码行中提取函数调用
    fn extract_calls_from_line(&self, line: &str, _line_num: usize) -> Vec<String> {
        let mut calls = Vec::new();
        
        // 简单的函数调用模式匹配
        let words: Vec<&str> = line.split_whitespace().collect();
        for word in words {
            if word.contains('(') && !word.starts_with('(') {
                let func_name = word.split('(').next().unwrap_or("");
                if !func_name.is_empty() && func_name.chars().all(|c| c.is_alphanumeric() || c == '_') {
                    calls.push(func_name.to_string());
                }
            }
        }

        calls
    }
}

impl DependencyGraph {
    /// 获取符号的直接依赖
    pub fn get_dependencies(&self, symbol: &str) -> Vec<&Dependency> {
        self.symbol_dependencies
            .get(symbol)
            .map(|deps| deps.iter().collect())
            .unwrap_or_default()
    }

    /// 获取依赖于指定符号的其他符号
    pub fn get_dependents(&self, symbol: &str) -> Vec<&String> {
        self.reverse_dependencies
            .get(symbol)
            .map(|deps| deps.iter().collect())
            .unwrap_or_default()
    }

    /// 检查是否存在循环依赖
    pub fn has_circular_dependency(&self) -> bool {
        let mut visited = HashSet::new();
        let mut rec_stack = HashSet::new();

        for symbol in self.symbol_dependencies.keys() {
            if self.has_cycle_util(symbol, &mut visited, &mut rec_stack) {
                return true;
            }
        }

        false
    }

    /// 循环依赖检测的辅助函数
    fn has_cycle_util(
        &self,
        symbol: &str,
        visited: &mut HashSet<String>,
        rec_stack: &mut HashSet<String>,
    ) -> bool {
        if rec_stack.contains(symbol) {
            return true;
        }

        if visited.contains(symbol) {
            return false;
        }

        visited.insert(symbol.to_string());
        rec_stack.insert(symbol.to_string());

        if let Some(dependencies) = self.symbol_dependencies.get(symbol) {
            for dep in dependencies {
                if self.has_cycle_util(&dep.target_symbol, visited, rec_stack) {
                    return true;
                }
            }
        }

        rec_stack.remove(symbol);
        false
    }
}

/// Rust 语言依赖分析器
pub struct RustDependencyAnalyzer;

impl RustDependencyAnalyzer {
    pub fn new() -> Self {
        Self
    }
}

impl LanguageDependencyAnalyzer for RustDependencyAnalyzer {
    fn analyze_dependencies(&self, ast: &SyntaxTree, symbols: &[SymbolInfo]) -> Result<Vec<Dependency>> {
        let mut dependencies = Vec::new();
        
        // 提取各种类型的依赖
        dependencies.extend(self.extract_imports(ast)?);
        dependencies.extend(self.extract_function_calls(ast, symbols)?);
        dependencies.extend(self.extract_type_references(ast)?);
        
        Ok(dependencies)
    }

    fn extract_imports(&self, ast: &SyntaxTree) -> Result<Vec<Dependency>> {
        let mut imports = Vec::new();
        self.extract_imports_from_node(&ast.root, &mut imports);
        Ok(imports)
    }

    fn extract_function_calls(&self, ast: &SyntaxTree, symbols: &[SymbolInfo]) -> Result<Vec<Dependency>> {
        let mut calls = Vec::new();
        
        for symbol in symbols {
            self.extract_calls_from_symbol_range(ast, symbol, &mut calls);
        }
        
        Ok(calls)
    }

    fn extract_type_references(&self, ast: &SyntaxTree) -> Result<Vec<Dependency>> {
        let mut references = Vec::new();
        self.extract_type_refs_from_node(&ast.root, &mut references);
        Ok(references)
    }
}

impl RustDependencyAnalyzer {
    /// 从 AST 节点中提取导入语句
    fn extract_imports_from_node(&self, node: &AstNode, imports: &mut Vec<Dependency>) {
        if node.node_type == "use_declaration" {
            if let Some(import_path) = self.extract_use_path(node) {
                imports.push(Dependency {
                    dependency_type: DependencyType::Import,
                    source_symbol: "module".to_string(),
                    target_symbol: import_path,
                    target_file: None,
                    strength: 1.0,
                    location: node.start_line,
                });
            }
        }

        // 递归处理子节点
        for child in &node.children {
            self.extract_imports_from_node(child, imports);
        }
    }

    /// 提取 use 语句的路径
    fn extract_use_path(&self, node: &AstNode) -> Option<String> {
        // 简化实现：从节点文本中提取路径
        let text = node.text.trim();
        if text.starts_with("use ") {
            let path = text.strip_prefix("use ")?.trim_end_matches(';').trim();
            Some(path.to_string())
        } else {
            None
        }
    }

    /// 从符号范围内提取函数调用
    fn extract_calls_from_symbol_range(&self, ast: &SyntaxTree, symbol: &SymbolInfo, calls: &mut Vec<Dependency>) {
        let lines: Vec<&str> = ast.source_code.lines().collect();
        
        for line_num in symbol.start_line..=symbol.end_line {
            if let Some(line) = lines.get(line_num - 1) {
                let found_calls = self.find_function_calls_in_line(line);
                for call in found_calls {
                    calls.push(Dependency {
                        dependency_type: DependencyType::FunctionCall,
                        source_symbol: symbol.name.clone(),
                        target_symbol: call,
                        target_file: None,
                        strength: 0.7,
                        location: line_num,
                    });
                }
            }
        }
    }

    /// 在代码行中查找函数调用
    fn find_function_calls_in_line(&self, line: &str) -> Vec<String> {
        let mut calls = Vec::new();
        
        // Rust 函数调用模式
        let patterns = [
            r"(\w+)\s*\(",  // 简单函数调用
            r"\.(\w+)\s*\(",  // 方法调用
        ];
        
        for _pattern in &patterns {
            // 这里应该使用正则表达式，但为了简化，使用字符串匹配
            let words: Vec<&str> = line.split_whitespace().collect();
            for word in words {
                if word.contains('(') && !word.starts_with('(') {
                    let func_name = word.split('(').next().unwrap_or("");
                    if !func_name.is_empty() && func_name.chars().all(|c| c.is_alphanumeric() || c == '_') {
                        calls.push(func_name.to_string());
                    }
                }
            }
        }

        calls
    }

    /// 从 AST 节点中提取类型引用
    fn extract_type_refs_from_node(&self, node: &AstNode, references: &mut Vec<Dependency>) {
        if node.node_type == "type_identifier" {
            references.push(Dependency {
                dependency_type: DependencyType::TypeReference,
                source_symbol: "unknown".to_string(),
                target_symbol: node.text.clone(),
                target_file: None,
                strength: 0.6,
                location: node.start_line,
            });
        }

        // 递归处理子节点
        for child in &node.children {
            self.extract_type_refs_from_node(child, references);
        }
    }
}

/// JavaScript 语言依赖分析器（占位符实现）
pub struct JavaScriptDependencyAnalyzer;

impl JavaScriptDependencyAnalyzer {
    pub fn new() -> Self {
        Self
    }
}

impl LanguageDependencyAnalyzer for JavaScriptDependencyAnalyzer {
    fn analyze_dependencies(&self, _ast: &SyntaxTree, _symbols: &[SymbolInfo]) -> Result<Vec<Dependency>> {
        // TODO: 实现 JavaScript 依赖分析
        Ok(Vec::new())
    }

    fn extract_imports(&self, _ast: &SyntaxTree) -> Result<Vec<Dependency>> {
        // TODO: 实现 JavaScript import/require 分析
        Ok(Vec::new())
    }

    fn extract_function_calls(&self, _ast: &SyntaxTree, _symbols: &[SymbolInfo]) -> Result<Vec<Dependency>> {
        // TODO: 实现 JavaScript 函数调用分析
        Ok(Vec::new())
    }

    fn extract_type_references(&self, _ast: &SyntaxTree) -> Result<Vec<Dependency>> {
        // TODO: 实现 JavaScript 类型引用分析
        Ok(Vec::new())
    }
}

/// Python 语言依赖分析器（占位符实现）
pub struct PythonDependencyAnalyzer;

impl PythonDependencyAnalyzer {
    pub fn new() -> Self {
        Self
    }
}

impl LanguageDependencyAnalyzer for PythonDependencyAnalyzer {
    fn analyze_dependencies(&self, _ast: &SyntaxTree, _symbols: &[SymbolInfo]) -> Result<Vec<Dependency>> {
        // TODO: 实现 Python 依赖分析
        Ok(Vec::new())
    }

    fn extract_imports(&self, _ast: &SyntaxTree) -> Result<Vec<Dependency>> {
        // TODO: 实现 Python import 分析
        Ok(Vec::new())
    }

    fn extract_function_calls(&self, _ast: &SyntaxTree, _symbols: &[SymbolInfo]) -> Result<Vec<Dependency>> {
        // TODO: 实现 Python 函数调用分析
        Ok(Vec::new())
    }

    fn extract_type_references(&self, _ast: &SyntaxTree) -> Result<Vec<Dependency>> {
        // TODO: 实现 Python 类型引用分析
        Ok(Vec::new())
    }
}
