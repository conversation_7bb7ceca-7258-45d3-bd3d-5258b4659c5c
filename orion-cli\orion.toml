# Orion Agent 配置文件
# 更多信息请参考: https://github.com/your-org/orion

[general]
# 日志级别: trace, debug, info, warn, error
log_level = "info"
# 工作目录
work_dir = "./orion-workspace"
# 输出格式: table, json, yaml
output_format = "table"
# 是否启用颜色输出
enable_color = true
# 是否启用性能分析
enable_profiling = false

[api]
# API 基础 URL
base_url = "https://api.orion-ai.com"
# API 密钥（可选）
# api_key = "your-api-key"
# 请求超时时间（秒）
timeout = 30
# 最大重试次数
max_retries = 3
# 重试间隔（秒）
retry_interval = 1

[agents]
# 默认模型
default_model = "gpt-4"
# 最大并发 Agent 数量
max_concurrent = 5
# 是否自动启动
auto_start = false
# 默认超时时间（秒）
default_timeout = 300
# 最大上下文长度
max_context_length = 8192

[tools]
# 工具注册表 URL
registry_url = "https://tools.orion-ai.com"
# 是否自动更新
auto_update = true
# 缓存目录
cache_dir = ".cache/orion/tools"
# 工具执行超时时间（秒）
execution_timeout = 60
# 最大并发工具执行数
max_concurrent_executions = 10

[workflows]
# 模板目录
template_dir = ".config/orion/templates"
# 最大执行时间（秒）
max_execution_time = 3600
# 是否自动保存
auto_save = true
# 最大并发工作流数
max_concurrent_workflows = 3

[logging]
# 日志级别: trace, debug, info, warn, error
level = "info"
# 日志格式
format = "pretty"
# 是否启用控制台输出
enable_console = true
# 是否启用文件输出
enable_file = false
# 日志轮转大小（MB）
rotation_size = 100
# 保留日志文件数量
retention_count = 10

[security]
# 是否启用沙箱
enable_sandbox = true
# 允许的域名列表
allowed_domains = ["api.openai.com", "api.anthropic.com"]
# 禁止的命令列表
blocked_commands = ["rm", "del", "format"]

[security.sandbox]
# 内存限制（MB）
memory_limit = 512
# CPU 限制（百分比）
cpu_limit = 50
# 执行超时时间（秒）
timeout = 300
# 临时目录
temp_dir = "/tmp/orion-sandbox"

