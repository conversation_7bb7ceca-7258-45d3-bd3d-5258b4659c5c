//! # 上下文构建器
//!
//! 构建和组织上下文信息。

use crate::{
    config::ContextConfig,
    context::ContextFragment,
    knowledge_base::SearchResult,
};

/// 上下文构建器
pub struct ContextBuilder {
    config: ContextConfig,
}

impl ContextBuilder {
    /// 创建新的上下文构建器
    pub fn new(config: &ContextConfig) -> Self {
        Self {
            config: config.clone(),
        }
    }

    /// 从搜索结果构建上下文片段
    pub fn build_context_fragments(
        &self,
        results: &[SearchResult],
    ) -> Vec<ContextFragment> {
        let mut fragments = Vec::new();
        
        for result in results {
            let fragment = self.build_single_fragment(result);
            fragments.push(fragment);
        }
        
        // 按重要性排序
        fragments.sort_by(|a, b| {
            b.importance.partial_cmp(&a.importance).unwrap_or(std::cmp::Ordering::Equal)
        });
        
        fragments
    }

    /// 构建单个上下文片段
    fn build_single_fragment(&self, result: &SearchResult) -> ContextFragment {
        let formatted_content = self.format_code_with_context(result);
        let importance = self.calculate_importance(result);
        let token_count = self.estimate_token_count(&formatted_content);
        
        ContextFragment {
            content: formatted_content,
            importance,
            source: crate::context::FragmentSource {
                chunk_id: result.chunk_id.clone(),
                file_path: result.file_path.clone(),
                symbol_name: result.symbol_name.clone(),
                line_range: (result.start_line, result.end_line),
            },
            token_count,
        }
    }

    /// 格式化代码并添加上下文信息
    fn format_code_with_context(&self, result: &SearchResult) -> String {
        let mut formatted = String::new();
        
        // 添加文件路径和位置信息
        formatted.push_str(&format!(
            "// File: {} (lines {}-{})\n",
            result.file_path,
            result.start_line,
            result.end_line
        ));
        
        // 添加符号信息
        if let Some(ref symbol_name) = result.symbol_name {
            formatted.push_str(&format!("// Symbol: {}\n", symbol_name));
        }
        
        // 添加语言标识
        formatted.push_str(&format!("// Language: {}\n", result.language));
        
        // 添加相似度分数
        formatted.push_str(&format!("// Relevance: {:.2}\n", result.score));
        
        formatted.push_str("//\n");
        
        // 添加实际代码内容
        formatted.push_str(&result.content);
        
        formatted
    }

    /// 计算片段重要性
    fn calculate_importance(&self, result: &SearchResult) -> f32 {
        let mut importance = result.score;
        
        // 根据符号类型调整重要性
        if result.symbol_name.is_some() {
            importance += 0.15; // 有符号名称的代码块更重要
        }
        
        // 根据代码长度调整重要性
        let line_count = result.end_line - result.start_line + 1;
        if line_count <= 5 {
            importance += 0.1; // 短代码块可能更精确
        } else if line_count <= 20 {
            importance += 0.05;
        } else if line_count > 100 {
            importance -= 0.1; // 长代码块可能包含太多无关信息
        }
        
        // 根据语言调整重要性（可以根据需要定制）
        match result.language.as_str() {
            "rust" | "javascript" | "typescript" | "python" => {
                importance += 0.05; // 主流语言
            }
            _ => {}
        }
        
        importance.clamp(0.0, 1.0)
    }

    /// 估算 token 数量
    fn estimate_token_count(&self, text: &str) -> usize {
        // 简化的 token 计数：大约每 4 个字符一个 token
        let char_count = text.chars().count();
        let word_count = text.split_whitespace().count();

        // 根据配置调整 token 计数策略
        let base_count = (char_count / 4).max(word_count);

        // 如果配置了更精确的计数，可以在这里使用 self.config
        if self.config.enable_compression {
            // 压缩模式下可能需要更保守的估计
            (base_count as f32 * 1.2) as usize
        } else {
            base_count
        }
    }

    /// 构建层次化上下文
    pub fn build_hierarchical_context(
        &self,
        fragments: &[ContextFragment],
    ) -> String {
        if fragments.is_empty() {
            return String::new();
        }

        let mut context = String::new();
        context.push_str("=== Code Context Analysis ===\n\n");
        
        // 按文件分组
        let mut file_groups = std::collections::HashMap::new();
        for fragment in fragments {
            file_groups
                .entry(fragment.source.file_path.clone())
                .or_insert_with(Vec::new)
                .push(fragment);
        }
        
        // 为每个文件生成上下文
        for (file_path, file_fragments) in file_groups {
            context.push_str(&format!("## File: {}\n\n", file_path));
            
            for (i, fragment) in file_fragments.iter().enumerate() {
                context.push_str(&format!(
                    "### Context {} (Importance: {:.2})\n",
                    i + 1,
                    fragment.importance
                ));
                context.push_str(&fragment.content);
                context.push_str("\n\n");
            }
        }
        
        context.push_str("=== End of Context ===\n");
        context
    }

    /// 构建紧凑上下文
    pub fn build_compact_context(
        &self,
        fragments: &[ContextFragment],
        max_tokens: usize,
    ) -> String {
        let mut context = String::new();
        let mut current_tokens = 0;
        
        context.push_str("=== Relevant Code ===\n\n");
        
        for (i, fragment) in fragments.iter().enumerate() {
            if current_tokens + fragment.token_count > max_tokens {
                break;
            }
            
            // 简化的格式
            context.push_str(&format!("// #{} {}\n", i + 1, fragment.source.file_path));
            
            // 压缩代码内容
            let compressed_content = self.compress_content(&fragment.content, max_tokens - current_tokens);
            context.push_str(&compressed_content);
            context.push_str("\n\n");
            
            current_tokens += self.estimate_token_count(&compressed_content);
        }
        
        context.push_str("=== End ===\n");
        context
    }

    /// 压缩内容以适应 token 限制
    fn compress_content(&self, content: &str, available_tokens: usize) -> String {
        let lines: Vec<&str> = content.lines().collect();
        
        if self.estimate_token_count(content) <= available_tokens {
            return content.to_string();
        }
        
        // 保留重要行
        let important_lines = self.select_important_lines(&lines, available_tokens);
        
        if important_lines.len() < lines.len() {
            let mut compressed = important_lines.join("\n");
            compressed.push_str("\n// ... (content truncated) ...");
            compressed
        } else {
            content.to_string()
        }
    }

    /// 选择重要的代码行
    fn select_important_lines<'a>(&self, lines: &[&'a str], available_tokens: usize) -> Vec<&'a str> {
        let mut important_lines = Vec::new();
        let mut current_tokens = 0;
        let _token_per_line = available_tokens / lines.len().max(1);
        
        // 优先保留的行类型
        let priority_patterns = [
            "fn ", "function ", "def ", "class ", "struct ", "enum ", "interface ",
            "impl ", "trait ", "type ", "const ", "let ", "var ",
            "import ", "use ", "from ", "include ",
            "// ", "/* ", "/**", "///",
        ];
        
        // 第一轮：保留高优先级行
        for line in lines {
            let line_tokens = self.estimate_token_count(line);
            if current_tokens + line_tokens > available_tokens {
                break;
            }
            
            let trimmed = line.trim();
            if priority_patterns.iter().any(|pattern| trimmed.starts_with(pattern)) {
                important_lines.push(*line);
                current_tokens += line_tokens;
            }
        }
        
        // 第二轮：填充剩余空间
        for line in lines {
            let line_tokens = self.estimate_token_count(line);
            if current_tokens + line_tokens > available_tokens {
                break;
            }
            
            if !important_lines.contains(line) {
                important_lines.push(*line);
                current_tokens += line_tokens;
            }
        }
        
        important_lines
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::knowledge_base::SearchResult;

    fn create_test_result() -> SearchResult {
        SearchResult {
            chunk_id: "test_1".to_string(),
            file_path: "src/main.rs".to_string(),
            content: "fn main() {\n    println!(\"Hello, world!\");\n}".to_string(),
            score: 0.8,
            language: "rust".to_string(),
            symbol_name: Some("main".to_string()),
            start_line: 1,
            end_line: 3,
        }
    }

    #[test]
    fn test_context_fragment_building() {
        let config = ContextConfig::default();
        let builder = ContextBuilder::new(&config);
        
        let results = vec![create_test_result()];
        let fragments = builder.build_context_fragments(&results);
        
        assert_eq!(fragments.len(), 1);
        assert!(fragments[0].importance > 0.0);
        assert!(fragments[0].token_count > 0);
    }

    #[test]
    fn test_importance_calculation() {
        let config = ContextConfig::default();
        let builder = ContextBuilder::new(&config);
        
        let result = create_test_result();
        let importance = builder.calculate_importance(&result);
        
        assert!(importance > 0.0);
        assert!(importance <= 1.0);
    }

    #[test]
    fn test_token_estimation() {
        let config = ContextConfig::default();
        let builder = ContextBuilder::new(&config);
        
        let text = "fn main() { println!(\"Hello\"); }";
        let token_count = builder.estimate_token_count(text);
        
        assert!(token_count > 0);
    }

    #[test]
    fn test_hierarchical_context_building() {
        let config = ContextConfig::default();
        let builder = ContextBuilder::new(&config);
        
        let results = vec![create_test_result()];
        let fragments = builder.build_context_fragments(&results);
        let context = builder.build_hierarchical_context(&fragments);
        
        assert!(!context.is_empty());
        assert!(context.contains("Code Context Analysis"));
        assert!(context.contains("src/main.rs"));
    }
}