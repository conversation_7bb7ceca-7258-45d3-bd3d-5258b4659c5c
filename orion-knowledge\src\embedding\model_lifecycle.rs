//! # 嵌入模型生命周期管理
//!
//! 管理嵌入模型的完整生命周期，包括初始化、健康检查、更新、销毁等。

use crate::{
    error::Result,
    embedding::{
        local_embedder::LocalEmbedder,
        remote_embedder::{OpenAIEmbedder, CohereEmbedder, OllamaEmbedder},
        performance::PerformanceEmbedder,
        manager::EmbedderType,
        model_registry::{ModelRegistry, ModelStatus, ModelConfig, ModelSelectionCriteria},
    },
    KnowledgeError,
};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::{RwLock, Mutex};
use tokio::time::interval;
use tracing::{debug, info, warn, error};

/// 模型实例包装器
pub enum ModelInstance {
    Local(PerformanceEmbedder<LocalEmbedder>),
    OpenAI(PerformanceEmbedder<OpenAIEmbedder>),
    Cohere(PerformanceEmbedder<CohereEmbedder>),
    <PERSON>llama(PerformanceEmbedder<OllamaEmbedder>),
}

impl ModelInstance {
    /// 获取模型信息
    pub fn model_info(&self) -> String {
        match self {
            ModelInstance::Local(embedder) => embedder.provider_model_info().name.clone(),
            ModelInstance::OpenAI(embedder) => embedder.provider_model_info().name.clone(),
            ModelInstance::Cohere(embedder) => embedder.provider_model_info().name.clone(),
            ModelInstance::Ollama(embedder) => embedder.provider_model_info().name.clone(),
        }
    }
    
    /// 获取模型维度
    pub fn dimension(&self) -> usize {
        match self {
            ModelInstance::Local(embedder) => embedder.provider_dimension(),
            ModelInstance::OpenAI(embedder) => embedder.provider_dimension(),
            ModelInstance::Cohere(embedder) => embedder.provider_dimension(),
            ModelInstance::Ollama(embedder) => embedder.provider_dimension(),
        }
    }
    
    /// 执行健康检查
    pub async fn health_check(&self) -> Result<Duration> {
        let start_time = Instant::now();
        
        let result = match self {
            ModelInstance::Local(embedder) => {
                embedder.embed_text_optimized("health check").await
            }
            ModelInstance::OpenAI(embedder) => {
                embedder.embed_text_optimized("health check").await
            }
            ModelInstance::Cohere(embedder) => {
                embedder.embed_text_optimized("health check").await
            }
            ModelInstance::Ollama(embedder) => {
                embedder.embed_text_optimized("health check").await
            }
        };
        
        let duration = start_time.elapsed();
        
        match result {
            Ok(_) => Ok(duration),
            Err(e) => Err(e),
        }
    }
}

/// 模型生命周期管理器
pub struct ModelLifecycleManager {
    /// 模型注册表
    registry: Arc<ModelRegistry>,
    /// 活跃的模型实例
    active_models: Arc<RwLock<HashMap<String, ModelInstance>>>,
    /// 模型初始化锁
    initialization_locks: Arc<Mutex<HashMap<String, Arc<Mutex<()>>>>>,
    /// 健康检查间隔
    health_check_interval: Duration,
    /// 是否启用自动健康检查
    auto_health_check: bool,
}

impl ModelLifecycleManager {
    /// 创建新的模型生命周期管理器
    pub fn new(registry: Arc<ModelRegistry>) -> Self {
        info!("初始化模型生命周期管理器");
        
        Self {
            registry,
            active_models: Arc::new(RwLock::new(HashMap::new())),
            initialization_locks: Arc::new(Mutex::new(HashMap::new())),
            health_check_interval: Duration::from_secs(300), // 5分钟
            auto_health_check: true,
        }
    }
    
    /// 启动生命周期管理器
    pub async fn start(&self) -> Result<()> {
        info!("启动模型生命周期管理器");
        
        // 启动健康检查任务
        if self.auto_health_check {
            self.start_health_check_task().await;
        }
        
        // 初始化所有注册的模型
        self.initialize_all_models().await?;
        
        Ok(())
    }
    
    /// 初始化模型
    pub async fn initialize_model(&self, model_id: &str) -> Result<()> {
        // 获取初始化锁
        let lock = {
            let mut locks = self.initialization_locks.lock().await;
            locks.entry(model_id.to_string())
                .or_insert_with(|| Arc::new(Mutex::new(())))
                .clone()
        };
        
        let _guard = lock.lock().await;
        
        // 检查模型是否已经初始化
        {
            let active_models = self.active_models.read().await;
            if active_models.contains_key(model_id) {
                debug!("模型 {} 已经初始化", model_id);
                return Ok(());
            }
        }
        
        info!("开始初始化模型: {}", model_id);
        
        // 更新状态为初始化中
        self.registry.update_model_status(model_id, ModelStatus::Initializing).await?;
        
        // 获取模型配置
        let config = self.registry.get_model_config(model_id).await
            .ok_or_else(|| KnowledgeError::embedding_error(&format!("模型配置不存在: {}", model_id)))?;
        
        // 创建模型实例
        let start_time = Instant::now();
        let model_instance = match self.create_model_instance(&config).await {
            Ok(instance) => instance,
            Err(e) => {
                error!("模型初始化失败: {} - {}", model_id, e);
                self.registry.update_model_status(model_id, ModelStatus::Error(e.to_string())).await?;
                return Err(e);
            }
        };
        
        let initialization_time = start_time.elapsed();
        info!("模型 {} 初始化完成，耗时: {:?}", model_id, initialization_time);
        
        // 执行健康检查
        match model_instance.health_check().await {
            Ok(health_check_time) => {
                info!("模型 {} 健康检查通过，耗时: {:?}", model_id, health_check_time);
                
                // 添加到活跃模型列表
                {
                    let mut active_models = self.active_models.write().await;
                    active_models.insert(model_id.to_string(), model_instance);
                }
                
                // 更新状态为可用
                self.registry.update_model_status(model_id, ModelStatus::Available).await?;
                self.registry.update_health_check(model_id).await?;
                
                // 记录初始化指标
                self.registry.update_model_metrics(model_id, initialization_time.as_millis() as f64, true).await?;
            }
            Err(e) => {
                error!("模型 {} 健康检查失败: {}", model_id, e);
                self.registry.update_model_status(model_id, ModelStatus::Error(e.to_string())).await?;
                return Err(e);
            }
        }
        
        Ok(())
    }
    
    /// 销毁模型
    pub async fn destroy_model(&self, model_id: &str) -> Result<()> {
        info!("销毁模型: {}", model_id);
        
        // 从活跃模型列表中移除
        {
            let mut active_models = self.active_models.write().await;
            active_models.remove(model_id);
        }
        
        // 更新状态为不可用
        self.registry.update_model_status(model_id, ModelStatus::Unavailable).await?;
        
        info!("模型 {} 已销毁", model_id);
        Ok(())
    }
    
    /// 重启模型
    pub async fn restart_model(&self, model_id: &str) -> Result<()> {
        info!("重启模型: {}", model_id);
        
        // 先销毁
        self.destroy_model(model_id).await?;
        
        // 再初始化
        self.initialize_model(model_id).await?;
        
        info!("模型 {} 重启完成", model_id);
        Ok(())
    }
    
    /// 获取模型实例
    pub async fn get_model_instance(&self, _model_id: &str) -> Option<ModelInstance> {
        let _active_models = self.active_models.read().await;
        // 这里需要克隆，但 ModelInstance 没有实现 Clone
        // 在实际使用中，我们会返回引用或使用 Arc
        None // 临时返回 None，实际实现需要调整
    }
    
    /// 列出活跃的模型
    pub async fn list_active_models(&self) -> Vec<String> {
        let active_models = self.active_models.read().await;
        active_models.keys().cloned().collect()
    }
    
    /// 获取最佳可用模型
    pub async fn get_best_available_model(&self, criteria: &ModelSelectionCriteria) -> Option<String> {
        self.registry.get_best_model(criteria).await
    }
    
    /// 执行所有模型的健康检查
    pub async fn health_check_all(&self) -> Result<HashMap<String, Result<Duration>>> {
        let active_models = self.active_models.read().await;
        let mut results = HashMap::new();
        
        for (model_id, model_instance) in active_models.iter() {
            let start_time = Instant::now();
            let health_result = model_instance.health_check().await;
            let check_duration = start_time.elapsed();
            
            match &health_result {
                Ok(response_time) => {
                    info!("模型 {} 健康检查通过，响应时间: {:?}", model_id, response_time);
                    self.registry.update_model_status(model_id, ModelStatus::Available).await?;
                    self.registry.update_model_metrics(model_id, response_time.as_millis() as f64, true).await?;
                }
                Err(e) => {
                    warn!("模型 {} 健康检查失败: {}", model_id, e);
                    self.registry.update_model_status(model_id, ModelStatus::Error(e.to_string())).await?;
                    self.registry.update_model_metrics(model_id, check_duration.as_millis() as f64, false).await?;
                }
            }
            
            self.registry.update_health_check(model_id).await?;
            results.insert(model_id.clone(), health_result);
        }
        
        Ok(results)
    }
    
    /// 初始化所有注册的模型
    async fn initialize_all_models(&self) -> Result<()> {
        let model_ids = self.registry.list_models().await;
        
        for model_id in model_ids {
            if let Err(e) = self.initialize_model(&model_id).await {
                warn!("模型 {} 初始化失败: {}", model_id, e);
            }
        }
        
        Ok(())
    }
    
    /// 创建模型实例
    async fn create_model_instance(&self, config: &ModelConfig) -> Result<ModelInstance> {
        match config.model_type {
            EmbedderType::Local => {
                let provider = LocalEmbedder::new(&config.embedding_config).await?;
                let performance_embedder = PerformanceEmbedder::new(provider, config.performance_config.clone());
                Ok(ModelInstance::Local(performance_embedder))
            }
            EmbedderType::OpenAI => {
                let provider = OpenAIEmbedder::new(&config.embedding_config).await?;
                let performance_embedder = PerformanceEmbedder::new(provider, config.performance_config.clone());
                Ok(ModelInstance::OpenAI(performance_embedder))
            }
            EmbedderType::Cohere => {
                let provider = CohereEmbedder::new(&config.embedding_config).await?;
                let performance_embedder = PerformanceEmbedder::new(provider, config.performance_config.clone());
                Ok(ModelInstance::Cohere(performance_embedder))
            }
            EmbedderType::Ollama => {
                let provider = OllamaEmbedder::new(&config.embedding_config).await?;
                let performance_embedder = PerformanceEmbedder::new(provider, config.performance_config.clone());
                Ok(ModelInstance::Ollama(performance_embedder))
            }
        }
    }
    
    /// 启动健康检查任务
    async fn start_health_check_task(&self) {
        let registry = self.registry.clone();
        let active_models = self.active_models.clone();
        let health_check_interval = self.health_check_interval;

        tokio::spawn(async move {
            let mut interval = interval(health_check_interval);

            loop {
                interval.tick().await;

                debug!("执行定期健康检查");

                // 获取需要健康检查的模型
                let models_to_check = registry.get_models_needing_health_check().await;

                for model_id in models_to_check {
                    // 简化的健康检查实现
                    let models = active_models.read().await;
                    if models.contains_key(&model_id) {
                        // 模拟健康检查
                        let _ = registry.update_model_status(&model_id, ModelStatus::Available).await;
                        let _ = registry.update_health_check(&model_id).await;
                    }
                }
            }
        });
    }
    
    /// 执行单个模型的健康检查
    #[allow(dead_code)]
    async fn perform_health_check(&self, model_id: &str) -> Result<()> {
        let active_models = self.active_models.read().await;
        
        if let Some(model_instance) = active_models.get(model_id) {
            match model_instance.health_check().await {
                Ok(response_time) => {
                    self.registry.update_model_status(model_id, ModelStatus::Available).await?;
                    self.registry.update_model_metrics(model_id, response_time.as_millis() as f64, true).await?;
                }
                Err(e) => {
                    self.registry.update_model_status(model_id, ModelStatus::Error(e.to_string())).await?;
                }
            }
            
            self.registry.update_health_check(model_id).await?;
        }
        
        Ok(())
    }
}
