//! # 嵌入引擎模块
//!
//! 提供代码嵌入向量计算和管理功能。

pub mod local_embedder;
pub mod remote_embedder;
pub mod performance;
pub mod manager;
pub mod memory_pool;
pub mod embedding_cache;
pub mod model_registry;
pub mod model_lifecycle;
pub mod load_balancer;
pub mod model_manager;

#[cfg(test)]
pub mod integration_tests;

#[cfg(test)]
pub mod performance_tests;

use crate::{
    config::EmbeddingConfig,
    parser::CodeChunk,
    error::Result,
};
use serde::{Deserialize, Serialize};

/// 嵌入向量
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Embedding {
    /// 向量数据
    pub vector: Vec<f32>,
    /// 向量维度
    pub dimension: usize,
    /// 嵌入模型信息
    pub model_info: ModelInfo,
    /// 创建时间
    pub created_at: chrono::DateTime<chrono::Utc>,
}

/// 模型信息
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ModelInfo {
    /// 模型名称
    pub name: String,
    /// 模型版本
    pub version: String,
    /// 模型类型
    pub model_type: String,
}

/// 批量嵌入结果
#[derive(Debug)]
pub struct BatchEmbeddingResult {
    /// 嵌入向量
    pub embeddings: Vec<Embedding>,
    /// 处理耗时（毫秒）
    pub duration_ms: u64,
    /// 成功数量
    pub success_count: usize,
    /// 失败数量
    pub error_count: usize,
}

/// 嵌入引擎
pub struct EmbeddingEngine {
    config: EmbeddingConfig,
    embedder: Box<dyn EmbeddingProvider>,
    cache: embedding_cache::EmbeddingCache,
}

/// 嵌入提供者特征
#[async_trait::async_trait]
pub trait EmbeddingProvider: Send + Sync {
    /// 计算单个文本的嵌入向量
    async fn embed_text(&self, text: &str) -> Result<Embedding>;
    
    /// 批量计算嵌入向量
    async fn embed_batch(&self, texts: &[String]) -> Result<BatchEmbeddingResult>;
    
    /// 获取模型信息
    fn model_info(&self) -> &ModelInfo;
    
    /// 获取向量维度
    fn dimension(&self) -> usize;
}

impl EmbeddingEngine {
    /// 创建新的嵌入引擎
    pub async fn new(config: &EmbeddingConfig) -> Result<Self> {
        let embedder: Box<dyn EmbeddingProvider> = match config.model_type {
            crate::config::EmbeddingModelType::Local => {
                Box::new(local_embedder::LocalEmbedder::new(config).await?)
            }
            crate::config::EmbeddingModelType::OpenAI => {
                Box::new(remote_embedder::OpenAIEmbedder::new(config).await?)
            }
            crate::config::EmbeddingModelType::Ollama => {
                Box::new(remote_embedder::OllamaEmbedder::new(config).await?)
            }
            crate::config::EmbeddingModelType::Custom => {
                Box::new(remote_embedder::CustomEmbedder::new(config).await?)
            }
        };

        let cache = embedding_cache::EmbeddingCache::new()?;

        Ok(Self {
            config: config.clone(),
            embedder,
            cache,
        })
    }

    /// 为代码块计算嵌入向量
    pub async fn embed_chunk(&self, chunk: &CodeChunk) -> Result<Embedding> {
        // 构造用于嵌入的文本
        let embed_text = self.prepare_chunk_text(chunk);
        
        // 检查缓存
        if let Some(cached_embedding) = self.cache.get(&embed_text).await? {
            return Ok(cached_embedding);
        }

        // 计算嵌入向量
        let embedding = self.embedder.embed_text(&embed_text).await?;

        // 缓存结果
        self.cache.put(&embed_text, &embedding).await?;

        Ok(embedding)
    }

    /// 批量计算代码块的嵌入向量
    pub async fn embed_chunks(&self, chunks: &[CodeChunk]) -> Result<Vec<Embedding>> {
        let mut embeddings = Vec::new();
        let mut texts_to_embed = Vec::new();
        let mut indices_to_embed = Vec::new();

        // 准备文本并检查缓存
        for (i, chunk) in chunks.iter().enumerate() {
            let embed_text = self.prepare_chunk_text(chunk);
            
            if let Some(cached_embedding) = self.cache.get(&embed_text).await? {
                embeddings.push(Some(cached_embedding));
            } else {
                embeddings.push(None);
                texts_to_embed.push(embed_text);
                indices_to_embed.push(i);
            }
        }

        // 批量计算未缓存的嵌入向量
        if !texts_to_embed.is_empty() {
            let batch_result = self.embedder.embed_batch(&texts_to_embed).await?;
            
            for (j, embedding) in batch_result.embeddings.into_iter().enumerate() {
                let index = indices_to_embed[j];
                let text = &texts_to_embed[j];
                
                // 缓存结果
                self.cache.put(text, &embedding).await?;
                embeddings[index] = Some(embedding);
            }
        }

        // 提取所有嵌入向量
        let result: Result<Vec<Embedding>> = embeddings
            .into_iter()
            .map(|opt| opt.ok_or_else(|| {
                crate::KnowledgeError::embedding_error("计算嵌入向量失败")
            }))
            .collect();

        result
    }

    /// 为查询计算嵌入向量
    pub async fn embed_query(&self, query: &str) -> Result<Embedding> {
        // 查询通常不缓存，直接计算
        self.embedder.embed_text(query).await
    }

    /// 准备代码块的嵌入文本
    fn prepare_chunk_text(&self, chunk: &CodeChunk) -> String {
        let mut parts = Vec::new();

        // 添加语言信息
        parts.push(format!("Language: {}", chunk.language));

        // 添加类型信息
        parts.push(format!("Type: {:?}", chunk.chunk_type));

        // 添加符号名称
        if let Some(symbol_name) = &chunk.symbol_name {
            parts.push(format!("Symbol: {}", symbol_name));
        }

        // 添加父级符号
        if let Some(parent_symbol) = &chunk.parent_symbol {
            parts.push(format!("Parent: {}", parent_symbol));
        }

        // 添加代码内容
        parts.push(format!("Code:\n{}", chunk.content));

        parts.join("\n")
    }

    /// 获取嵌入维度
    pub fn dimension(&self) -> usize {
        self.embedder.dimension()
    }

    /// 获取模型信息
    pub fn model_info(&self) -> &ModelInfo {
        self.embedder.model_info()
    }

    /// 获取当前配置
    pub fn get_config(&self) -> &EmbeddingConfig {
        &self.config
    }

    /// 获取批处理大小
    pub fn batch_size(&self) -> usize {
        self.config.batch_size
    }

    /// 清理缓存
    pub async fn clear_cache(&mut self) -> Result<()> {
        self.cache.clear().await
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::parser::ChunkType;
    use std::path::PathBuf;

    fn create_test_chunk() -> CodeChunk {
        CodeChunk {
            id: "test_chunk".to_string(),
            file_path: PathBuf::from("test.rs"),
            content: "fn hello() { println!(\"Hello\"); }".to_string(),
            language: "rust".to_string(),
            chunk_type: ChunkType::Function,
            start_line: 1,
            end_line: 1,
            symbol_name: Some("hello".to_string()),
            parent_symbol: None,
            dependencies: Vec::new(),
            metadata: std::collections::HashMap::new(),
            created_at: chrono::Utc::now(),
        }
    }

    #[test]
    fn test_chunk_text_formatting() {
        let chunk = create_test_chunk();

        // 测试代码块的基本属性
        assert_eq!(chunk.language, "rust");
        assert_eq!(chunk.chunk_type, ChunkType::Function);
        assert_eq!(chunk.symbol_name, Some("hello".to_string()));
        assert!(chunk.content.contains("fn hello()"));
    }

    #[test]
    fn test_config_access() {
        let config = EmbeddingConfig::default();
        let batch_size = config.batch_size;

        // 测试配置访问
        assert!(batch_size > 0);
        assert_eq!(config.dimension, 384);
    }
}