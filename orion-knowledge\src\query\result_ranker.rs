//! # 结果排序器
//!
//! 对搜索结果进行排序和重排序。

use crate::{
    config::QueryConfig,
    error::Result,
    knowledge_base::SearchResult,
    query::{ProcessedQuery, ResultWeight},
};
use serde::{Deserialize, Serialize};

/// 排序策略
#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
pub enum RankingStrategy {
    /// 基于相关性分数
    Relevance,
    /// 基于多样性
    Diversity,
    /// 基于新鲜度
    Freshness,
    /// 基于流行度
    Popularity,
    /// 混合排序
    Hybrid,
}

/// 多维度排序配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RankingConfig {
    /// 主要排序策略
    pub primary_strategy: RankingStrategy,
    /// 次要排序策略
    pub secondary_strategy: Option<RankingStrategy>,
    /// 相关性权重
    pub relevance_weight: f32,
    /// 多样性权重
    pub diversity_weight: f32,
    /// 新鲜度权重
    pub freshness_weight: f32,
    /// 流行度权重
    pub popularity_weight: f32,
    /// 语言匹配权重
    pub language_match_weight: f32,
    /// 符号类型匹配权重
    pub symbol_type_weight: f32,
    /// 文件路径匹配权重
    pub file_path_weight: f32,
    /// 多样性阈值
    pub diversity_threshold: f32,
    /// 是否启用个性化排序
    pub enable_personalization: bool,
}

impl Default for RankingConfig {
    fn default() -> Self {
        Self {
            primary_strategy: RankingStrategy::Hybrid,
            secondary_strategy: Some(RankingStrategy::Diversity),
            relevance_weight: 0.4,
            diversity_weight: 0.2,
            freshness_weight: 0.1,
            popularity_weight: 0.1,
            language_match_weight: 0.1,
            symbol_type_weight: 0.05,
            file_path_weight: 0.05,
            diversity_threshold: 0.8,
            enable_personalization: false,
        }
    }
}

/// 个性化排序上下文
#[derive(Debug, Clone, Default)]
pub struct PersonalizationContext {
    /// 用户偏好的编程语言
    pub preferred_languages: Vec<String>,
    /// 用户常用的文件路径模式
    pub frequent_paths: Vec<String>,
    /// 用户偏好的符号类型
    pub preferred_symbol_types: Vec<String>,
    /// 历史查询记录
    pub query_history: Vec<String>,
}

/// 结果排序器
pub struct ResultRanker {
    config: QueryConfig,
    weights: ResultWeight,
    ranking_config: RankingConfig,
    personalization_context: PersonalizationContext,
}

impl ResultRanker {
    /// 创建新的结果排序器
    pub fn new(config: &QueryConfig) -> Self {
        Self {
            config: config.clone(),
            weights: ResultWeight::default(),
            ranking_config: RankingConfig::default(),
            personalization_context: PersonalizationContext::default(),
        }
    }

    /// 使用自定义排序配置创建结果排序器
    pub fn with_ranking_config(config: &QueryConfig, ranking_config: RankingConfig) -> Self {
        Self {
            config: config.clone(),
            weights: ResultWeight::default(),
            ranking_config,
            personalization_context: PersonalizationContext::default(),
        }
    }

    /// 设置个性化上下文
    pub fn set_personalization_context(&mut self, context: PersonalizationContext) {
        self.personalization_context = context;
    }

    /// 合并并排序搜索结果
    pub async fn merge_and_rank(
        &self,
        vector_results: Vec<SearchResult>,
        keyword_results: Vec<SearchResult>,
        query: &ProcessedQuery,
    ) -> Result<Vec<SearchResult>> {
        let mut merged_results = self.merge_results(vector_results, keyword_results);
        self.rank_results(&mut merged_results, query).await?;
        
        // 应用相似度阈值过滤
        merged_results.retain(|result| result.score >= self.config.similarity_threshold);
        
        // 限制结果数量
        merged_results.truncate(query.top_k.min(self.config.max_top_k));
        
        Ok(merged_results)
    }

    /// 重新排序结果
    pub async fn rerank(
        &self,
        mut results: Vec<SearchResult>,
        query: &ProcessedQuery,
    ) -> Result<Vec<SearchResult>> {
        self.rank_results(&mut results, query).await?;
        Ok(results)
    }

    /// 合并结果列表
    fn merge_results(
        &self,
        vector_results: Vec<SearchResult>,
        keyword_results: Vec<SearchResult>,
    ) -> Vec<SearchResult> {
        let mut merged = std::collections::HashMap::new();
        
        // 添加向量搜索结果
        for result in vector_results {
            let weighted_score = result.score * self.weights.vector_weight;
            merged.insert(result.chunk_id.clone(), SearchResult {
                score: weighted_score,
                ..result
            });
        }
        
        // 合并关键词搜索结果
        for result in keyword_results {
            let weighted_score = result.score * self.weights.keyword_weight;
            
            if let Some(existing) = merged.get_mut(&result.chunk_id) {
                // 如果已存在，合并分数
                existing.score += weighted_score;
            } else {
                // 如果不存在，添加新结果
                merged.insert(result.chunk_id.clone(), SearchResult {
                    score: weighted_score,
                    ..result
                });
            }
        }
        
        merged.into_values().collect()
    }

    /// 对结果进行排序（增强版）
    async fn rank_results(
        &self,
        results: &mut Vec<SearchResult>,
        query: &ProcessedQuery,
    ) -> Result<()> {
        // 根据配置选择排序策略
        match self.ranking_config.primary_strategy {
            RankingStrategy::Relevance => self.rank_by_relevance(results, query).await?,
            RankingStrategy::Diversity => self.rank_by_diversity(results, query).await?,
            RankingStrategy::Freshness => self.rank_by_freshness(results, query).await?,
            RankingStrategy::Popularity => self.rank_by_popularity(results, query).await?,
            RankingStrategy::Hybrid => self.rank_by_hybrid(results, query).await?,
        }

        // 应用次要排序策略
        if let Some(secondary_strategy) = self.ranking_config.secondary_strategy {
            self.apply_secondary_ranking(results, query, secondary_strategy).await?;
        }

        // 应用个性化排序
        if self.ranking_config.enable_personalization {
            self.apply_personalization(results, query).await?;
        }

        Ok(())
    }

    /// 基于相关性排序
    async fn rank_by_relevance(
        &self,
        results: &mut Vec<SearchResult>,
        query: &ProcessedQuery,
    ) -> Result<()> {
        // 计算每个结果的相关性分数
        for result in results.iter_mut() {
            result.score = self.calculate_relevance_score(result, query);
        }

        // 按分数降序排序
        results.sort_by(|a, b| b.score.partial_cmp(&a.score).unwrap_or(std::cmp::Ordering::Equal));

        Ok(())
    }

    /// 基于多样性排序
    async fn rank_by_diversity(
        &self,
        results: &mut Vec<SearchResult>,
        query: &ProcessedQuery,
    ) -> Result<()> {
        if results.is_empty() {
            return Ok(());
        }

        // 首先按相关性排序
        self.rank_by_relevance(results, query).await?;

        // 应用多样性重排序
        let mut diversified = Vec::new();
        let mut remaining = results.clone();

        // 选择第一个最高分的结果
        if !remaining.is_empty() {
            diversified.push(remaining.remove(0));
        }

        // 基于多样性选择后续结果
        while !remaining.is_empty() && diversified.len() < results.len() {
            let mut best_idx = 0;
            let mut best_diversity_score = 0.0;

            for (idx, candidate) in remaining.iter().enumerate() {
                let diversity_score = self.calculate_diversity_score(candidate, &diversified);
                let combined_score = candidate.score * 0.7 + diversity_score * 0.3;

                if combined_score > best_diversity_score {
                    best_diversity_score = combined_score;
                    best_idx = idx;
                }
            }

            diversified.push(remaining.remove(best_idx));
        }

        *results = diversified;
        Ok(())
    }

    /// 基于新鲜度排序
    async fn rank_by_freshness(
        &self,
        results: &mut Vec<SearchResult>,
        _query: &ProcessedQuery,
    ) -> Result<()> {
        // 简化实现：假设文件路径中包含时间信息或使用默认权重
        for result in results.iter_mut() {
            let freshness_score = self.calculate_freshness_score(result);
            result.score = result.score * 0.7 + freshness_score * 0.3;
        }

        results.sort_by(|a, b| b.score.partial_cmp(&a.score).unwrap_or(std::cmp::Ordering::Equal));
        Ok(())
    }

    /// 基于流行度排序
    async fn rank_by_popularity(
        &self,
        results: &mut Vec<SearchResult>,
        _query: &ProcessedQuery,
    ) -> Result<()> {
        // 简化实现：基于符号名称长度和文件路径深度估算流行度
        for result in results.iter_mut() {
            let popularity_score = self.calculate_popularity_score(result);
            result.score = result.score * 0.7 + popularity_score * 0.3;
        }

        results.sort_by(|a, b| b.score.partial_cmp(&a.score).unwrap_or(std::cmp::Ordering::Equal));
        Ok(())
    }

    /// 混合排序
    async fn rank_by_hybrid(
        &self,
        results: &mut Vec<SearchResult>,
        query: &ProcessedQuery,
    ) -> Result<()> {
        // 计算每个结果的综合分数
        let results_clone = results.clone();
        for result in results.iter_mut() {
            let relevance_score = self.calculate_relevance_score(result, query);
            let diversity_score = self.calculate_diversity_score(result, &results_clone);
            let freshness_score = self.calculate_freshness_score(result);
            let popularity_score = self.calculate_popularity_score(result);

            // 加权组合各个分数
            result.score = relevance_score * self.ranking_config.relevance_weight
                + diversity_score * self.ranking_config.diversity_weight
                + freshness_score * self.ranking_config.freshness_weight
                + popularity_score * self.ranking_config.popularity_weight;
        }

        results.sort_by(|a, b| b.score.partial_cmp(&a.score).unwrap_or(std::cmp::Ordering::Equal));
        Ok(())
    }

    /// 计算相关性分数
    fn calculate_relevance_score(&self, result: &SearchResult, query: &ProcessedQuery) -> f32 {
        let mut relevance_score = result.score;

        // 文本匹配加权
        let text_score = self.calculate_text_match_score(result, query);
        relevance_score += text_score * self.weights.text_weight;

        // 语言匹配加权
        if let Some(ref lang_filter) = query.language_filter {
            if result.language == *lang_filter {
                relevance_score += self.ranking_config.language_match_weight;
            }
        }

        // 符号匹配加权
        let symbol_score = self.calculate_symbol_match_score(result, query);
        relevance_score += symbol_score * self.ranking_config.symbol_type_weight;

        // 文件路径匹配加权
        if let Some(ref file_filter) = query.file_filter {
            if result.file_path.contains(file_filter) {
                relevance_score += self.ranking_config.file_path_weight;
            }
        }

        // 位置偏好（较短的代码块可能更相关）
        let position_score = self.calculate_position_score(result);
        relevance_score += position_score * 0.1;

        relevance_score.clamp(0.0, 1.0)
    }

    /// 计算文本匹配分数
    fn calculate_text_match_score(&self, result: &SearchResult, query: &ProcessedQuery) -> f32 {
        let content_lower = result.content.to_lowercase();
        let mut matches = 0;
        
        for keyword in &query.keywords {
            if content_lower.contains(&keyword.to_lowercase()) {
                matches += 1;
            }
        }
        
        if query.keywords.is_empty() {
            0.0
        } else {
            matches as f32 / query.keywords.len() as f32
        }
    }

    /// 计算符号匹配分数
    fn calculate_symbol_match_score(&self, result: &SearchResult, query: &ProcessedQuery) -> f32 {
        if let Some(ref symbol_name) = result.symbol_name {
            let symbol_lower = symbol_name.to_lowercase();
            let mut matches = 0;
            
            for keyword in &query.keywords {
                if symbol_lower.contains(&keyword.to_lowercase()) {
                    matches += 1;
                }
                
                // 精确匹配加额外分数
                if symbol_lower == keyword.to_lowercase() {
                    matches += 2;
                }
            }
            
            if query.keywords.is_empty() {
                0.0
            } else {
                (matches as f32 / query.keywords.len() as f32).min(1.0)
            }
        } else {
            0.0
        }
    }

    /// 计算位置分数（偏好较短的代码块）
    fn calculate_position_score(&self, result: &SearchResult) -> f32 {
        let line_count = result.end_line - result.start_line + 1;
        
        // 较短的代码块得分更高
        if line_count <= 10 {
            0.3
        } else if line_count <= 50 {
            0.2
        } else if line_count <= 100 {
            0.1
        } else {
            0.0
        }
    }

    /// 应用多样性过滤（避免重复结果）
    pub fn apply_diversity_filter(&self, results: Vec<SearchResult>) -> Vec<SearchResult> {
        let mut filtered = Vec::new();
        let mut seen_files = std::collections::HashSet::new();
        let mut file_counts = std::collections::HashMap::new();
        
        for result in results {
            let file_count = file_counts.get(&result.file_path).unwrap_or(&0);
            
            // 限制每个文件的结果数量
            if *file_count < 3 {
                filtered.push(result.clone());
                file_counts.insert(result.file_path.clone(), file_count + 1);
                seen_files.insert(result.file_path);
            }
        }
        
        filtered
    }

    /// 计算多样性分数
    fn calculate_diversity_score(&self, candidate: &SearchResult, selected: &[SearchResult]) -> f32 {
        if selected.is_empty() {
            return 1.0;
        }

        let mut diversity_score: f32 = 1.0;

        for selected_result in selected {
            // 基于文件路径的多样性
            if candidate.file_path == selected_result.file_path {
                diversity_score *= 0.5;
            }

            // 基于语言的多样性
            if candidate.language != selected_result.language {
                diversity_score *= 1.2;
            }

            // 基于符号类型的多样性
            if candidate.symbol_name.is_some() && selected_result.symbol_name.is_some() {
                if candidate.symbol_name != selected_result.symbol_name {
                    diversity_score *= 1.1;
                }
            }
        }

        diversity_score.min(1.0)
    }

    /// 计算新鲜度分数
    fn calculate_freshness_score(&self, result: &SearchResult) -> f32 {
        // 简化实现：基于文件路径中的模式估算新鲜度
        let path_str = result.file_path.to_lowercase();

        if path_str.contains("new") || path_str.contains("latest") || path_str.contains("v2") {
            0.9
        } else if path_str.contains("old") || path_str.contains("legacy") || path_str.contains("deprecated") {
            0.3
        } else {
            0.6 // 默认分数
        }
    }

    /// 计算流行度分数
    fn calculate_popularity_score(&self, result: &SearchResult) -> f32 {
        let mut popularity_score: f32 = 0.5; // 基础分数

        // 基于符号名称长度（较短的名称可能更常用）
        if let Some(ref symbol_name) = result.symbol_name {
            if symbol_name.len() <= 5 {
                popularity_score += 0.2;
            } else if symbol_name.len() > 15 {
                popularity_score -= 0.1;
            }
        }

        // 基于文件路径深度（较浅的路径可能更重要）
        let path_depth = result.file_path.len(); // 简化实现
        if path_depth <= 20 {
            popularity_score += 0.2;
        } else if path_depth > 50 {
            popularity_score -= 0.1;
        }

        // 基于代码块大小（中等大小的代码块可能更有用）
        let line_count = result.end_line - result.start_line + 1;
        if line_count >= 10 && line_count <= 50 {
            popularity_score += 0.1;
        }

        popularity_score.clamp(0.0, 1.0)
    }

    /// 应用次要排序策略
    async fn apply_secondary_ranking(
        &self,
        results: &mut Vec<SearchResult>,
        _query: &ProcessedQuery,
        strategy: RankingStrategy,
    ) -> Result<()> {
        // 保存原始分数
        let original_scores: Vec<f32> = results.iter().map(|r| r.score).collect();

        // 应用次要排序
        match strategy {
            RankingStrategy::Diversity => {
                // 在相似分数的结果中增加多样性
                let results_clone = results.clone();
                for (i, result) in results.iter_mut().enumerate() {
                    let diversity_bonus = self.calculate_diversity_score(result, &results_clone) * 0.1;
                    result.score = original_scores[i] + diversity_bonus;
                }
            }
            RankingStrategy::Freshness => {
                for (i, result) in results.iter_mut().enumerate() {
                    let freshness_bonus = self.calculate_freshness_score(result) * 0.1;
                    result.score = original_scores[i] + freshness_bonus;
                }
            }
            _ => {} // 其他策略暂不实现
        }

        // 重新排序
        results.sort_by(|a, b| b.score.partial_cmp(&a.score).unwrap_or(std::cmp::Ordering::Equal));

        Ok(())
    }

    /// 应用个性化排序
    async fn apply_personalization(
        &self,
        results: &mut Vec<SearchResult>,
        _query: &ProcessedQuery,
    ) -> Result<()> {
        // 基于个性化上下文调整分数
        for result in results.iter_mut() {
            let mut personalization_bonus = 0.0;

            // 偏好语言加权
            if self.personalization_context.preferred_languages.contains(&result.language) {
                personalization_bonus += 0.1;
            }

            // 常用路径加权
            for frequent_path in &self.personalization_context.frequent_paths {
                if result.file_path.contains(frequent_path) {
                    personalization_bonus += 0.05;
                    break;
                }
            }

            result.score += personalization_bonus;
        }

        // 重新排序
        results.sort_by(|a, b| b.score.partial_cmp(&a.score).unwrap_or(std::cmp::Ordering::Equal));

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::query::QueryType;

    fn create_test_result(chunk_id: &str, score: f32, symbol_name: Option<&str>) -> SearchResult {
        SearchResult {
            chunk_id: chunk_id.to_string(),
            file_path: "test.rs".to_string(),
            content: "fn hello() { println!(\"Hello\"); }".to_string(),
            score,
            language: "rust".to_string(),
            symbol_name: symbol_name.map(|s| s.to_string()),
            start_line: 1,
            end_line: 3,
        }
    }

    fn create_test_query() -> ProcessedQuery {
        ProcessedQuery {
            original_text: "find hello function".to_string(),
            text: "find hello function".to_string(),
            keywords: vec!["find".to_string(), "hello".to_string(), "function".to_string()],
            query_type: QueryType::Semantic,
            top_k: 10,
            enable_hybrid: true,
            language_filter: Some("rust".to_string()),
            file_filter: None,
            symbol_filter: None,
        }
    }

    #[tokio::test]
    async fn test_result_ranking() {
        let config = QueryConfig::default();
        let ranker = ResultRanker::new(&config);
        
        let mut results = vec![
            create_test_result("1", 0.5, Some("hello")),
            create_test_result("2", 0.8, Some("world")),
            create_test_result("3", 0.3, Some("test")),
        ];
        
        let query = create_test_query();
        ranker.rank_results(&mut results, &query).await.unwrap();
        
        // 结果应该按分数降序排列
        assert!(results[0].score >= results[1].score);
        assert!(results[1].score >= results[2].score);
    }

    #[test]
    fn test_text_match_scoring() {
        let config = QueryConfig::default();
        let ranker = ResultRanker::new(&config);
        
        let result = create_test_result("1", 0.5, Some("hello"));
        let query = create_test_query();
        
        let score = ranker.calculate_text_match_score(&result, &query);
        assert!(score > 0.0); // 应该有一些匹配
    }

    #[test]
    fn test_diversity_filter() {
        let config = QueryConfig::default();
        let ranker = ResultRanker::new(&config);
        
        let results = vec![
            create_test_result("1", 0.9, Some("hello")),
            create_test_result("2", 0.8, Some("world")),
            create_test_result("3", 0.7, Some("test")),
        ];
        
        let filtered = ranker.apply_diversity_filter(results);
        assert_eq!(filtered.len(), 3);
    }
}