//! # 嵌入系统集成测试
//!
//! 测试整个嵌入系统的集成功能，包括多模型协作、故障转移、性能优化等。

#[cfg(test)]
mod tests {
    use crate::{
        config::EmbeddingConfig,
        embedding::{
            manager::{EmbeddingManager, EmbeddingManagerConfig, EmbedderType},
            model_registry::{ModelRegistry, ModelConfig, ModelSelectionCriteria, ModelSelectionStrategy},
            performance::PerformanceConfig,
            load_balancer::LoadBalancerConfig,
        },
    };
    use std::sync::Arc;
    use std::time::Duration;
    use tokio::time::sleep;

    /// 创建测试用的嵌入配置
    fn create_test_embedding_config() -> EmbeddingConfig {
        EmbeddingConfig {
            dimension: 384, // 使用较小的维度进行测试
            model_path: "test-model".to_string(),
            ..Default::default()
        }
    }

    /// 创建测试用的管理器配置
    fn create_test_manager_config() -> EmbeddingManagerConfig {
        EmbeddingManagerConfig {
            primary_embedder: EmbedderType::Local,
            fallback_embedders: vec![EmbedderType::OpenAI],
            performance_config: PerformanceConfig {
                enable_embedding_cache: true,
                cache_max_entries: 100,
                batch_size: 8,
                max_concurrency: 2,
                ..Default::default()
            },
            enable_failover: true,
            failover_timeout_seconds: 5,
            health_check_interval_seconds: 30,
        }
    }

    #[tokio::test]
    async fn test_embedding_manager_creation() {
        let embedding_config = create_test_embedding_config();
        let manager_config = create_test_manager_config();
        
        // 创建嵌入管理器（可能会失败，因为没有实际的模型）
        let result = EmbeddingManager::new(embedding_config, manager_config).await;
        
        // 根据是否有可用的模型，结果可能成功或失败
        match result {
            Ok(manager) => {
                // 如果成功创建，测试基本功能
                let _model_info = manager.model_info().await;
                let dimension = manager.dimension().await;

                // 基本断言
                assert!(dimension.is_some());
                println!("嵌入管理器创建成功，维度: {:?}", dimension);
            }
            Err(e) => {
                // 如果失败，确保是预期的错误（比如模型不可用）
                println!("嵌入管理器创建失败（预期）: {}", e);
                assert!(e.to_string().contains("模型") || e.to_string().contains("API"));
            }
        }
    }

    #[tokio::test]
    async fn test_model_registry_integration() {
        let registry = Arc::new(ModelRegistry::new());
        
        // 注册多个测试模型
        let models = vec![
            ModelConfig::new(
                "local-test".to_string(),
                EmbedderType::Local,
                "test-local".to_string(),
                384,
            ).with_priority(1).with_tag("type".to_string(), "local".to_string()),
            
            ModelConfig::new(
                "openai-test".to_string(),
                EmbedderType::OpenAI,
                "text-embedding-3-small".to_string(),
                1536,
            ).with_priority(2).with_tag("type".to_string(), "remote".to_string()),
            
            ModelConfig::new(
                "cohere-test".to_string(),
                EmbedderType::Cohere,
                "embed-english-v3.0".to_string(),
                1024,
            ).with_priority(3).with_tag("type".to_string(), "remote".to_string()),
        ];
        
        // 注册所有模型
        for model in models {
            let result = registry.register_model(model).await;
            assert!(result.is_ok());
        }
        
        // 验证注册结果
        let all_models = registry.list_models().await;
        assert_eq!(all_models.len(), 3);
        assert!(all_models.contains(&"local-test".to_string()));
        assert!(all_models.contains(&"openai-test".to_string()));
        assert!(all_models.contains(&"cohere-test".to_string()));
        
        // 测试模型选择
        let criteria = ModelSelectionCriteria {
            selection_strategy: ModelSelectionStrategy::Priority,
            ..Default::default()
        };
        
        // 设置模型状态
        registry.update_model_status("local-test", crate::embedding::model_registry::ModelStatus::Available).await.unwrap();
        registry.update_model_status("openai-test", crate::embedding::model_registry::ModelStatus::Available).await.unwrap();
        
        let best_model = registry.get_best_model(&criteria).await;
        assert_eq!(best_model, Some("local-test".to_string())); // 优先级最高
        
        // 测试按标签筛选
        let local_models = registry.list_models_by_tag("type", "local").await;
        assert_eq!(local_models.len(), 1);
        assert!(local_models.contains(&"local-test".to_string()));
        
        let remote_models = registry.list_models_by_tag("type", "remote").await;
        assert_eq!(remote_models.len(), 2);
    }

    #[tokio::test]
    async fn test_performance_metrics_collection() {
        let registry = Arc::new(ModelRegistry::new());
        
        // 注册测试模型
        let config = ModelConfig::new(
            "perf-test".to_string(),
            EmbedderType::Local,
            "test-model".to_string(),
            384,
        );
        registry.register_model(config).await.unwrap();
        
        // 模拟性能指标更新
        let response_times = vec![100.0, 150.0, 80.0, 200.0, 120.0];
        let success_rates = vec![true, true, false, true, true];
        
        for (time, success) in response_times.iter().zip(success_rates.iter()) {
            registry.update_model_metrics("perf-test", *time, *success).await.unwrap();
        }
        
        // 验证指标计算
        let metrics = registry.get_model_metrics("perf-test").await.unwrap();
        assert_eq!(metrics.total_requests, 5);
        assert_eq!(metrics.successful_requests, 4);
        assert_eq!(metrics.failed_requests, 1);
        assert_eq!(metrics.error_rate, 0.2);
        
        // 验证平均响应时间
        let expected_avg = (100.0 + 150.0 + 80.0 + 200.0 + 120.0) / 5.0;
        assert!((metrics.avg_response_time_ms - expected_avg).abs() < 0.01);
    }

    #[tokio::test]
    async fn test_load_balancing_strategies() {
        let registry = Arc::new(ModelRegistry::new());
        
        // 注册多个模型
        let models = vec![
            ("fast-model", 50.0, 1),
            ("slow-model", 200.0, 2),
            ("medium-model", 100.0, 3),
        ];
        
        for (id, response_time, priority) in models {
            let config = ModelConfig::new(
                id.to_string(),
                EmbedderType::Local,
                format!("test-{}", id),
                384,
            ).with_priority(priority);
            
            registry.register_model(config).await.unwrap();
            registry.update_model_status(id, crate::embedding::model_registry::ModelStatus::Available).await.unwrap();
            
            // 添加性能指标
            for _ in 0..10 {
                registry.update_model_metrics(id, response_time, true).await.unwrap();
            }
        }
        
        // 测试不同的选择策略
        let strategies = vec![
            (ModelSelectionStrategy::Priority, "fast-model"),
            (ModelSelectionStrategy::LowestLatency, "fast-model"),
        ];
        
        for (strategy, expected) in strategies {
            let criteria = ModelSelectionCriteria {
                selection_strategy: strategy,
                ..Default::default()
            };
            
            let selected = registry.get_best_model(&criteria).await;
            assert_eq!(selected, Some(expected.to_string()));
        }
    }

    #[tokio::test]
    async fn test_model_health_monitoring() {
        let registry = Arc::new(ModelRegistry::new());
        
        // 注册测试模型
        let config = ModelConfig::new(
            "health-test".to_string(),
            EmbedderType::Local,
            "test-model".to_string(),
            384,
        );
        registry.register_model(config).await.unwrap();
        
        // 初始状态
        let status = registry.get_model_status("health-test").await;
        assert_eq!(status, Some(crate::embedding::model_registry::ModelStatus::Uninitialized));
        
        // 模拟健康检查过程
        registry.update_model_status("health-test", crate::embedding::model_registry::ModelStatus::Initializing).await.unwrap();
        sleep(Duration::from_millis(10)).await;
        
        registry.update_model_status("health-test", crate::embedding::model_registry::ModelStatus::Available).await.unwrap();
        registry.update_health_check("health-test").await.unwrap();
        
        // 验证状态更新
        let status = registry.get_model_status("health-test").await;
        assert_eq!(status, Some(crate::embedding::model_registry::ModelStatus::Available));
        
        // 模拟故障
        registry.update_model_status("health-test", crate::embedding::model_registry::ModelStatus::Error("Connection failed".to_string())).await.unwrap();
        
        let status = registry.get_model_status("health-test").await;
        assert!(matches!(status, Some(crate::embedding::model_registry::ModelStatus::Error(_))));
    }

    #[tokio::test]
    async fn test_registry_summary_reporting() {
        let registry = Arc::new(ModelRegistry::new());
        
        // 注册不同状态的模型
        let models = vec![
            ("available-1", crate::embedding::model_registry::ModelStatus::Available),
            ("available-2", crate::embedding::model_registry::ModelStatus::Available),
            ("error-1", crate::embedding::model_registry::ModelStatus::Error("test".to_string())),
            ("maintenance-1", crate::embedding::model_registry::ModelStatus::Maintenance),
        ];
        
        for (id, status) in models {
            let config = ModelConfig::new(
                id.to_string(),
                EmbedderType::Local,
                format!("test-{}", id),
                384,
            );
            registry.register_model(config).await.unwrap();
            registry.update_model_status(id, status).await.unwrap();
        }
        
        // 获取摘要
        let summary = registry.get_registry_summary().await;
        assert_eq!(summary.total_models, 4);
        assert_eq!(summary.available_models, 2);
        assert_eq!(summary.error_models, 1);
        assert_eq!(summary.maintenance_models, 1);
        assert_eq!(summary.models_by_type.get("local"), Some(&4));
    }

    #[tokio::test]
    async fn test_concurrent_model_operations() {
        let registry = Arc::new(ModelRegistry::new());
        
        // 并发注册多个模型
        let mut handles = vec![];
        
        for i in 0..10 {
            let registry_clone = registry.clone();
            let handle = tokio::spawn(async move {
                let config = ModelConfig::new(
                    format!("concurrent-{}", i),
                    EmbedderType::Local,
                    format!("test-model-{}", i),
                    384,
                );
                registry_clone.register_model(config).await
            });
            handles.push(handle);
        }
        
        // 等待所有注册完成
        for handle in handles {
            let result = handle.await.unwrap();
            assert!(result.is_ok());
        }
        
        // 验证所有模型都已注册
        let models = registry.list_models().await;
        assert_eq!(models.len(), 10);
        
        // 并发更新模型状态
        let mut handles = vec![];
        
        for i in 0..10 {
            let registry_clone = registry.clone();
            let handle = tokio::spawn(async move {
                registry_clone.update_model_status(
                    &format!("concurrent-{}", i),
                    crate::embedding::model_registry::ModelStatus::Available
                ).await
            });
            handles.push(handle);
        }
        
        // 等待所有更新完成
        for handle in handles {
            let result = handle.await.unwrap();
            assert!(result.is_ok());
        }
        
        // 验证状态更新
        let summary = registry.get_registry_summary().await;
        assert_eq!(summary.available_models, 10);
    }

    #[test]
    fn test_configuration_serialization() {
        // 测试各种配置的序列化和反序列化
        let embedding_config = create_test_embedding_config();
        let serialized = serde_json::to_string(&embedding_config).unwrap();
        let deserialized: EmbeddingConfig = serde_json::from_str(&serialized).unwrap();
        assert_eq!(embedding_config.dimension, deserialized.dimension);
        
        let manager_config = create_test_manager_config();
        let serialized = serde_json::to_string(&manager_config).unwrap();
        let deserialized: EmbeddingManagerConfig = serde_json::from_str(&serialized).unwrap();
        assert_eq!(manager_config.primary_embedder, deserialized.primary_embedder);
        
        let load_balancer_config = LoadBalancerConfig::default();
        let serialized = serde_json::to_string(&load_balancer_config).unwrap();
        let deserialized: LoadBalancerConfig = serde_json::from_str(&serialized).unwrap();
        assert_eq!(load_balancer_config.strategy, deserialized.strategy);
    }
}
