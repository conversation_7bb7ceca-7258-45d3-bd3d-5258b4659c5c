//! # UI 工具函数
//!
//! 提供 UI 相关的工具函数。

use crossterm::{
    terminal::{size, Clear, ClearType},
    cursor::{MoveTo, position},
    execute,
};
use std::io::stdout;

/// 获取终端尺寸
pub fn get_terminal_size() -> Result<(u16, u16), Box<dyn std::error::Error>> {
    Ok(size()?)
}

/// 检查终端是否支持颜色
pub fn supports_color() -> bool {
    // 检查环境变量
    if std::env::var("NO_COLOR").is_ok() {
        return false;
    }

    if let Ok(term) = std::env::var("TERM") {
        if term == "dumb" {
            return false;
        }
        
        // 大多数现代终端都支持颜色
        return term.contains("color") || 
               term.contains("xterm") || 
               term.contains("256") ||
               !term.is_empty();
    }

    // 默认假设支持颜色
    true
}

/// 检查终端是否支持真彩色
pub fn supports_truecolor() -> bool {
    if let Ok(colorterm) = std::env::var("COLORTERM") {
        return colorterm.contains("truecolor") || colorterm.contains("24bit");
    }
    false
}

/// 检查是否在 TTY 中运行
pub fn is_tty() -> bool {
    atty::is(atty::Stream::Stdout)
}

/// 计算文本的显示宽度（考虑 Unicode 字符）
pub fn text_width(text: &str) -> usize {
    text.chars()
        .map(|c| {
            // 简化的宽度计算，实际应用中可能需要更复杂的 Unicode 处理
            match c {
                // CJK 字符通常占用两个显示位置
                '\u{4e00}'..='\u{9fff}' |  // 中文
                '\u{3040}'..='\u{309f}' |  // 平假名
                '\u{30a0}'..='\u{30ff}' |  // 片假名
                '\u{ac00}'..='\u{d7af}' => 2, // 韩文
                _ => 1,
            }
        })
        .sum()
}

/// 截断文本以适应指定宽度
pub fn truncate_text(text: &str, max_width: usize) -> String {
    if text_width(text) <= max_width {
        return text.to_string();
    }

    // 如果最大宽度小于3，无法显示省略号
    if max_width < 3 {
        return text.chars().take(max_width).collect();
    }

    let mut result = String::new();
    let mut current_width = 0;
    let ellipsis_width = 3; // "..." 的宽度

    for c in text.chars() {
        let char_width = match c {
            '\u{4e00}'..='\u{9fff}' |
            '\u{3040}'..='\u{309f}' |
            '\u{30a0}'..='\u{30ff}' |
            '\u{ac00}'..='\u{d7af}' => 2,
            _ => 1,
        };

        // 检查添加当前字符和省略号是否会超过最大宽度
        if current_width + char_width + ellipsis_width > max_width {
            result.push_str("...");
            break;
        }

        result.push(c);
        current_width += char_width;
    }

    result
}

/// 填充文本到指定宽度
pub fn pad_text(text: &str, width: usize, align: TextAlign) -> String {
    let text_len = text_width(text);
    
    if text_len >= width {
        return text.to_string();
    }
    
    let padding = width - text_len;
    
    match align {
        TextAlign::Left => format!("{}{}", text, " ".repeat(padding)),
        TextAlign::Right => format!("{}{}", " ".repeat(padding), text),
        TextAlign::Center => {
            let left_padding = padding / 2;
            let right_padding = padding - left_padding;
            format!("{}{}{}", " ".repeat(left_padding), text, " ".repeat(right_padding))
        }
    }
}

/// 文本对齐方式
#[derive(Debug, Clone, Copy)]
pub enum TextAlign {
    Left,
    Right,
    Center,
}

/// 清除屏幕
pub fn clear_screen() -> Result<(), Box<dyn std::error::Error>> {
    execute!(stdout(), Clear(ClearType::All), MoveTo(0, 0))?;
    Ok(())
}

/// 清除当前行
pub fn clear_line() -> Result<(), Box<dyn std::error::Error>> {
    execute!(stdout(), Clear(ClearType::CurrentLine))?;
    Ok(())
}

/// 移动光标到指定位置
pub fn move_cursor(x: u16, y: u16) -> Result<(), Box<dyn std::error::Error>> {
    execute!(stdout(), MoveTo(x, y))?;
    Ok(())
}

/// 获取当前光标位置
pub fn get_cursor_position() -> Result<(u16, u16), Box<dyn std::error::Error>> {
    Ok(position()?)
}

/// 创建分隔线
pub fn create_separator(width: usize, char: char) -> String {
    char.to_string().repeat(width)
}

/// 格式化文件大小
pub fn format_file_size(size: u64) -> String {
    const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB"];
    let mut size = size as f64;
    let mut unit_index = 0;
    
    while size >= 1024.0 && unit_index < UNITS.len() - 1 {
        size /= 1024.0;
        unit_index += 1;
    }
    
    if unit_index == 0 {
        format!("{} {}", size as u64, UNITS[unit_index])
    } else {
        format!("{:.1} {}", size, UNITS[unit_index])
    }
}

/// 格式化持续时间
pub fn format_duration(duration: std::time::Duration) -> String {
    let total_seconds = duration.as_secs();
    
    if total_seconds < 60 {
        format!("{}秒", total_seconds)
    } else if total_seconds < 3600 {
        let minutes = total_seconds / 60;
        let seconds = total_seconds % 60;
        format!("{}分{}秒", minutes, seconds)
    } else {
        let hours = total_seconds / 3600;
        let minutes = (total_seconds % 3600) / 60;
        let seconds = total_seconds % 60;
        format!("{}时{}分{}秒", hours, minutes, seconds)
    }
}

/// 创建表格
pub struct Table {
    headers: Vec<String>,
    rows: Vec<Vec<String>>,
    column_widths: Vec<usize>,
}

impl Table {
    /// 创建新表格
    pub fn new(headers: Vec<String>) -> Self {
        let column_widths = headers.iter().map(|h| text_width(h)).collect();
        Self {
            headers,
            rows: Vec::new(),
            column_widths,
        }
    }

    /// 添加行
    pub fn add_row(&mut self, row: Vec<String>) {
        // 更新列宽
        for (i, cell) in row.iter().enumerate() {
            if i < self.column_widths.len() {
                let cell_width = text_width(cell);
                if cell_width > self.column_widths[i] {
                    self.column_widths[i] = cell_width;
                }
            }
        }
        self.rows.push(row);
    }

    /// 渲染表格
    pub fn render(&self) -> String {
        let mut result = String::new();
        
        // 渲染头部
        let header_row = self.headers
            .iter()
            .enumerate()
            .map(|(i, header)| {
                pad_text(header, self.column_widths[i], TextAlign::Left)
            })
            .collect::<Vec<_>>()
            .join(" | ");
        
        result.push_str(&header_row);
        result.push('\n');
        
        // 渲染分隔线
        let separator = self.column_widths
            .iter()
            .map(|&width| "-".repeat(width))
            .collect::<Vec<_>>()
            .join("-+-");
        
        result.push_str(&separator);
        result.push('\n');
        
        // 渲染数据行
        for row in &self.rows {
            let data_row = row
                .iter()
                .enumerate()
                .map(|(i, cell)| {
                    let width = if i < self.column_widths.len() {
                        self.column_widths[i]
                    } else {
                        10 // 默认宽度
                    };
                    pad_text(cell, width, TextAlign::Left)
                })
                .collect::<Vec<_>>()
                .join(" | ");
            
            result.push_str(&data_row);
            result.push('\n');
        }
        
        result
    }
}

/// 解析颜色字符串
pub fn parse_color(color_str: &str) -> Option<crossterm::style::Color> {
    use crossterm::style::Color;
    
    match color_str.to_lowercase().as_str() {
        "black" => Some(Color::Black),
        "red" => Some(Color::Red),
        "green" => Some(Color::Green),
        "yellow" => Some(Color::Yellow),
        "blue" => Some(Color::Blue),
        "magenta" => Some(Color::Magenta),
        "cyan" => Some(Color::Cyan),
        "white" => Some(Color::White),
        "grey" | "gray" => Some(Color::Grey),
        "darkgrey" | "darkgray" => Some(Color::DarkGrey),
        "lightred" => Some(Color::DarkRed),
        "lightgreen" => Some(Color::DarkGreen),
        "lightyellow" => Some(Color::DarkYellow),
        "lightblue" => Some(Color::DarkBlue),
        "lightmagenta" => Some(Color::DarkMagenta),
        "lightcyan" => Some(Color::DarkCyan),
        _ => {
            // 尝试解析 RGB 颜色 (格式: #RRGGBB 或 rgb(r,g,b))
            if color_str.starts_with('#') && color_str.len() == 7 {
                if let (Ok(r), Ok(g), Ok(b)) = (
                    u8::from_str_radix(&color_str[1..3], 16),
                    u8::from_str_radix(&color_str[3..5], 16),
                    u8::from_str_radix(&color_str[5..7], 16),
                ) {
                    return Some(Color::Rgb { r, g, b });
                }
            }
            None
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_text_width() {
        assert_eq!(text_width("hello"), 5);
        assert_eq!(text_width("你好"), 4); // 中文字符宽度为 2
    }

    #[test]
    fn test_truncate_text() {
        assert_eq!(truncate_text("hello world", 5), "he...");
        assert_eq!(truncate_text("short", 10), "short");
    }

    #[test]
    fn test_pad_text() {
        assert_eq!(pad_text("test", 10, TextAlign::Left), "test      ");
        assert_eq!(pad_text("test", 10, TextAlign::Right), "      test");
        assert_eq!(pad_text("test", 10, TextAlign::Center), "   test   ");
    }

    #[test]
    fn test_format_file_size() {
        assert_eq!(format_file_size(1024), "1.0 KB");
        assert_eq!(format_file_size(1048576), "1.0 MB");
    }

    #[test]
    fn test_table() {
        let mut table = Table::new(vec!["Name".to_string(), "Age".to_string()]);
        table.add_row(vec!["Alice".to_string(), "25".to_string()]);
        table.add_row(vec!["Bob".to_string(), "30".to_string()]);
        
        let rendered = table.render();
        assert!(rendered.contains("Name"));
        assert!(rendered.contains("Alice"));
    }

    #[test]
    fn test_parse_color() {
        assert!(parse_color("red").is_some());
        assert!(parse_color("#FF0000").is_some());
        assert!(parse_color("invalid").is_none());
    }
}