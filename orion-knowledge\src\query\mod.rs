//! # 查询处理模块
//!
//! 提供查询预处理、混合搜索和结果排序功能。

pub mod query_cache;
pub mod query_parser;
pub mod query_processor;
pub mod result_ranker;
pub mod search_optimizer;

use crate::{
    config::QueryConfig,
    error::Result,
    knowledge_base::SearchResult,
};
use serde::{Deserialize, Serialize};

/// 处理后的查询
#[derive(Debug, Clone)]
pub struct ProcessedQuery {
    /// 原始查询文本
    pub original_text: String,
    /// 处理后的查询文本
    pub text: String,
    /// 提取的关键词
    pub keywords: Vec<String>,
    /// 查询类型
    pub query_type: QueryType,
    /// 搜索结果数量
    pub top_k: usize,
    /// 是否启用混合搜索
    pub enable_hybrid: bool,
    /// 语言过滤
    pub language_filter: Option<String>,
    /// 文件路径过滤
    pub file_filter: Option<String>,
    /// 符号类型过滤
    pub symbol_filter: Option<SymbolType>,
}

/// 查询类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum QueryType {
    /// 语义搜索
    Semantic,
    /// 精确匹配
    Exact,
    /// 代码搜索
    Code,
    /// 符号搜索
    Symbol,
    /// 文档搜索
    Documentation,
    /// 混合搜索
    Hybrid,
}

/// 符号类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SymbolType {
    Function,
    Class,
    Struct,
    Interface,
    Enum,
    Constant,
    Variable,
}

/// 搜索结果权重
#[derive(Debug, Clone)]
pub struct ResultWeight {
    /// 向量相似度权重
    pub vector_weight: f32,
    /// 关键词匹配权重
    pub keyword_weight: f32,
    /// 文本匹配权重
    pub text_weight: f32,
    /// 语言匹配权重
    pub language_weight: f32,
    /// 符号匹配权重
    pub symbol_weight: f32,
}

/// 查询处理器
pub struct QueryProcessor {
    config: QueryConfig,
    parser: query_parser::QueryParser,
    optimizer: search_optimizer::SearchOptimizer,
    ranker: result_ranker::ResultRanker,
}

impl QueryProcessor {
    /// 创建新的查询处理器
    pub fn new(config: &QueryConfig) -> Result<Self> {
        Ok(Self {
            config: config.clone(),
            parser: query_parser::QueryParser::new(),
            optimizer: search_optimizer::SearchOptimizer::new(config),
            ranker: result_ranker::ResultRanker::new(config),
        })
    }

    /// 处理查询
    pub async fn process_query(&self, query: &str) -> Result<ProcessedQuery> {
        // 解析查询语法
        let parsed = self.parser.parse(query)?;
        
        // 优化查询
        let optimized = self.optimizer.optimize(parsed).await?;
        
        Ok(optimized)
    }

    /// 合并搜索结果
    pub async fn merge_results(
        &self,
        vector_results: Vec<SearchResult>,
        keyword_results: Vec<SearchResult>,
        query: &ProcessedQuery,
    ) -> Result<Vec<SearchResult>> {
        self.ranker.merge_and_rank(vector_results, keyword_results, query).await
    }

    /// 重新排序结果
    pub async fn rerank_results(
        &self,
        results: Vec<SearchResult>,
        query: &ProcessedQuery,
    ) -> Result<Vec<SearchResult>> {
        self.ranker.rerank(results, query).await
    }

    /// 计算查询相关性
    pub fn calculate_relevance(
        &self,
        result: &SearchResult,
        query: &ProcessedQuery,
    ) -> f32 {
        let mut relevance = result.score;

        // 关键词匹配加权
        let keyword_matches = query.keywords.iter()
            .filter(|keyword| {
                result.content.to_lowercase().contains(&keyword.to_lowercase()) ||
                result.symbol_name.as_ref()
                    .map(|name| name.to_lowercase().contains(&keyword.to_lowercase()))
                    .unwrap_or(false)
            })
            .count() as f32;

        if !query.keywords.is_empty() {
            let keyword_ratio = keyword_matches / query.keywords.len() as f32;
            relevance += keyword_ratio * 0.3;
        }

        // 语言匹配加权
        if let Some(ref lang_filter) = query.language_filter {
            if result.language == *lang_filter {
                relevance += 0.2;
            }
        }

        // 符号名称精确匹配加权
        if let Some(ref symbol_name) = result.symbol_name {
            for keyword in &query.keywords {
                if symbol_name.to_lowercase() == keyword.to_lowercase() {
                    relevance += 0.4;
                    break;
                }
            }
        }

        relevance.min(1.0)
    }

    /// 过滤结果
    pub fn filter_results(
        &self,
        results: Vec<SearchResult>,
        query: &ProcessedQuery,
    ) -> Vec<SearchResult> {
        results.into_iter()
            .filter(|result| {
                // 相似度阈值过滤
                if result.score < self.config.similarity_threshold {
                    return false;
                }

                // 语言过滤
                if let Some(ref lang_filter) = query.language_filter {
                    if result.language != *lang_filter {
                        return false;
                    }
                }

                // 文件路径过滤
                if let Some(ref file_filter) = query.file_filter {
                    if !result.file_path.contains(file_filter) {
                        return false;
                    }
                }

                true
            })
            .collect()
    }

    /// 获取默认查询配置
    pub fn get_default_query(&self, text: &str) -> ProcessedQuery {
        ProcessedQuery {
            original_text: text.to_string(),
            text: text.to_string(),
            keywords: self.extract_keywords(text),
            query_type: QueryType::Semantic,
            top_k: self.config.default_top_k,
            enable_hybrid: true,
            language_filter: None,
            file_filter: None,
            symbol_filter: None,
        }
    }

    /// 提取关键词
    fn extract_keywords(&self, text: &str) -> Vec<String> {
        // 简单的关键词提取，实际实现可能更复杂
        text.split_whitespace()
            .filter(|word| word.len() > 2)
            .map(|word| word.trim_matches(|c: char| !c.is_alphanumeric()).to_lowercase())
            .filter(|word| !word.is_empty())
            .collect()
    }
}

/// 默认结果权重
impl Default for ResultWeight {
    fn default() -> Self {
        Self {
            vector_weight: 0.7,
            keyword_weight: 0.2,
            text_weight: 0.05,
            language_weight: 0.03,
            symbol_weight: 0.02,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_query_processing() {
        let config = QueryConfig::default();
        let processor = QueryProcessor::new(&config).unwrap();
        
        let query = processor.process_query("find function hello").await.unwrap();
        assert_eq!(query.original_text, "find function hello");
        assert!(!query.keywords.is_empty());
    }

    #[test]
    fn test_keyword_extraction() {
        let config = QueryConfig::default();
        let processor = QueryProcessor::new(&config).unwrap();
        
        let keywords = processor.extract_keywords("find function hello world");
        assert!(keywords.contains(&"find".to_string()));
        assert!(keywords.contains(&"function".to_string()));
        assert!(keywords.contains(&"hello".to_string()));
        assert!(keywords.contains(&"world".to_string()));
    }

    #[test]
    fn test_relevance_calculation() {
        let config = QueryConfig::default();
        let processor = QueryProcessor::new(&config).unwrap();
        
        let result = SearchResult {
            chunk_id: "test".to_string(),
            file_path: "test.rs".to_string(),
            content: "fn hello() { println!(\"Hello\"); }".to_string(),
            score: 0.8,
            language: "rust".to_string(),
            symbol_name: Some("hello".to_string()),
            start_line: 1,
            end_line: 1,
        };
        
        let query = ProcessedQuery {
            original_text: "hello function".to_string(),
            text: "hello function".to_string(),
            keywords: vec!["hello".to_string(), "function".to_string()],
            query_type: QueryType::Semantic,
            top_k: 10,
            enable_hybrid: true,
            language_filter: Some("rust".to_string()),
            file_filter: None,
            symbol_filter: None,
        };
        
        let relevance = processor.calculate_relevance(&result, &query);
        assert!(relevance > 0.8);
    }
}