//! # UI 组件
//!
//! 可重用的终端 UI 组件。

use crossterm::{
    execute,
    style::{Color, SetForegroundColor, ResetColor, Print},
    terminal::{Clear, ClearType},
};
use std::io::{stdout, Write};
use crate::ui::{
    constants::{borders, status_indicators, LOADING_CHARS},
    colors::Colors,
    ascii_art::{select_logo_for_width, center_ascii_art},
};

/// 头部组件
pub struct Header {
    terminal_width: u16,
    version: String,
    show_version: bool,
}

impl Header {
    /// 创建新的头部组件
    pub fn new(terminal_width: u16, version: String) -> Self {
        Self {
            terminal_width,
            version,
            show_version: true,
        }
    }

    /// 设置是否显示版本
    pub fn show_version(mut self, show: bool) -> Self {
        self.show_version = show;
        self
    }

    /// 渲染头部
    pub fn render(&self) -> Result<(), Box<dyn std::error::Error>> {
        // 选择合适的 Logo
        let logo = select_logo_for_width(self.terminal_width);
        let centered_logo = center_ascii_art(logo, self.terminal_width);

        // 渲染渐变 Logo
        if let Some(gradient_colors) = Colors::gradient_colors() {
            self.render_gradient_logo(&centered_logo, &gradient_colors)?;
        } else {
            execute!(
                stdout(),
                SetForegroundColor(Colors::accent_blue()),
                Print(&centered_logo),
                ResetColor
            )?;
        }

        // 显示版本信息
        if self.show_version {
            let version_text = format!("🚀 Orion Agent 系统 v{}", self.version);
            let centered_version = self.center_text(&version_text);
            
            execute!(
                stdout(),
                SetForegroundColor(Colors::accent_green()),
                Print(&centered_version),
                Print("\n"),
                ResetColor
            )?;
        }

        // 显示分隔线
        self.render_separator()?;
        
        stdout().flush()?;
        Ok(())
    }

    /// 渲染渐变 Logo
    fn render_gradient_logo(&self, logo: &str, colors: &[Color]) -> Result<(), Box<dyn std::error::Error>> {
        let lines: Vec<&str> = logo.lines().collect();
        let color_count = colors.len();
        
        for (i, line) in lines.iter().enumerate() {
            if !line.trim().is_empty() {
                let color_index = i % color_count;
                execute!(
                    stdout(),
                    SetForegroundColor(colors[color_index]),
                    Print(line),
                    Print("\n"),
                    ResetColor
                )?;
            } else {
                execute!(stdout(), Print("\n"))?;
            }
        }
        
        Ok(())
    }

    /// 居中文本
    fn center_text(&self, text: &str) -> String {
        let text_width = text.chars().count();
        let padding = if self.terminal_width as usize > text_width {
            (self.terminal_width as usize - text_width) / 2
        } else {
            0
        };
        format!("{}{}", " ".repeat(padding), text)
    }

    /// 渲染分隔线
    fn render_separator(&self) -> Result<(), Box<dyn std::error::Error>> {
        let separator = "─".repeat(self.terminal_width as usize);
        execute!(
            stdout(),
            SetForegroundColor(Colors::border()),
            Print(&separator),
            Print("\n"),
            ResetColor
        )?;
        Ok(())
    }
}

/// 底部组件
pub struct Footer {
    terminal_width: u16,
}

impl Footer {
    /// 创建新的底部组件
    pub fn new(terminal_width: u16) -> Self {
        Self { terminal_width }
    }

    /// 渲染底部
    pub fn render(&self, status_text: Option<&str>) -> Result<(), Box<dyn std::error::Error>> {
        // 渲染分隔线
        let separator = "─".repeat(self.terminal_width as usize);
        execute!(
            stdout(),
            SetForegroundColor(Colors::border()),
            Print(&separator),
            Print("\n"),
            ResetColor
        )?;

        // 显示状态信息
        if let Some(status) = status_text {
            let centered_status = self.center_text(status);
            execute!(
                stdout(),
                SetForegroundColor(Colors::secondary()),
                Print(&centered_status),
                Print("\n"),
                ResetColor
            )?;
        }

        // 显示当前工作目录信息
        if let Ok(current_dir) = std::env::current_dir() {
            let dir_info = format!("📁 {}", current_dir.display());
            let centered_dir = self.center_text(&dir_info);
            execute!(
                stdout(),
                SetForegroundColor(Colors::info()),
                Print(&centered_dir),
                Print("\n"),
                ResetColor
            )?;
        }

        // 显示帮助提示
        let help_text = "💡 输入 'help' 查看帮助 | 'exit' 退出 | Ctrl+C 强制退出";
        let centered_help = self.center_text(help_text);
        execute!(
            stdout(),
            SetForegroundColor(Colors::comment()),
            Print(&centered_help),
            Print("\n"),
            ResetColor
        )?;

        stdout().flush()?;
        Ok(())
    }

    /// 居中文本
    fn center_text(&self, text: &str) -> String {
        let text_width = text.chars().count();
        let padding = if self.terminal_width as usize > text_width {
            (self.terminal_width as usize - text_width) / 2
        } else {
            0
        };
        format!("{}{}", " ".repeat(padding), text)
    }
}

/// 加载指示器
pub struct LoadingIndicator {
    current_frame: usize,
    message: String,
    running: std::sync::Arc<std::sync::atomic::AtomicBool>,
    handle: Option<std::thread::JoinHandle<()>>,
}

impl LoadingIndicator {
    /// 创建新的加载指示器
    pub fn new(message: String) -> Self {
        Self {
            current_frame: 0,
            message,
            running: std::sync::Arc::new(std::sync::atomic::AtomicBool::new(false)),
            handle: None,
        }
    }

    /// 开始动画
    pub fn start(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        if self.running.load(std::sync::atomic::Ordering::Relaxed) {
            return Ok(()); // 已经在运行
        }

        self.running.store(true, std::sync::atomic::Ordering::Relaxed);
        let message = self.message.clone();
        let running = self.running.clone();
        
        let handle = std::thread::spawn(move || {
            let mut frame = 0;
            while running.load(std::sync::atomic::Ordering::Relaxed) {
                let spinner_char = LOADING_CHARS[frame];
                
                // 清除当前行并渲染新的加载状态
                let _ = execute!(
                    stdout(),
                    Clear(ClearType::CurrentLine),
                    crossterm::cursor::MoveToColumn(0),
                    SetForegroundColor(Colors::accent_blue()),
                    Print(format!("{} {}", spinner_char, message)),
                    ResetColor
                );
                let _ = stdout().flush();
                
                frame = (frame + 1) % LOADING_CHARS.len();
                std::thread::sleep(std::time::Duration::from_millis(100));
            }
        });
        
        self.handle = Some(handle);
        Ok(())
    }

    /// 更新动画帧
    pub fn update(&mut self) {
        self.current_frame = (self.current_frame + 1) % LOADING_CHARS.len();
    }

    /// 渲染加载指示器（用于静态显示）
    pub fn render(&self) -> Result<(), Box<dyn std::error::Error>> {
        let spinner_char = LOADING_CHARS[self.current_frame];
        
        execute!(
            stdout(),
            SetForegroundColor(Colors::accent_blue()),
            Print(format!("{} {}", spinner_char, self.message)),
            ResetColor
        )?;
        
        stdout().flush()?;
        Ok(())
    }

    /// 停止动画并清除指示器
    pub fn stop_and_clear(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        // 停止动画线程
        self.running.store(false, std::sync::atomic::Ordering::Relaxed);
        
        // 等待线程结束
        if let Some(handle) = self.handle.take() {
            let _ = handle.join();
        }
        
        // 清除当前行
        execute!(
            stdout(),
            Clear(ClearType::CurrentLine),
            crossterm::cursor::MoveToColumn(0)
        )?;
        stdout().flush()?;
        Ok(())
    }

    /// 清除加载指示器（兼容旧接口）
    pub fn clear(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        self.stop_and_clear()
    }
}

/// 进度条组件
pub struct ProgressBar {
    width: usize,
    progress: f32, // 0.0 - 1.0
    label: String,
}

impl ProgressBar {
    /// 创建新的进度条
    pub fn new(width: usize, label: String) -> Self {
        Self {
            width,
            progress: 0.0,
            label,
        }
    }

    /// 设置进度
    pub fn set_progress(&mut self, progress: f32) {
        self.progress = progress.clamp(0.0, 1.0);
    }

    /// 渲染进度条
    pub fn render(&self) -> Result<(), Box<dyn std::error::Error>> {
        let filled_width = (self.width as f32 * self.progress) as usize;
        let empty_width = self.width - filled_width;

        let filled_bar = "█".repeat(filled_width);
        let empty_bar = "░".repeat(empty_width);
        
        let percentage = (self.progress * 100.0) as u8;

        execute!(
            stdout(),
            SetForegroundColor(Colors::accent_green()),
            Print(&filled_bar),
            SetForegroundColor(Colors::gray()),
            Print(&empty_bar),
            ResetColor,
            Print(format!(" {}% {}", percentage, self.label))
        )?;

        stdout().flush()?;
        Ok(())
    }
}

/// 状态指示器
pub struct StatusIndicator;

impl StatusIndicator {
    /// 渲染成功状态
    pub fn success(message: &str) -> Result<(), Box<dyn std::error::Error>> {
        execute!(
            stdout(),
            SetForegroundColor(Colors::success()),
            Print(format!("{} {}", status_indicators::SUCCESS, message)),
            ResetColor,
            Print("\n")
        )?;
        stdout().flush()?;
        Ok(())
    }

    /// 渲染错误状态
    pub fn error(message: &str) -> Result<(), Box<dyn std::error::Error>> {
        execute!(
            stdout(),
            SetForegroundColor(Colors::error()),
            Print(format!("{} {}", status_indicators::ERROR, message)),
            ResetColor,
            Print("\n")
        )?;
        stdout().flush()?;
        Ok(())
    }

    /// 渲染警告状态
    pub fn warning(message: &str) -> Result<(), Box<dyn std::error::Error>> {
        execute!(
            stdout(),
            SetForegroundColor(Colors::warning()),
            Print(format!("{} {}", status_indicators::WARNING, message)),
            ResetColor,
            Print("\n")
        )?;
        stdout().flush()?;
        Ok(())
    }

    /// 渲染信息状态
    pub fn info(message: &str) -> Result<(), Box<dyn std::error::Error>> {
        execute!(
            stdout(),
            SetForegroundColor(Colors::info()),
            Print(format!("{} {}", status_indicators::INFO, message)),
            ResetColor,
            Print("\n")
        )?;
        stdout().flush()?;
        Ok(())
    }
}

/// 边框组件
pub struct Border;

impl Border {
    /// 渲染单线边框
    pub fn single(width: usize, height: usize, title: Option<&str>) -> Result<(), Box<dyn std::error::Error>> {
        let (top_left, top_right, bottom_left, bottom_right, horizontal, vertical, _, _) = borders::SINGLE;
        
        // 顶部边框
        execute!(stdout(), SetForegroundColor(Colors::border()))?;
        execute!(stdout(), Print(top_left))?;
        
        if let Some(title) = title {
            let title_space = width.saturating_sub(4);
            if title.len() <= title_space {
                let remaining = title_space - title.len();
                let left_padding = remaining / 2;
                let right_padding = remaining - left_padding;
                
                execute!(stdout(), Print(&horizontal.repeat(left_padding)))?;
                execute!(stdout(), SetForegroundColor(Colors::accent_blue()))?;
                execute!(stdout(), Print(format!(" {} ", title)))?;
                execute!(stdout(), SetForegroundColor(Colors::border()))?;
                execute!(stdout(), Print(&horizontal.repeat(right_padding)))?;
            } else {
                execute!(stdout(), Print(&horizontal.repeat(width - 2)))?;
            }
        } else {
            execute!(stdout(), Print(&horizontal.repeat(width - 2)))?;
        }
        
        execute!(stdout(), Print(top_right), Print("\n"))?;

        // 侧边框
        for _ in 1..height-1 {
            execute!(
                stdout(),
                Print(vertical),
                Print(&" ".repeat(width - 2)),
                Print(vertical),
                Print("\n")
            )?;
        }

        // 底部边框
        execute!(
            stdout(),
            Print(bottom_left),
            Print(&horizontal.repeat(width - 2)),
            Print(bottom_right),
            Print("\n"),
            ResetColor
        )?;

        stdout().flush()?;
        Ok(())
    }

    /// 渲染圆角边框
    pub fn rounded(width: usize, height: usize, title: Option<&str>) -> Result<(), Box<dyn std::error::Error>> {
        let (top_left, top_right, bottom_left, bottom_right, horizontal, vertical, _, _) = borders::ROUNDED;
        
        // 顶部边框
        execute!(stdout(), SetForegroundColor(Colors::border()))?;
        execute!(stdout(), Print(top_left))?;
        
        if let Some(title) = title {
            let title_space = width.saturating_sub(4);
            if title.len() <= title_space {
                let remaining = title_space - title.len();
                let left_padding = remaining / 2;
                let right_padding = remaining - left_padding;
                
                execute!(stdout(), Print(&horizontal.repeat(left_padding)))?;
                execute!(stdout(), SetForegroundColor(Colors::accent_purple()))?;
                execute!(stdout(), Print(format!(" {} ", title)))?;
                execute!(stdout(), SetForegroundColor(Colors::border()))?;
                execute!(stdout(), Print(&horizontal.repeat(right_padding)))?;
            } else {
                execute!(stdout(), Print(&horizontal.repeat(width - 2)))?;
            }
        } else {
            execute!(stdout(), Print(&horizontal.repeat(width - 2)))?;
        }
        
        execute!(stdout(), Print(top_right), Print("\n"))?;

        // 侧边框
        for _ in 1..height-1 {
            execute!(
                stdout(),
                Print(vertical),
                Print(&" ".repeat(width - 2)),
                Print(vertical),
                Print("\n")
            )?;
        }

        // 底部边框
        execute!(
            stdout(),
            Print(bottom_left),
            Print(&horizontal.repeat(width - 2)),
            Print(bottom_right),
            Print("\n"),
            ResetColor
        )?;

        stdout().flush()?;
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_header_creation() {
        let header = Header::new(80, "1.0.0".to_string());
        assert_eq!(header.terminal_width, 80);
        assert_eq!(header.version, "1.0.0");
    }

    #[test]
    fn test_progress_bar() {
        let mut progress = ProgressBar::new(20, "测试".to_string());
        progress.set_progress(0.5);
        assert_eq!(progress.progress, 0.5);
    }

    #[test]
    fn test_loading_indicator() {
        let mut loader = LoadingIndicator::new("加载中".to_string());
        let initial_frame = loader.current_frame;
        loader.update();
        assert_ne!(initial_frame, loader.current_frame);
    }
}