//! # 语义分块器
//!
//! 将解析的 AST 转换为语义化的代码块。

use crate::{
    config::ParserConfig,
    error::Result,
    parser::{ChunkType, CodeChunk, ast_parser::{SyntaxTree, SymbolInfo, AstNode}},
};
use std::path::Path;
use std::collections::HashMap;

/// 语义分块器
pub struct SemanticChunker {
    config: ParserConfig,
}

impl SemanticChunker {
    /// 创建新的语义分块器
    pub fn new(config: &ParserConfig) -> Self {
        Self {
            config: config.clone(),
        }
    }

    /// 对 AST 进行语义分块
    pub fn chunk_ast(
        &self,
        ast: &SyntaxTree,
        file_path: &Path,
        language: &str,
    ) -> Result<Vec<CodeChunk>> {
        match self.config.chunking_strategy {
            crate::config::ChunkingStrategy::Semantic => {
                self.semantic_chunking(ast, file_path, language)
            }
            crate::config::ChunkingStrategy::FixedSize => {
                self.fixed_size_chunking(ast, file_path, language)
            }
            crate::config::ChunkingStrategy::SlidingWindow => {
                self.sliding_window_chunking(ast, file_path, language)
            }
            crate::config::ChunkingStrategy::Hybrid => {
                self.hybrid_chunking(ast, file_path, language)
            }
        }
    }

    /// 语义分块策略
    fn semantic_chunking(
        &self,
        ast: &SyntaxTree,
        file_path: &Path,
        language: &str,
    ) -> Result<Vec<CodeChunk>> {
        let mut chunks = Vec::new();
        let source_lines: Vec<&str> = ast.source_code.lines().collect();
        
        // 提取符号信息
        let symbols = self.extract_symbols_from_ast(ast)?;
        
        for symbol in symbols {
            let chunk = self.create_chunk_from_symbol(
                &symbol,
                &source_lines,
                file_path,
                language,
            )?;
            chunks.push(chunk);
        }

        // 如果没有符号，创建整个文件的块
        if chunks.is_empty() {
            let chunk = self.create_file_chunk(ast, file_path, language)?;
            chunks.push(chunk);
        }

        Ok(chunks)
    }

    /// 固定大小分块策略
    fn fixed_size_chunking(
        &self,
        ast: &SyntaxTree,
        file_path: &Path,
        language: &str,
    ) -> Result<Vec<CodeChunk>> {
        let mut chunks = Vec::new();
        let source_lines: Vec<&str> = ast.source_code.lines().collect();
        let chunk_size = self.config.chunk_size;
        let overlap = self.config.chunk_overlap;
        
        let mut start_line = 0;
        let mut chunk_id = 0;
        
        while start_line < source_lines.len() {
            let end_line = (start_line + chunk_size).min(source_lines.len());
            let content = source_lines[start_line..end_line].join("\n");
            
            let chunk = CodeChunk {
                id: format!("{}_{}", self.generate_file_id(file_path), chunk_id),
                file_path: file_path.to_path_buf(),
                content,
                language: language.to_string(),
                chunk_type: ChunkType::Other,
                start_line: start_line + 1,
                end_line,
                symbol_name: None,
                parent_symbol: None,
                dependencies: Vec::new(),
                metadata: HashMap::new(),
                created_at: chrono::Utc::now(),
            };
            
            chunks.push(chunk);
            chunk_id += 1;
            
            // 移动到下一个块，考虑重叠
            start_line += chunk_size - overlap;
        }

        Ok(chunks)
    }

    /// 滑动窗口分块策略
    fn sliding_window_chunking(
        &self,
        ast: &SyntaxTree,
        file_path: &Path,
        language: &str,
    ) -> Result<Vec<CodeChunk>> {
        let mut chunks = Vec::new();
        let source_lines: Vec<&str> = ast.source_code.lines().collect();
        let window_size = self.config.chunk_size;
        let step_size = self.config.chunk_size - self.config.chunk_overlap;
        
        let mut start_line = 0;
        let mut chunk_id = 0;
        
        while start_line < source_lines.len() {
            let end_line = (start_line + window_size).min(source_lines.len());
            
            // 跳过太小的块
            if end_line - start_line < window_size / 2 && start_line > 0 {
                break;
            }
            
            let content = source_lines[start_line..end_line].join("\n");
            
            let chunk = CodeChunk {
                id: format!("{}_{}", self.generate_file_id(file_path), chunk_id),
                file_path: file_path.to_path_buf(),
                content,
                language: language.to_string(),
                chunk_type: ChunkType::Other,
                start_line: start_line + 1,
                end_line,
                symbol_name: None,
                parent_symbol: None,
                dependencies: Vec::new(),
                metadata: HashMap::new(),
                created_at: chrono::Utc::now(),
            };
            
            chunks.push(chunk);
            chunk_id += 1;
            start_line += step_size;
        }

        Ok(chunks)
    }

    /// 混合分块策略
    fn hybrid_chunking(
        &self,
        ast: &SyntaxTree,
        file_path: &Path,
        language: &str,
    ) -> Result<Vec<CodeChunk>> {
        // 首先尝试语义分块
        let chunks = self.semantic_chunking(ast, file_path, language)?;
        
        // 对于过大的块，应用固定大小分块
        let mut final_chunks = Vec::new();
        
        for chunk in chunks {
            if chunk.content.len() > self.config.chunk_size * 2 {
                // 对大块进行进一步分割
                let sub_chunks = self.split_large_chunk(chunk)?;
                final_chunks.extend(sub_chunks);
            } else {
                final_chunks.push(chunk);
            }
        }

        Ok(final_chunks)
    }

    /// 分割大的代码块
    fn split_large_chunk(&self, chunk: CodeChunk) -> Result<Vec<CodeChunk>> {
        let lines: Vec<&str> = chunk.content.lines().collect();
        let chunk_size = self.config.chunk_size;
        let mut sub_chunks = Vec::new();
        
        let mut start_line = 0;
        let mut sub_id = 0;
        
        while start_line < lines.len() {
            let end_line = (start_line + chunk_size).min(lines.len());
            let content = lines[start_line..end_line].join("\n");
            
            let sub_chunk = CodeChunk {
                id: format!("{}_sub_{}", chunk.id, sub_id),
                file_path: chunk.file_path.clone(),
                content,
                language: chunk.language.clone(),
                chunk_type: chunk.chunk_type.clone(),
                start_line: chunk.start_line + start_line,
                end_line: chunk.start_line + end_line - 1,
                symbol_name: chunk.symbol_name.clone(),
                parent_symbol: chunk.parent_symbol.clone(),
                dependencies: chunk.dependencies.clone(),
                metadata: chunk.metadata.clone(),
                created_at: chunk.created_at,
            };
            
            sub_chunks.push(sub_chunk);
            sub_id += 1;
            start_line += chunk_size - self.config.chunk_overlap;
        }

        Ok(sub_chunks)
    }

    /// 从 AST 提取符号信息
    fn extract_symbols_from_ast(&self, ast: &SyntaxTree) -> Result<Vec<SymbolInfo>> {
        // 使用真正的 AST 解析来提取符号信息
        let mut symbols = Vec::new();
        self.extract_symbols_from_node(&ast.root, &mut symbols, None);
        Ok(symbols)
    }

    /// 从 AST 节点递归提取符号信息
    fn extract_symbols_from_node(
        &self,
        node: &AstNode,
        symbols: &mut Vec<SymbolInfo>,
        parent_symbol: Option<String>,
    ) {
        // 根据节点类型提取符号
        match node.node_type.as_str() {
            "function_item" | "function_declaration" | "method_definition" => {
                if let Some(symbol) = self.extract_function_symbol_from_node(node, parent_symbol.clone()) {
                    symbols.push(symbol);
                }
            }
            "struct_item" | "struct_declaration" => {
                if let Some(symbol) = self.extract_struct_symbol_from_node(node, parent_symbol.clone()) {
                    symbols.push(symbol);
                }
            }
            "enum_item" | "enum_declaration" => {
                if let Some(symbol) = self.extract_enum_symbol_from_node(node, parent_symbol.clone()) {
                    symbols.push(symbol);
                }
            }
            "impl_item" => {
                if let Some(symbol) = self.extract_impl_symbol_from_node(node, parent_symbol.clone()) {
                    symbols.push(symbol);
                }
            }
            "mod_item" | "module_declaration" => {
                if let Some(symbol) = self.extract_module_symbol_from_node(node, parent_symbol.clone()) {
                    symbols.push(symbol);
                }
            }
            "class_declaration" | "class_definition" => {
                if let Some(symbol) = self.extract_class_symbol_from_node(node, parent_symbol.clone()) {
                    symbols.push(symbol);
                }
            }
            _ => {
                // 对于其他节点类型，继续递归处理子节点
            }
        }

        // 递归处理所有子节点
        for child in &node.children {
            self.extract_symbols_from_node(child, symbols, parent_symbol.clone());
        }
    }

    /// 从 AST 节点提取函数符号
    fn extract_function_symbol_from_node(&self, node: &AstNode, parent: Option<String>) -> Option<SymbolInfo> {
        // 查找函数名标识符
        let name = self.find_identifier_in_node(node)?;

        Some(SymbolInfo {
            name,
            symbol_type: ChunkType::Function,
            start_line: node.start_line,
            end_line: node.end_line,
            parent,
            visibility: self.extract_visibility_from_node(node),
            modifiers: self.extract_modifiers_from_node(node),
        })
    }

    /// 从 AST 节点提取结构体符号
    fn extract_struct_symbol_from_node(&self, node: &AstNode, parent: Option<String>) -> Option<SymbolInfo> {
        let name = self.find_type_identifier_in_node(node)?;

        Some(SymbolInfo {
            name,
            symbol_type: ChunkType::Struct,
            start_line: node.start_line,
            end_line: node.end_line,
            parent,
            visibility: self.extract_visibility_from_node(node),
            modifiers: self.extract_modifiers_from_node(node),
        })
    }

    /// 从 AST 节点提取枚举符号
    fn extract_enum_symbol_from_node(&self, node: &AstNode, parent: Option<String>) -> Option<SymbolInfo> {
        let name = self.find_type_identifier_in_node(node)?;

        Some(SymbolInfo {
            name,
            symbol_type: ChunkType::Enum,
            start_line: node.start_line,
            end_line: node.end_line,
            parent,
            visibility: self.extract_visibility_from_node(node),
            modifiers: self.extract_modifiers_from_node(node),
        })
    }

    /// 从 AST 节点提取实现块符号
    fn extract_impl_symbol_from_node(&self, node: &AstNode, parent: Option<String>) -> Option<SymbolInfo> {
        let name = self.find_type_identifier_in_node(node)
            .unwrap_or_else(|| "impl".to_string());

        Some(SymbolInfo {
            name,
            symbol_type: ChunkType::Implementation,
            start_line: node.start_line,
            end_line: node.end_line,
            parent,
            visibility: Some("public".to_string()),
            modifiers: Vec::new(),
        })
    }

    /// 从 AST 节点提取模块符号
    fn extract_module_symbol_from_node(&self, node: &AstNode, parent: Option<String>) -> Option<SymbolInfo> {
        let name = self.find_identifier_in_node(node)?;

        Some(SymbolInfo {
            name,
            symbol_type: ChunkType::Module,
            start_line: node.start_line,
            end_line: node.end_line,
            parent,
            visibility: self.extract_visibility_from_node(node),
            modifiers: Vec::new(),
        })
    }

    /// 从 AST 节点提取类符号
    fn extract_class_symbol_from_node(&self, node: &AstNode, parent: Option<String>) -> Option<SymbolInfo> {
        let name = self.find_identifier_in_node(node)?;

        Some(SymbolInfo {
            name,
            symbol_type: ChunkType::Class,
            start_line: node.start_line,
            end_line: node.end_line,
            parent,
            visibility: self.extract_visibility_from_node(node),
            modifiers: self.extract_modifiers_from_node(node),
        })
    }

    /// 在 AST 节点中查找标识符
    fn find_identifier_in_node(&self, node: &AstNode) -> Option<String> {
        // 递归查找 identifier 节点
        if node.node_type == "identifier" {
            return Some(node.text.clone());
        }

        for child in &node.children {
            if let Some(name) = self.find_identifier_in_node(child) {
                return Some(name);
            }
        }

        None
    }

    /// 在 AST 节点中查找类型标识符
    fn find_type_identifier_in_node(&self, node: &AstNode) -> Option<String> {
        // 递归查找 type_identifier 节点
        if node.node_type == "type_identifier" {
            return Some(node.text.clone());
        }

        for child in &node.children {
            if let Some(name) = self.find_type_identifier_in_node(child) {
                return Some(name);
            }
        }

        None
    }

    /// 从 AST 节点提取可见性信息
    fn extract_visibility_from_node(&self, node: &AstNode) -> Option<String> {
        // 查找 visibility_modifier 子节点
        for child in &node.children {
            if child.node_type == "visibility_modifier" {
                return Some("public".to_string());
            }
        }
        Some("private".to_string())
    }

    /// 从 AST 节点提取修饰符
    fn extract_modifiers_from_node(&self, node: &AstNode) -> Vec<String> {
        let mut modifiers = Vec::new();

        // 查找各种修饰符
        for child in &node.children {
            match child.node_type.as_str() {
                "async" => modifiers.push("async".to_string()),
                "unsafe" => modifiers.push("unsafe".to_string()),
                "const" => modifiers.push("const".to_string()),
                "static" => modifiers.push("static".to_string()),
                "abstract" => modifiers.push("abstract".to_string()),
                "final" => modifiers.push("final".to_string()),
                _ => {}
            }
        }

        modifiers
    }







    /// 从符号创建代码块
    fn create_chunk_from_symbol(
        &self,
        symbol: &SymbolInfo,
        source_lines: &[&str],
        file_path: &Path,
        language: &str,
    ) -> Result<CodeChunk> {
        // 查找符号的实际结束行（简化处理）
        let end_line = self.find_symbol_end_line(symbol, source_lines);
        
        let content = if end_line > symbol.start_line {
            source_lines[(symbol.start_line - 1)..end_line].join("\n")
        } else {
            source_lines.get(symbol.start_line - 1)
                .unwrap_or(&"")
                .to_string()
        };

        Ok(CodeChunk {
            id: format!("{}_{}", self.generate_file_id(file_path), symbol.name),
            file_path: file_path.to_path_buf(),
            content,
            language: language.to_string(),
            chunk_type: symbol.symbol_type.clone(),
            start_line: symbol.start_line,
            end_line,
            symbol_name: Some(symbol.name.clone()),
            parent_symbol: symbol.parent.clone(),
            dependencies: Vec::new(),
            metadata: HashMap::new(),
            created_at: chrono::Utc::now(),
        })
    }

    /// 查找符号的结束行
    fn find_symbol_end_line(&self, symbol: &SymbolInfo, source_lines: &[&str]) -> usize {
        let start_line = symbol.start_line;
        let mut brace_count = 0;
        let mut found_opening_brace = false;
        
        for (i, line) in source_lines.iter().enumerate().skip(start_line - 1) {
            for ch in line.chars() {
                match ch {
                    '{' => {
                        brace_count += 1;
                        found_opening_brace = true;
                    }
                    '}' => {
                        brace_count -= 1;
                        if found_opening_brace && brace_count == 0 {
                            return i + 1;
                        }
                    }
                    _ => {}
                }
            }
        }
        
        // 如果没有找到闭合大括号，返回开始行
        start_line
    }

    /// 创建整个文件的代码块
    fn create_file_chunk(
        &self,
        ast: &SyntaxTree,
        file_path: &Path,
        language: &str,
    ) -> Result<CodeChunk> {
        Ok(CodeChunk {
            id: self.generate_file_id(file_path),
            file_path: file_path.to_path_buf(),
            content: ast.source_code.clone(),
            language: language.to_string(),
            chunk_type: ChunkType::Other,
            start_line: 1,
            end_line: ast.source_code.lines().count(),
            symbol_name: None,
            parent_symbol: None,
            dependencies: Vec::new(),
            metadata: HashMap::new(),
            created_at: chrono::Utc::now(),
        })
    }

    /// 生成文件 ID
    fn generate_file_id(&self, file_path: &Path) -> String {
        let path_str = file_path.to_string_lossy();
        format!("{:x}", md5::compute(path_str.as_bytes()))
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::parser::ast_parser::{AstNode, SyntaxTree};
    use std::collections::HashMap;

    fn create_test_ast() -> SyntaxTree {
        let source_code = r#"
fn hello() {
    println!("Hello, world!");
}

struct Person {
    name: String,
    age: u32,
}
"#.trim();

        SyntaxTree {
            root: AstNode {
                node_type: "source_file".to_string(),
                text: source_code.to_string(),
                start_byte: 0,
                end_byte: source_code.len(),
                start_line: 1,
                end_line: source_code.lines().count(),
                children: Vec::new(),
                metadata: HashMap::new(),
            },
            language: "rust".to_string(),
            source_code: source_code.to_string(),
        }
    }

    #[test]
    fn test_semantic_chunking() {
        let config = ParserConfig::default();
        let chunker = SemanticChunker::new(&config);
        let ast = create_test_ast();
        
        let chunks = chunker.chunk_ast(&ast, Path::new("test.rs"), "rust").unwrap();
        assert!(!chunks.is_empty());
    }


}