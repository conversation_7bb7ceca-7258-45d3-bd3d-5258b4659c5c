//! # 内容排序器
//!
//! 对上下文内容进行排序和优化。

use crate::{
    context::ContextFragment,
    knowledge_base::SearchResult,
};

/// 内容排序器
pub struct ContentRanker;

impl ContentRanker {
    /// 创建新的内容排序器
    pub fn new() -> Self {
        Self
    }

    /// 对上下文片段进行排序并返回排序后的向量
    pub fn rank_fragments(&self, fragments: &[ContextFragment]) -> Vec<ContextFragment> {
        let mut sorted_fragments = fragments.to_vec();
        sorted_fragments.sort_by(|a, b| {
            // 首先按重要性降序排序
            let importance_cmp = b.importance.partial_cmp(&a.importance)
                .unwrap_or(std::cmp::Ordering::Equal);

            if importance_cmp != std::cmp::Ordering::Equal {
                return importance_cmp;
            }

            // 如果重要性相同，按 token 数量升序排序（优先选择更紧凑的内容）
            a.token_count.cmp(&b.token_count)
        });
        sorted_fragments
    }

    /// 对上下文片段进行原地排序
    pub fn rank_fragments_in_place(&self, fragments: &mut [ContextFragment]) {
        fragments.sort_by(|a, b| {
            // 首先按重要性降序排序
            let importance_cmp = b.importance.partial_cmp(&a.importance)
                .unwrap_or(std::cmp::Ordering::Equal);

            if importance_cmp != std::cmp::Ordering::Equal {
                return importance_cmp;
            }

            // 如果重要性相同，按 token 数量升序排序（优先选择更紧凑的内容）
            a.token_count.cmp(&b.token_count)
        });
    }

    /// 根据多个维度对搜索结果进行排序
    pub fn rank_search_results(&self, results: &mut [SearchResult]) {
        results.sort_by(|a, b| {
            // 多维度排序权重
            let score_a = self.calculate_composite_score(a);
            let score_b = self.calculate_composite_score(b);
            
            score_b.partial_cmp(&score_a).unwrap_or(std::cmp::Ordering::Equal)
        });
    }

    /// 计算综合分数
    fn calculate_composite_score(&self, result: &SearchResult) -> f32 {
        let mut score = result.score * 0.6; // 基础相似度权重 60%
        
        // 符号名称存在性加权 15%
        if result.symbol_name.is_some() {
            score += 0.15;
        }
        
        // 代码长度合理性加权 15%
        let line_count = result.end_line - result.start_line + 1;
        let length_score = self.calculate_length_score(line_count);
        score += length_score * 0.15;
        
        // 语言优先级加权 10%
        let language_score = self.calculate_language_score(&result.language);
        score += language_score * 0.1;
        
        score.clamp(0.0, 1.0)
    }

    /// 计算代码长度分数
    fn calculate_length_score(&self, line_count: usize) -> f32 {
        match line_count {
            1..=5 => 1.0,      // 很短，可能很精确
            6..=15 => 0.9,     // 短，通常很好
            16..=30 => 0.8,    // 中等，不错
            31..=50 => 0.6,    // 较长，可能包含无关内容
            51..=100 => 0.4,   // 长，可能有太多无关内容
            _ => 0.2,          // 很长，可能不够精确
        }
    }

    /// 计算语言优先级分数
    fn calculate_language_score(&self, language: &str) -> f32 {
        match language {
            // 主流编程语言
            "rust" | "javascript" | "typescript" | "python" | "java" | "cpp" | "c" => 1.0,
            // 其他常用语言
            "go" | "swift" | "kotlin" | "scala" | "ruby" | "php" | "csharp" => 0.8,
            // 标记语言和配置
            "html" | "css" | "xml" | "json" | "yaml" | "toml" => 0.6,
            // 脚本和其他
            "shell" | "bash" | "powershell" | "sql" => 0.7,
            // 文档
            "markdown" | "text" => 0.4,
            // 未知语言
            _ => 0.3,
        }
    }

    /// 对片段进行内容质量评估
    pub fn assess_content_quality(&self, fragment: &ContextFragment) -> ContentQuality {
        let content = &fragment.content;
        
        // 评估代码完整性
        let completeness = self.assess_completeness(content);
        
        // 评估可读性
        let readability = self.assess_readability(content);
        
        // 评估信息密度
        let information_density = self.assess_information_density(content);
        
        // 评估结构化程度
        let structure_quality = self.assess_structure_quality(content);
        
        let overall_score = (completeness + readability + information_density + structure_quality) / 4.0;
        
        ContentQuality {
            overall_score,
            completeness,
            readability,
            information_density,
            structure_quality,
        }
    }

    /// 评估代码完整性
    fn assess_completeness(&self, content: &str) -> f32 {
        let lines: Vec<&str> = content.lines().collect();
        let mut completeness: f32 = 0.0;
        
        // 检查是否有开始和结束的结构
        let has_opening_brace = content.contains('{');
        let has_closing_brace = content.contains('}');
        
        if has_opening_brace && has_closing_brace {
            completeness += 0.4;
        }
        
        // 检查是否有函数/类定义
        let has_definition = lines.iter().any(|line| {
            let trimmed = line.trim();
            trimmed.starts_with("fn ") || 
            trimmed.starts_with("function ") ||
            trimmed.starts_with("def ") ||
            trimmed.starts_with("class ") ||
            trimmed.starts_with("struct ")
        });
        
        if has_definition {
            completeness += 0.3;
        }
        
        // 检查是否有完整的语句
        let statement_count = lines.iter()
            .filter(|line| {
                let trimmed = line.trim();
                !trimmed.is_empty() && 
                !trimmed.starts_with("//") && 
                !trimmed.starts_with("/*")
            })
            .count();
        
        if statement_count >= 3 {
            completeness += 0.3;
        } else if statement_count >= 1 {
            completeness += 0.1;
        }
        
        completeness.clamp(0.0, 1.0)
    }

    /// 评估可读性
    fn assess_readability(&self, content: &str) -> f32 {
        let lines: Vec<&str> = content.lines().collect();
        let mut readability = 0.0;
        
        // 检查缩进一致性
        let has_consistent_indentation = self.check_indentation_consistency(&lines);
        if has_consistent_indentation {
            readability += 0.3;
        }
        
        // 检查注释的存在
        let comment_lines = lines.iter()
            .filter(|line| {
                let trimmed = line.trim();
                trimmed.starts_with("//") || 
                trimmed.starts_with("/*") || 
                trimmed.starts_with("*") ||
                trimmed.starts_with("#")
            })
            .count();
        
        let comment_ratio = comment_lines as f32 / lines.len().max(1) as f32;
        if comment_ratio > 0.1 && comment_ratio < 0.5 {
            readability += 0.2;
        }
        
        // 检查行长度合理性
        let reasonable_line_length = lines.iter()
            .filter(|line| line.len() <= 120)
            .count() as f32 / lines.len().max(1) as f32;
        
        readability += reasonable_line_length * 0.3;
        
        // 检查空行的存在（提高可读性）
        let empty_lines = lines.iter()
            .filter(|line| line.trim().is_empty())
            .count();
        
        if empty_lines > 0 && empty_lines < lines.len() / 2 {
            readability += 0.2;
        }
        
        readability.clamp(0.0, 1.0)
    }

    /// 评估信息密度
    fn assess_information_density(&self, content: &str) -> f32 {
        let lines: Vec<&str> = content.lines().collect();
        let non_empty_lines = lines.iter()
            .filter(|line| !line.trim().is_empty())
            .count();
        
        let code_lines = lines.iter()
            .filter(|line| {
                let trimmed = line.trim();
                !trimmed.is_empty() && 
                !trimmed.starts_with("//") && 
                !trimmed.starts_with("/*") &&
                !trimmed.starts_with("*")
            })
            .count();
        
        if non_empty_lines == 0 {
            return 0.0;
        }
        
        let code_ratio = code_lines as f32 / non_empty_lines as f32;
        
        // 理想的代码与注释比例
        if code_ratio >= 0.6 && code_ratio <= 0.9 {
            1.0
        } else if code_ratio >= 0.4 {
            0.8
        } else if code_ratio >= 0.2 {
            0.6
        } else {
            0.3
        }
    }

    /// 评估结构化程度
    fn assess_structure_quality(&self, content: &str) -> f32 {
        let mut structure_score: f32 = 0.0;
        
        // 检查是否有明确的代码块结构
        let brace_pairs = self.count_brace_pairs(content);
        if brace_pairs > 0 {
            structure_score += 0.4;
        }
        
        // 检查是否有函数或类定义
        let has_structured_definition = content.contains("fn ") || 
                                       content.contains("function ") ||
                                       content.contains("def ") ||
                                       content.contains("class ") ||
                                       content.contains("struct ");
        
        if has_structured_definition {
            structure_score += 0.3;
        }
        
        // 检查是否有逻辑控制结构
        let has_control_structures = content.contains("if ") ||
                                    content.contains("for ") ||
                                    content.contains("while ") ||
                                    content.contains("match ") ||
                                    content.contains("switch ");
        
        if has_control_structures {
            structure_score += 0.3;
        }
        
        structure_score.clamp(0.0, 1.0)
    }

    /// 检查缩进一致性
    fn check_indentation_consistency(&self, lines: &[&str]) -> bool {
        let mut indent_size = None;
        
        for line in lines {
            if line.trim().is_empty() {
                continue;
            }
            
            let leading_spaces = line.len() - line.trim_start().len();
            if leading_spaces > 0 {
                if let Some(expected_size) = indent_size {
                    if leading_spaces % expected_size != 0 {
                        return false;
                    }
                } else {
                    indent_size = Some(leading_spaces);
                }
            }
        }
        
        true
    }

    /// 计算大括号对数
    fn count_brace_pairs(&self, content: &str) -> usize {
        let open_braces = content.chars().filter(|&c| c == '{').count();
        let close_braces = content.chars().filter(|&c| c == '}').count();
        open_braces.min(close_braces)
    }
}

/// 内容质量评估结果
#[derive(Debug, Clone)]
pub struct ContentQuality {
    /// 总体分数
    pub overall_score: f32,
    /// 完整性分数
    pub completeness: f32,
    /// 可读性分数
    pub readability: f32,
    /// 信息密度分数
    pub information_density: f32,
    /// 结构化程度分数
    pub structure_quality: f32,
}

impl Default for ContentRanker {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_length_score_calculation() {
        let ranker = ContentRanker::new();
        
        assert_eq!(ranker.calculate_length_score(3), 1.0);  // 很短
        assert_eq!(ranker.calculate_length_score(10), 0.9); // 短
        assert_eq!(ranker.calculate_length_score(25), 0.8); // 中等
        assert_eq!(ranker.calculate_length_score(40), 0.6); // 较长
        assert_eq!(ranker.calculate_length_score(80), 0.4); // 长
        assert_eq!(ranker.calculate_length_score(150), 0.2); // 很长
    }

    #[test]
    fn test_language_score_calculation() {
        let ranker = ContentRanker::new();
        
        assert_eq!(ranker.calculate_language_score("rust"), 1.0);
        assert_eq!(ranker.calculate_language_score("go"), 0.8);
        assert_eq!(ranker.calculate_language_score("json"), 0.6);
        assert_eq!(ranker.calculate_language_score("unknown"), 0.3);
    }

    #[test]
    fn test_brace_pair_counting() {
        let ranker = ContentRanker::new();
        
        let content1 = "fn test() { return 42; }";
        assert_eq!(ranker.count_brace_pairs(content1), 1);
        
        let content2 = "fn test() { if true { return 42; } }";
        assert_eq!(ranker.count_brace_pairs(content2), 2);
        
        let content3 = "fn incomplete() { if true {";
        assert_eq!(ranker.count_brace_pairs(content3), 0); // 不完整的对
    }
}