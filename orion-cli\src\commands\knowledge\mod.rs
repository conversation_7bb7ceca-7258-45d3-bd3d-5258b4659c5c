//! # 知识库命令模块
//!
//! 提供知识库相关的CLI命令，包括索引、搜索、备份等功能。

pub mod index;
pub mod search;
pub mod backup;
pub mod stats;

use crate::error::Result;
use clap::{Args, Subcommand};
use serde::{Deserialize, Serialize};

/// 知识库命令
#[derive(Debug, <PERSON>lone, Args, Serialize, Deserialize)]
pub struct KnowledgeCommand {
    #[command(subcommand)]
    pub action: KnowledgeAction,
}

/// 知识库子命令
#[derive(Debug, Clone, Subcommand, Serialize, Deserialize)]
pub enum KnowledgeAction {
    /// 索引管理
    Index(index::IndexCommand),
    /// 代码搜索
    Search(search::SearchCommand),
    /// 备份管理
    Backup(backup::BackupCommand),
    /// 统计信息
    Stats(stats::StatsCommand),
}

impl KnowledgeCommand {
    /// 执行知识库命令
    pub async fn execute(&self) -> Result<()> {
        match &self.action {
            KnowledgeAction::Index(cmd) => cmd.execute().await,
            KnowledgeAction::Search(cmd) => cmd.execute().await,
            KnowledgeAction::Backup(cmd) => cmd.execute().await,
            KnowledgeAction::Stats(cmd) => cmd.execute().await,
        }
    }
}