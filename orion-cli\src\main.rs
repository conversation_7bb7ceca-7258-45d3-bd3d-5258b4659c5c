//! # Orion CLI 主程序
//!
//! Orion Agent 系统的命令行界面，提供 Agent 管理、工作流执行、工具管理等功能。

mod commands;
mod error;
pub mod ui;
mod utils;

use clap::Parser;
use commands::Commands;
use error::{<PERSON><PERSON><PERSON>rror, Result};
use ui::{UIManager, components::Header};
use serde::{Deserialize, Serialize};
use std::process;
use std::sync::Arc;
use std::sync::atomic::{AtomicBool, Ordering};
use tracing::{error, info, warn};
use tracing_subscriber::{fmt, EnvFilter};

/// Orion CLI - 智能 Agent 系统命令行工具
#[derive(Debug, Parser, Serialize, Deserialize)]
#[command(
    name = "orion",
    version = env!("CARGO_PKG_VERSION"),
    about = "Orion Agent 系统 - 智能 Agent 平台的命令行工具",
    long_about = "Orion 是一个强大的智能 Agent 系统，支持多模态交互、工作流自动化、工具集成等功能。\n\n\
                  主要功能：\n\
                  • Agent 管理和交互\n\
                  • 工作流设计和执行\n\
                  • 工具注册和管理\n\
                  • 配置管理\n\
                  • 系统监控和统计\n\n\
                  快速开始: \n\
                  - 直接运行 'orion' 启动交互模式\n\
                  - 或者使用 'orion run' 启动",
    author = "Orion Team",
    help_template = "{before-help}{name} {version}\n{author-with-newline}{about-with-newline}\n{usage-heading} {usage}\n\n{all-args}{after-help}"
)]
struct Cli {
    /// 子命令
    #[command(subcommand)]
    command: Option<Commands>,
    
    /// 详细输出
    #[arg(short, long, global = true, help = "启用详细输出")]
    verbose: bool,
    
    /// 静默模式
    #[arg(short, long, global = true, help = "静默模式，只输出错误信息")]
    quiet: bool,
    
    /// 日志级别
    #[arg(
        long,
        global = true,
        value_parser = ["trace", "debug", "info", "warn", "error"],
        help = "设置日志级别"
    )]
    log_level: Option<String>,
    
    /// 配置文件路径
    #[arg(
        short,
        long,
        global = true,
        default_value = "orion.toml",
        help = "指定配置文件路径"
    )]
    config: String,
    
    /// 工作目录
    #[arg(
        long,
        global = true,
        help = "设置工作目录"
    )]
    workdir: Option<String>,
    
    /// 输出格式
    #[arg(
        long,
        global = true,
        value_parser = ["text", "json", "yaml"],
        default_value = "text",
        help = "设置输出格式"
    )]
    output_format: String,
    
    /// 禁用颜色输出
    #[arg(
        long,
        global = true,
        help = "禁用颜色输出"
    )]
    no_color: bool,
    
    /// 启用性能分析
    #[arg(
        long,
        global = true,
        help = "启用性能分析"
    )]
    profile: bool,
}

#[tokio::main]
async fn main() {
    // 设置全局的退出标志
    let shutdown_flag = Arc::new(AtomicBool::new(false));
    let shutdown_flag_clone = shutdown_flag.clone();
    
    // 设置 Ctrl+C 信号处理器
    tokio::spawn(async move {
        match tokio::signal::ctrl_c().await {
            Ok(()) => {
                println!("\n🛑 收到中断信号 (Ctrl+C)，正在优雅退出...");
                shutdown_flag_clone.store(true, Ordering::SeqCst);
                
                // 给一点时间让程序优雅退出
                tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;
                
                println!("👋 再见！");
                process::exit(0);
            }
            Err(err) => {
                eprintln!("设置 Ctrl+C 处理器失败: {}", err);
            }
        }
    });
    
    // 解析命令行参数
    let cli = Cli::parse();
    
    // 初始化日志系统
    if let Err(e) = init_logging(&cli) {
        eprintln!("初始化日志系统失败: {}", e);
        process::exit(1);
    }
    
    // 设置工作目录
    if let Some(workdir) = &cli.workdir {
        if let Err(e) = std::env::set_current_dir(workdir) {
            error!("设置工作目录失败: {}", e);
            process::exit(1);
        }
        info!("工作目录设置为: {}", workdir);
    }
    
    // 设置环境变量
    setup_environment(&cli);
    
    // 显示启动信息
    if !cli.quiet {
        print_banner();
        info!("Orion CLI v{} 启动", env!("CARGO_PKG_VERSION"));
        info!("配置文件: {}", cli.config);
    }
    
    // 执行命令
    let start_time = std::time::Instant::now();
    let result = execute_command(&cli).await;
    let execution_time = start_time.elapsed();
    
    // 处理执行结果
    match result {
        Ok(()) => {
            if cli.profile {
                info!("命令执行完成，耗时: {:.2}秒", execution_time.as_secs_f64());
            }
            if !cli.quiet {
                info!("操作成功完成");
            }
        }
        Err(e) => {
            error!("命令执行失败: {}", e);
            
            // 根据错误类型设置不同的退出码
            let exit_code = match e {
                CliError::ConfigError { .. } => 2,
                CliError::InvalidArgument { .. } => 3,
                CliError::IoError { .. } => 4,
                CliError::SerializationError { .. } => 5,
                CliError::ExecutionError { .. } => 6,
                CliError::NetworkError { .. } => 7,
                CliError::AuthenticationError { .. } => 8,
                CliError::PermissionError { .. } => 9,
                CliError::TimeoutError { .. } => 10,
                CliError::Other { .. } => 1,
                CliError::InitializationError { .. } => 11,
                CliError::SystemError { .. } => 12,
            };
            
            if !cli.quiet {
                eprintln!("\n❌ 错误: {}", e);
                
                // 提供帮助信息
                match e {
                    CliError::ConfigError { .. } => {
                        eprintln!("💡 提示: 请检查配置文件是否存在且格式正确");
                        eprintln!("   可以使用 'orion config init' 创建默认配置");
                    }
                    CliError::InvalidArgument { .. } => {
                        eprintln!("💡 提示: 请检查命令参数是否正确");
                        eprintln!("   可以使用 'orion --help' 查看帮助信息");
                    }
                    CliError::PermissionError { .. } => {
                        eprintln!("💡 提示: 请检查文件权限或以管理员身份运行");
                    }
                    _ => {}
                }
            }
            
            process::exit(exit_code);
        }
    }
}

/// 初始化日志系统
fn init_logging(cli: &Cli) -> Result<()> {
    let log_level = if cli.quiet {
        "error"
    } else if cli.verbose {
        "debug"
    } else {
        cli.log_level.as_deref().unwrap_or("info")
    };
    
    let env_filter = EnvFilter::try_new(format!("orion={},orion_core={},orion_cli={}", log_level, log_level, log_level))
        .map_err(|e| CliError::Other {
            error: format!("创建日志过滤器失败: {}", e),
        })?;
    
    let subscriber = fmt::Subscriber::builder()
        .with_env_filter(env_filter)
        .with_target(false)
        .with_thread_ids(cli.verbose)
        .with_thread_names(cli.verbose)
        .with_file(cli.verbose)
        .with_line_number(cli.verbose)
        .with_ansi(!cli.no_color && atty::is(atty::Stream::Stderr))
        .finish();
    
    tracing::subscriber::set_global_default(subscriber)
        .map_err(|e| CliError::Other {
            error: format!("设置全局日志订阅者失败: {}", e),
        })?;
    
    Ok(())
}

/// 设置环境变量
fn setup_environment(cli: &Cli) {
    // 设置配置文件路径
    std::env::set_var("ORION_CONFIG", &cli.config);
    
    // 设置输出格式
    std::env::set_var("ORION_OUTPUT_FORMAT", &cli.output_format);
    
    // 设置颜色输出
    if cli.no_color {
        std::env::set_var("NO_COLOR", "1");
    }
    
    // 设置详细模式
    if cli.verbose {
        std::env::set_var("ORION_VERBOSE", "1");
    }
    
    // 设置静默模式
    if cli.quiet {
        std::env::set_var("ORION_QUIET", "1");
    }
    
    // 设置性能分析
    if cli.profile {
        std::env::set_var("ORION_PROFILE", "1");
    }
}

/// 打印启动横幅
fn print_banner() {
    if atty::is(atty::Stream::Stdout) {
        // 初始化颜色系统
        ui::colors::init_global_theme_manager();
        
        // 创建 UI 管理器
        if let Ok(ui_manager) = UIManager::new() {
            let (width, _) = ui_manager.get_terminal_size();
            
            // 创建并渲染头部
            let header = Header::new(width, env!("CARGO_PKG_VERSION").to_string());
            if let Err(e) = header.render() {
                eprintln!("渲染头部失败: {}", e);
                // 回退到简单横幅
                print_simple_banner();
            }
        } else {
            // 回退到简单横幅
            print_simple_banner();
        }
    }
}

/// 打印简单横幅（回退方案）
fn print_simple_banner() {
    println!(r#"
   ____       _             
  / __ \     (_)            
 | |  | |_ __ _  ___  _ __  
 | |  | | '__| |/ _ \| '_ \ 
 | |__| | |  | | (_) | | | |
  \____/|_|  |_|\___/|_| |_|
                           
"#);
    println!("🚀 Orion Agent 系统 v{}", env!("CARGO_PKG_VERSION"));
    println!("🔗 https://github.com/orion-ai/orion");
    println!();
}

/// 执行命令
async fn execute_command(cli: &Cli) -> Result<()> {
    // 如果没有提供子命令，默认启动交互模式
    let command = match &cli.command {
        Some(cmd) => cmd.clone(),
        None => {
            // 显示友好的启动消息
            if !cli.quiet {
                println!("🎉 没有指定子命令，自动启动交互模式...");
                println!("💡 提示: 使用 'orion --help' 查看所有可用命令\n");
            }
            
            // 创建默认的 run 命令（交互模式）
            Commands::Run(commands::run::RunCommand {
                config: Some(cli.config.clone()),
                name: "orion-agent".to_string(),
                interactive: true,
                verbose: cli.verbose,
                log_level: cli.log_level.clone().unwrap_or_else(|| "info".to_string()),
                sandbox: true,
                max_concurrent_tasks: 10,
                workdir: cli.workdir.clone(),
                command: None,
                file: None,
                output_format: cli.output_format.clone(),
                stream: true,  // 默认启用流式模式
                no_stream: false,
                typing_speed: 30,
            })
        }
    };

    // 验证配置文件（除了 config init 命令）
    if !matches!(command, Commands::Config(ref cmd) if matches!(cmd.action, commands::config::ConfigAction::Init { .. })) {
        validate_config_file(&cli.config)?;
    }
    
    // 执行命令 - 需要可变引用
    let mut command = command;
    command.execute().await.map_err(|e| {
        // 添加上下文信息
        match e {
            CliError::ConfigError { error } => CliError::ConfigError {
                error: format!("配置文件 '{}': {}", cli.config, error),
            },
            other => other,
        }
    })
}

/// 验证配置文件
fn validate_config_file(config_path: &str) -> Result<()> {
    let path = std::path::Path::new(config_path);
    
    if !path.exists() {
        return Err(CliError::ConfigError {
            error: format!(
                "配置文件不存在: {}\n💡 提示: 使用 'orion config init' 创建默认配置文件",
                config_path
            ),
        });
    }
    
    if !path.is_file() {
        return Err(CliError::ConfigError {
            error: format!("配置路径不是文件: {}", config_path),
        });
    }
    
    // 检查文件权限
    let metadata = std::fs::metadata(path)
        .map_err(|e| CliError::IoError {
            error: format!("读取配置文件元数据失败: {}", e),
        })?;
    
    if metadata.len() == 0 {
        warn!("配置文件为空: {}", config_path);
    }
    
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    use clap::Parser;
    
    #[test]
    fn test_cli_parsing() {
        // 测试基本命令解析
        let args = vec!["orion", "version"];
        let cli = Cli::try_parse_from(args);
        assert!(cli.is_ok());
        
        // 测试带参数的命令
        let args = vec!["orion", "--verbose", "--config", "test.toml", "run", "--help"];
        let cli = Cli::try_parse_from(args);
        assert!(cli.is_ok());
        
        if let Ok(cli) = cli {
            assert!(cli.verbose);
            assert_eq!(cli.config, "test.toml");
        }
    }
    
    #[test]
    fn test_global_flags() {
        let args = vec![
            "orion",
            "--verbose",
            "--quiet",
            "--no-color",
            "--profile",
            "--log-level", "debug",
            "--output-format", "json",
            "version"
        ];
        
        let cli = Cli::try_parse_from(args).unwrap();
        assert!(cli.verbose);
        assert!(cli.quiet);
        assert!(cli.no_color);
        assert!(cli.profile);
        assert_eq!(cli.log_level, Some("debug".to_string()));
        assert_eq!(cli.output_format, "json");
    }
    
    #[test]
    fn test_validate_config_file() {
        // 测试不存在的文件
        let result = validate_config_file("nonexistent.toml");
        assert!(result.is_err());
        
        // 测试当前文件（应该存在）
        let _result = validate_config_file("Cargo.toml");
        // 这个测试可能会失败，取决于运行环境
    }
    
    #[tokio::test]
    async fn test_command_execution() {
        let cli = Cli {
            command: Some(Commands::Version(commands::version::VersionCommand {
                verbose: false,
                format: "text".to_string(),
                check_updates: false,
            })),
            verbose: false,
            quiet: true,
            log_level: None,
            config: "test.toml".to_string(),
            workdir: None,
            output_format: "text".to_string(),
            no_color: false,
            profile: false,
        };
        
        // 这个测试需要模拟环境，实际项目中可能需要更复杂的测试设置
        // let result = execute_command(&cli).await;
        // 由于依赖外部配置，这里只测试结构
        assert_eq!(cli.output_format, "text");
    }
}