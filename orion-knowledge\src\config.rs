//! # 知识库配置
//!
//! 管理知识库系统的所有配置选项。

use serde::{Deserialize, Serialize};
use std::path::PathBuf;

/// 知识库配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Config {
    /// 数据库配置
    pub database: DatabaseConfig,
    /// 解析器配置
    pub parser: ParserConfig,
    /// 嵌入模型配置
    pub embedding: EmbeddingConfig,
    /// 查询配置
    pub query: QueryConfig,
    /// 上下文配置
    pub context: ContextConfig,
}

/// 数据库配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DatabaseConfig {
    /// 数据库文件路径
    pub path: PathBuf,
    /// 连接池大小
    pub pool_size: u32,
    /// 查询超时时间（秒）
    pub query_timeout: u64,
    /// 是否启用 WAL 模式
    pub enable_wal: bool,
    /// 缓存大小（MB）
    pub cache_size_mb: usize,
}

/// 解析器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ParserConfig {
    /// 支持的语言列表
    pub supported_languages: Vec<String>,
    /// 最大文件大小（字节）
    pub max_file_size: usize,
    /// 分块策略
    pub chunking_strategy: ChunkingStrategy,
    /// 分块大小
    pub chunk_size: usize,
    /// 分块重叠大小
    pub chunk_overlap: usize,
}

/// 嵌入模型配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EmbeddingConfig {
    /// 模型类型
    pub model_type: EmbeddingModelType,
    /// 模型路径或 API 端点
    pub model_path: String,
    /// 向量维度
    pub dimension: usize,
    /// 批处理大小
    pub batch_size: usize,
    /// API 密钥（如果使用远程服务）
    pub api_key: Option<String>,
}

/// 查询配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QueryConfig {
    /// 默认搜索结果数量
    pub default_top_k: usize,
    /// 最大搜索结果数量
    pub max_top_k: usize,
    /// 相似度阈值
    pub similarity_threshold: f32,
    /// 混合搜索权重（向量搜索 vs 关键词搜索）
    pub hybrid_weight: f32,
}

/// 上下文配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ContextConfig {
    /// 最大上下文长度（tokens）
    pub max_context_length: usize,
    /// 上下文窗口大小
    pub context_window: usize,
    /// 是否启用智能压缩
    pub enable_compression: bool,
    /// 压缩比例
    pub compression_ratio: f32,
}

/// 分块策略
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ChunkingStrategy {
    /// 基于 AST 的语义分块
    Semantic,
    /// 固定大小分块
    FixedSize,
    /// 滑动窗口分块
    SlidingWindow,
    /// 混合策略
    Hybrid,
}

/// 嵌入模型类型
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum EmbeddingModelType {
    /// 本地模型
    Local,
    /// OpenAI API
    OpenAI,
    /// Ollama 本地服务
    Ollama,
    /// 自定义 API
    Custom,
}

impl Default for Config {
    fn default() -> Self {
        Self {
            database: DatabaseConfig::default(),
            parser: ParserConfig::default(),
            embedding: EmbeddingConfig::default(),
            query: QueryConfig::default(),
            context: ContextConfig::default(),
        }
    }
}

impl Default for DatabaseConfig {
    fn default() -> Self {
        Self {
            path: PathBuf::from("./.orion/knowledge.db"),
            pool_size: 10,
            query_timeout: 30,
            enable_wal: true,
            cache_size_mb: 64,
        }
    }
}

impl Default for ParserConfig {
    fn default() -> Self {
        Self {
            supported_languages: vec![
                "rust".to_string(),
                "javascript".to_string(),
                "typescript".to_string(),
                "python".to_string(),
                "java".to_string(),
                "cpp".to_string(),
                "c".to_string(),
                "go".to_string(),
            ],
            max_file_size: 10 * 1024 * 1024, // 10MB
            chunking_strategy: ChunkingStrategy::Semantic,
            chunk_size: 1000,
            chunk_overlap: 200,
        }
    }
}

impl Default for EmbeddingConfig {
    fn default() -> Self {
        Self {
            model_type: EmbeddingModelType::Ollama,
            model_path: "http://localhost:11434".to_string(),
            dimension: 384,
            batch_size: 32,
            api_key: None,
        }
    }
}

impl Default for QueryConfig {
    fn default() -> Self {
        Self {
            default_top_k: 10,
            max_top_k: 100,
            similarity_threshold: 0.7,
            hybrid_weight: 0.7, // 70% 向量搜索，30% 关键词搜索
        }
    }
}

impl Default for ContextConfig {
    fn default() -> Self {
        Self {
            max_context_length: 4096,
            context_window: 2048,
            enable_compression: true,
            compression_ratio: 0.8,
        }
    }
}

impl Config {
    /// 从文件加载配置
    pub fn from_file(path: impl AsRef<std::path::Path>) -> crate::Result<Self> {
        let content = std::fs::read_to_string(path)?;
        let config: Config = toml::from_str(&content)
            .map_err(|e| crate::KnowledgeError::config_error(format!("解析配置文件失败: {}", e)))?;
        Ok(config)
    }

    /// 保存配置到文件
    pub fn save_to_file(&self, path: impl AsRef<std::path::Path>) -> crate::Result<()> {
        let content = toml::to_string_pretty(self)
            .map_err(|e| crate::KnowledgeError::config_error(format!("序列化配置失败: {}", e)))?;
        std::fs::write(path, content)?;
        Ok(())
    }

    /// 验证配置的有效性
    pub fn validate(&self) -> crate::Result<()> {
        // 验证数据库配置
        if self.database.pool_size == 0 {
            return Err(crate::KnowledgeError::config_error("数据库连接池大小不能为0"));
        }

        // 验证解析器配置
        if self.parser.supported_languages.is_empty() {
            return Err(crate::KnowledgeError::config_error("支持的语言列表不能为空"));
        }

        // 验证嵌入配置
        if self.embedding.dimension == 0 {
            return Err(crate::KnowledgeError::config_error("向量维度不能为0"));
        }

        // 验证查询配置
        if self.query.similarity_threshold < 0.0 || self.query.similarity_threshold > 1.0 {
            return Err(crate::KnowledgeError::config_error("相似度阈值必须在0-1之间"));
        }

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_default_config() {
        let config = Config::default();
        assert!(config.validate().is_ok());
    }

    #[test]
    fn test_config_validation() {
        let mut config = Config::default();
        config.database.pool_size = 0;
        assert!(config.validate().is_err());
    }
}