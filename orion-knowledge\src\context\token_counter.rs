//! # Token 计数器
//!
//! 估算文本的 token 数量。

/// Token 计数器
pub struct TokenCounter {
    /// 每个 token 的平均字符数
    chars_per_token: f32,
}

impl TokenCounter {
    /// 创建新的 token 计数器
    pub fn new() -> Self {
        Self {
            chars_per_token: 4.0, // 英文文本大约每 4 个字符一个 token
        }
    }

    /// 计算文本的 token 数量
    pub fn count_tokens(&self, text: &str) -> usize {
        if text.is_empty() {
            return 0;
        }

        // 使用多种方法估算 token 数量，取最大值作为保守估计
        let char_based = self.count_by_characters(text);
        let word_based = self.count_by_words(text);
        let line_based = self.count_by_lines(text);
        
        // 返回最大值作为安全估计
        char_based.max(word_based).max(line_based)
    }

    /// 基于字符数计算 token
    fn count_by_characters(&self, text: &str) -> usize {
        let char_count = text.chars().count();
        ((char_count as f32) / self.chars_per_token).ceil() as usize
    }

    /// 基于单词数计算 token
    fn count_by_words(&self, text: &str) -> usize {
        let words: Vec<&str> = text.split_whitespace().collect();
        let mut token_count = 0;
        
        for word in words {
            // 长单词可能被分成多个 token
            if word.len() > 12 {
                token_count += (word.len() as f32 / 6.0).ceil() as usize;
            } else if word.len() > 6 {
                token_count += 2;
            } else {
                token_count += 1;
            }
        }
        
        token_count
    }

    /// 基于行数计算 token（代码特定）
    fn count_by_lines(&self, text: &str) -> usize {
        let lines: Vec<&str> = text.lines().collect();
        let mut token_count = 0;
        
        for line in lines {
            let trimmed = line.trim();
            if trimmed.is_empty() {
                continue;
            }
            
            // 代码行通常包含更多 token
            if self.is_code_line(trimmed) {
                token_count += self.count_code_line_tokens(trimmed);
            } else {
                // 注释行
                token_count += self.count_by_words(trimmed);
            }
        }
        
        token_count
    }

    /// 判断是否为代码行
    fn is_code_line(&self, line: &str) -> bool {
        let trimmed = line.trim();
        
        // 注释行
        if trimmed.starts_with("//") || 
           trimmed.starts_with("/*") || 
           trimmed.starts_with("*") ||
           trimmed.starts_with("#") {
            return false;
        }
        
        // 包含代码特征的行
        trimmed.contains('{') || 
        trimmed.contains('}') ||
        trimmed.contains(';') ||
        trimmed.contains('(') ||
        trimmed.contains(')') ||
        trimmed.contains('=') ||
        trimmed.contains("fn ") ||
        trimmed.contains("function ") ||
        trimmed.contains("def ") ||
        trimmed.contains("class ") ||
        trimmed.contains("struct ")
    }

    /// 计算代码行的 token 数量
    fn count_code_line_tokens(&self, line: &str) -> usize {
        let mut token_count = 0;
        
        // 分割符号和关键词
        let symbols = ['(', ')', '{', '}', '[', ']', ';', ',', '.', ':', '=', '+', '-', '*', '/', '&', '|', '!', '<', '>'];
        
        let mut current_word = String::new();
        
        for ch in line.chars() {
            if ch.is_whitespace() {
                if !current_word.is_empty() {
                    token_count += self.count_word_tokens(&current_word);
                    current_word.clear();
                }
            } else if symbols.contains(&ch) {
                if !current_word.is_empty() {
                    token_count += self.count_word_tokens(&current_word);
                    current_word.clear();
                }
                token_count += 1; // 符号本身是一个 token
            } else {
                current_word.push(ch);
            }
        }
        
        // 处理最后一个单词
        if !current_word.is_empty() {
            token_count += self.count_word_tokens(&current_word);
        }
        
        token_count.max(1) // 至少一个 token
    }

    /// 计算单个单词的 token 数量
    fn count_word_tokens(&self, word: &str) -> usize {
        if word.is_empty() {
            return 0;
        }
        
        // 检查是否为字符串字面量
        if (word.starts_with('"') && word.ends_with('"')) ||
           (word.starts_with('\'') && word.ends_with('\'')) {
            // 字符串内容按字符数计算
            let content_len = word.len().saturating_sub(2);
            return ((content_len as f32) / self.chars_per_token).ceil().max(1.0) as usize;
        }
        
        // 检查是否为数字
        if word.chars().all(|c| c.is_ascii_digit() || c == '.' || c == '_') {
            return 1;
        }
        
        // 普通标识符
        if word.len() <= 8 {
            1
        } else if word.len() <= 16 {
            2
        } else {
            ((word.len() as f32) / 8.0).ceil() as usize
        }
    }

    /// 估算批量文本的总 token 数
    pub fn count_batch_tokens(&self, texts: &[String]) -> usize {
        texts.iter()
            .map(|text| self.count_tokens(text))
            .sum()
    }

    /// 检查文本是否超过 token 限制
    pub fn exceeds_limit(&self, text: &str, limit: usize) -> bool {
        self.count_tokens(text) > limit
    }

    /// 截断文本以适应 token 限制
    pub fn truncate_to_limit(&self, text: &str, limit: usize) -> String {
        if !self.exceeds_limit(text, limit) {
            return text.to_string();
        }
        
        let lines: Vec<&str> = text.lines().collect();
        let mut result = String::new();
        let mut current_tokens = 0;
        
        for line in lines {
            let line_tokens = self.count_tokens(line);
            if current_tokens + line_tokens > limit {
                break;
            }
            
            if !result.is_empty() {
                result.push('\n');
            }
            result.push_str(line);
            current_tokens += line_tokens;
        }
        
        if result.len() < text.len() {
            result.push_str("\n\n... (truncated)");
        }
        
        result
    }

    /// 设置每个 token 的平均字符数
    pub fn set_chars_per_token(&mut self, chars_per_token: f32) {
        self.chars_per_token = chars_per_token.max(1.0);
    }
}

impl Default for TokenCounter {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_empty_text() {
        let counter = TokenCounter::new();
        assert_eq!(counter.count_tokens(""), 0);
    }

    #[test]
    fn test_simple_text() {
        let counter = TokenCounter::new();
        let tokens = counter.count_tokens("hello world");
        assert!(tokens >= 2);
    }

    #[test]
    fn test_code_line_detection() {
        let counter = TokenCounter::new();
        
        assert!(counter.is_code_line("fn main() {"));
        assert!(counter.is_code_line("let x = 42;"));
        assert!(counter.is_code_line("return result;"));
        
        assert!(!counter.is_code_line("// This is a comment"));
        assert!(!counter.is_code_line("/* Block comment */"));
        assert!(!counter.is_code_line("# Python comment"));
    }

    #[test]
    fn test_code_line_token_counting() {
        let counter = TokenCounter::new();
        
        let tokens = counter.count_code_line_tokens("fn main() {");
        assert!(tokens >= 3); // fn, main, (, ), {
        
        let tokens = counter.count_code_line_tokens("let x = 42;");
        assert!(tokens >= 4); // let, x, =, 42, ;
    }

    #[test]
    fn test_word_token_counting() {
        let counter = TokenCounter::new();
        
        assert_eq!(counter.count_word_tokens("hello"), 1);
        assert_eq!(counter.count_word_tokens("verylongidentifiername"), 3);
        assert_eq!(counter.count_word_tokens("\"hello world\""), 3); // 字符串内容
        assert_eq!(counter.count_word_tokens("123"), 1);
    }

    #[test]
    fn test_batch_counting() {
        let counter = TokenCounter::new();
        
        let texts = vec![
            "hello world".to_string(),
            "fn main() {}".to_string(),
        ];
        
        let total = counter.count_batch_tokens(&texts);
        assert!(total > 0);
    }

    #[test]
    fn test_truncation() {
        let counter = TokenCounter::new();
        
        let long_text = "hello world\nthis is a long text\nthat should be truncated";
        let truncated = counter.truncate_to_limit(long_text, 5);
        
        assert!(truncated.len() < long_text.len());
        assert!(counter.count_tokens(&truncated) <= 5);
    }

    #[test]
    fn test_limit_checking() {
        let counter = TokenCounter::new();
        
        let text = "hello world test";
        assert!(!counter.exceeds_limit(text, 100));
        assert!(counter.exceeds_limit(text, 1));
    }
}