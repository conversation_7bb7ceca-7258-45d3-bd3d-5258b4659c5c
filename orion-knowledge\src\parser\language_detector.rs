//! # 语言检测器
//!
//! 根据文件扩展名和内容检测编程语言。

use crate::error::Result;
use std::collections::HashMap;
use std::path::Path;

/// 语言检测器
pub struct LanguageDetector {
    /// 扩展名到语言的映射
    extension_map: HashMap<String, String>,
    /// 语言特征模式
    pattern_map: HashMap<String, Vec<String>>,
    /// 语言权重模式（更精确的检测）
    weighted_patterns: HashMap<String, Vec<(String, f32)>>,
    /// 文件名模式
    filename_patterns: HashMap<String, String>,
}

impl LanguageDetector {
    /// 创建新的语言检测器
    pub fn new() -> Self {
        let mut extension_map = HashMap::new();
        let mut pattern_map = HashMap::new();
        let mut weighted_patterns = HashMap::new();
        let mut filename_patterns = HashMap::new();

        // 注册文件扩展名映射
        Self::register_extensions(&mut extension_map);

        // 注册语言特征模式
        Self::register_patterns(&mut pattern_map);

        // 注册权重模式
        Self::register_weighted_patterns(&mut weighted_patterns);

        // 注册文件名模式
        Self::register_filename_patterns(&mut filename_patterns);

        Self {
            extension_map,
            pattern_map,
            weighted_patterns,
            filename_patterns,
        }
    }

    /// 检测编程语言
    pub fn detect_language(&self, file_path: &Path, content: &str) -> Result<String> {
        // 1. 首先尝试通过文件名模式检测（如 Makefile, Dockerfile 等）
        if let Some(language) = self.detect_language_by_filename(file_path) {
            return Ok(language);
        }

        // 2. 尝试通过扩展名检测
        if let Some(language) = self.detect_language_by_extension(file_path) {
            // 如果是模糊的扩展名（如 .h），使用内容验证
            if self.is_ambiguous_extension(file_path) {
                if let Some(content_language) = self.detect_language_by_weighted_content(content) {
                    return Ok(content_language);
                }
            }
            return Ok(language);
        }

        // 3. 尝试通过 shebang 检测
        if let Some(language) = self.detect_language_by_shebang(content) {
            return Ok(language);
        }

        // 4. 尝试通过权重内容检测
        if let Some(language) = self.detect_language_by_weighted_content(content) {
            return Ok(language);
        }

        // 5. 如果权重检测失败，尝试传统的内容检测
        if let Some(language) = self.detect_language_by_content(content) {
            return Ok(language);
        }

        // 默认返回未知语言
        Err(crate::KnowledgeError::parse_error(format!(
            "无法检测文件 {} 的编程语言",
            file_path.display()
        )))
    }

    /// 通过文件名模式检测语言
    fn detect_language_by_filename(&self, file_path: &Path) -> Option<String> {
        let filename = file_path.file_name()?.to_str()?.to_lowercase();
        self.filename_patterns.get(&filename).cloned()
    }

    /// 通过文件扩展名检测语言
    pub fn detect_language_by_extension(&self, file_path: &Path) -> Option<String> {
        let extension = file_path
            .extension()?
            .to_str()?
            .to_lowercase();

        self.extension_map.get(&extension).cloned()
    }

    /// 检查是否是模糊的扩展名
    fn is_ambiguous_extension(&self, file_path: &Path) -> bool {
        if let Some(ext) = file_path.extension().and_then(|e| e.to_str()) {
            matches!(ext.to_lowercase().as_str(), "h" | "hpp" | "hxx" | "c" | "cc")
        } else {
            false
        }
    }

    /// 通过 shebang 检测语言
    fn detect_language_by_shebang(&self, content: &str) -> Option<String> {
        let first_line = content.lines().next()?;
        if !first_line.starts_with("#!") {
            return None;
        }

        let shebang = first_line.to_lowercase();

        if shebang.contains("python") {
            Some("python".to_string())
        } else if shebang.contains("node") || shebang.contains("javascript") {
            Some("javascript".to_string())
        } else if shebang.contains("bash") || shebang.contains("sh") {
            Some("shell".to_string())
        } else if shebang.contains("ruby") {
            Some("ruby".to_string())
        } else if shebang.contains("perl") {
            Some("perl".to_string())
        } else if shebang.contains("php") {
            Some("php".to_string())
        } else {
            None
        }
    }

    /// 通过权重内容检测语言
    fn detect_language_by_weighted_content(&self, content: &str) -> Option<String> {
        let mut language_scores: HashMap<String, f32> = HashMap::new();

        // 预处理内容：移除注释和字符串字面量以提高准确性
        let cleaned_content = self.clean_content_for_analysis(content);

        for (language, patterns) in &self.weighted_patterns {
            let mut score = 0.0;

            for (pattern, weight) in patterns {
                let count = cleaned_content.matches(pattern).count() as f32;
                score += count * weight;
            }

            // 根据内容长度标准化分数
            let normalized_score = score / (cleaned_content.len() as f32 / 1000.0).max(1.0);

            if normalized_score > 0.0 {
                language_scores.insert(language.clone(), normalized_score);
            }
        }

        // 返回分数最高的语言，但需要达到最小阈值
        language_scores
            .into_iter()
            .filter(|(_, score)| *score > 0.5) // 最小阈值
            .max_by(|(_, a), (_, b)| a.partial_cmp(b).unwrap_or(std::cmp::Ordering::Equal))
            .map(|(language, _)| language)
    }

    /// 清理内容以便分析（移除注释和字符串）
    fn clean_content_for_analysis(&self, content: &str) -> String {
        let mut cleaned = String::new();
        let mut in_string = false;
        let mut in_single_comment = false;
        let mut in_multi_comment = false;
        let mut chars = content.chars().peekable();

        while let Some(ch) = chars.next() {
            match ch {
                '"' if !in_single_comment && !in_multi_comment => {
                    in_string = !in_string;
                }
                '/' if !in_string => {
                    if let Some(&next_ch) = chars.peek() {
                        match next_ch {
                            '/' => {
                                in_single_comment = true;
                                chars.next(); // 消费 '/'
                                continue;
                            }
                            '*' => {
                                in_multi_comment = true;
                                chars.next(); // 消费 '*'
                                continue;
                            }
                            _ => {}
                        }
                    }
                }
                '*' if in_multi_comment => {
                    if let Some(&'/') = chars.peek() {
                        in_multi_comment = false;
                        chars.next(); // 消费 '/'
                        continue;
                    }
                }
                '\n' => {
                    in_single_comment = false;
                    if !in_string && !in_multi_comment {
                        cleaned.push(ch);
                    }
                }
                _ => {
                    if !in_string && !in_single_comment && !in_multi_comment {
                        cleaned.push(ch);
                    }
                }
            }
        }

        cleaned
    }

    /// 通过文件内容检测语言（传统方法）
    pub fn detect_language_by_content(&self, content: &str) -> Option<String> {
        let content_lower = content.to_lowercase();
        let mut language_scores: HashMap<String, usize> = HashMap::new();

        // 计算每种语言的匹配分数
        for (language, patterns) in &self.pattern_map {
            let mut score = 0;

            for pattern in patterns {
                if content_lower.contains(pattern) {
                    score += 1;
                }
            }

            if score > 0 {
                language_scores.insert(language.clone(), score);
            }
        }

        // 返回分数最高的语言
        language_scores
            .into_iter()
            .max_by_key(|(_, score)| *score)
            .map(|(language, _)| language)
    }

    /// 检查是否支持该语言
    pub fn is_supported(&self, language: &str) -> bool {
        self.extension_map.values().any(|lang| lang == language) ||
        self.pattern_map.contains_key(language)
    }

    /// 获取支持的语言列表
    pub fn supported_languages(&self) -> Vec<String> {
        let mut languages: Vec<String> = self.extension_map.values().cloned().collect();
        languages.sort();
        languages.dedup();
        languages
    }

    /// 注册文件扩展名映射
    fn register_extensions(map: &mut HashMap<String, String>) {
        // Rust
        map.insert("rs".to_string(), "rust".to_string());
        
        // JavaScript/TypeScript
        map.insert("js".to_string(), "javascript".to_string());
        map.insert("mjs".to_string(), "javascript".to_string());
        map.insert("jsx".to_string(), "javascript".to_string());
        map.insert("ts".to_string(), "typescript".to_string());
        map.insert("tsx".to_string(), "typescript".to_string());
        
        // Python
        map.insert("py".to_string(), "python".to_string());
        map.insert("pyw".to_string(), "python".to_string());
        map.insert("pyi".to_string(), "python".to_string());
        
        // Java
        map.insert("java".to_string(), "java".to_string());
        
        // C/C++
        map.insert("c".to_string(), "c".to_string());
        map.insert("h".to_string(), "c".to_string());
        map.insert("cpp".to_string(), "cpp".to_string());
        map.insert("cxx".to_string(), "cpp".to_string());
        map.insert("cc".to_string(), "cpp".to_string());
        map.insert("hpp".to_string(), "cpp".to_string());
        map.insert("hxx".to_string(), "cpp".to_string());
        
        // Go
        map.insert("go".to_string(), "go".to_string());
        
        // C#
        map.insert("cs".to_string(), "csharp".to_string());
        
        // Swift
        map.insert("swift".to_string(), "swift".to_string());
        
        // Kotlin
        map.insert("kt".to_string(), "kotlin".to_string());
        map.insert("kts".to_string(), "kotlin".to_string());
        
        // PHP
        map.insert("php".to_string(), "php".to_string());
        
        // Ruby
        map.insert("rb".to_string(), "ruby".to_string());
        
        // Scala
        map.insert("scala".to_string(), "scala".to_string());
        
        // Shell
        map.insert("sh".to_string(), "shell".to_string());
        map.insert("bash".to_string(), "shell".to_string());
        map.insert("zsh".to_string(), "shell".to_string());
        
        // SQL
        map.insert("sql".to_string(), "sql".to_string());
        
        // YAML
        map.insert("yml".to_string(), "yaml".to_string());
        map.insert("yaml".to_string(), "yaml".to_string());
        
        // JSON
        map.insert("json".to_string(), "json".to_string());
        
        // XML
        map.insert("xml".to_string(), "xml".to_string());
        
        // HTML
        map.insert("html".to_string(), "html".to_string());
        map.insert("htm".to_string(), "html".to_string());
        
        // CSS
        map.insert("css".to_string(), "css".to_string());
        
        // Markdown
        map.insert("md".to_string(), "markdown".to_string());
        map.insert("markdown".to_string(), "markdown".to_string());
    }

    /// 注册文件名模式
    fn register_filename_patterns(map: &mut HashMap<String, String>) {
        // 常见的特殊文件名
        map.insert("makefile".to_string(), "makefile".to_string());
        map.insert("dockerfile".to_string(), "dockerfile".to_string());
        map.insert("vagrantfile".to_string(), "ruby".to_string());
        map.insert("jenkinsfile".to_string(), "groovy".to_string());
        map.insert("cmakelists.txt".to_string(), "cmake".to_string());
        map.insert(".gitignore".to_string(), "gitignore".to_string());
        map.insert(".gitattributes".to_string(), "gitattributes".to_string());
        map.insert(".editorconfig".to_string(), "editorconfig".to_string());
        map.insert("package.json".to_string(), "json".to_string());
        map.insert("cargo.toml".to_string(), "toml".to_string());
        map.insert("go.mod".to_string(), "go".to_string());
        map.insert("requirements.txt".to_string(), "pip".to_string());
    }

    /// 注册权重模式
    fn register_weighted_patterns(map: &mut HashMap<String, Vec<(String, f32)>>) {
        // Rust 权重模式
        map.insert("rust".to_string(), vec![
            ("fn ".to_string(), 2.0),
            ("struct ".to_string(), 2.0),
            ("enum ".to_string(), 2.0),
            ("impl ".to_string(), 2.5),
            ("trait ".to_string(), 3.0),
            ("pub ".to_string(), 1.0),
            ("mod ".to_string(), 2.0),
            ("use ".to_string(), 0.5),
            ("let ".to_string(), 0.5),
            ("mut ".to_string(), 1.0),
            ("match ".to_string(), 1.5),
            ("Result<".to_string(), 2.0),
            ("Option<".to_string(), 2.0),
            ("Vec<".to_string(), 1.0),
            ("println!".to_string(), 1.0),
            ("unwrap()".to_string(), 2.0),
            ("expect(".to_string(), 2.0),
            ("#[derive(".to_string(), 3.0),
            ("crate::".to_string(), 3.0),
        ]);

        // C++ 权重模式
        map.insert("cpp".to_string(), vec![
            ("class ".to_string(), 2.0),
            ("template<".to_string(), 3.0),
            ("namespace ".to_string(), 2.5),
            ("std::".to_string(), 2.0),
            ("public:".to_string(), 2.0),
            ("private:".to_string(), 2.0),
            ("protected:".to_string(), 2.0),
            ("virtual ".to_string(), 2.0),
            ("const ".to_string(), 1.0),
            ("static ".to_string(), 1.0),
            ("auto ".to_string(), 1.5),
            ("new ".to_string(), 1.0),
            ("delete ".to_string(), 1.5),
            ("nullptr".to_string(), 2.0),
            ("cout <<".to_string(), 1.5),
            ("cin >>".to_string(), 1.5),
            ("#include".to_string(), 1.0),
            ("operator".to_string(), 2.5),
            ("->".to_string(), 1.0),
            ("::".to_string(), 1.0),
        ]);

        // Python 权重模式
        map.insert("python".to_string(), vec![
            ("def ".to_string(), 2.0),
            ("class ".to_string(), 2.0),
            ("import ".to_string(), 1.0),
            ("from ".to_string(), 1.0),
            ("if __name__".to_string(), 3.0),
            ("self.".to_string(), 1.5),
            ("__init__".to_string(), 2.5),
            ("__str__".to_string(), 2.0),
            ("print(".to_string(), 1.0),
            ("return ".to_string(), 0.5),
            ("for ".to_string(), 0.5),
            ("in ".to_string(), 0.5),
            ("elif ".to_string(), 1.5),
            ("except:".to_string(), 1.5),
            ("try:".to_string(), 1.0),
            ("with ".to_string(), 1.5),
            ("as ".to_string(), 1.0),
            ("lambda ".to_string(), 2.0),
            ("@".to_string(), 2.0),  // 装饰器
            ("->".to_string(), 2.0),  // 类型注解
        ]);

        // JavaScript 权重模式
        map.insert("javascript".to_string(), vec![
            ("function ".to_string(), 2.0),
            ("const ".to_string(), 1.0),
            ("let ".to_string(), 1.0),
            ("var ".to_string(), 0.5),
            ("class ".to_string(), 2.0),
            ("extends ".to_string(), 2.0),
            ("export ".to_string(), 1.5),
            ("import ".to_string(), 1.5),
            ("require(".to_string(), 2.0),
            ("module.exports".to_string(), 2.5),
            ("console.log".to_string(), 1.0),
            ("=> ".to_string(), 1.5),
            ("async ".to_string(), 1.5),
            ("await ".to_string(), 1.5),
            ("Promise".to_string(), 2.0),
            ("document.".to_string(), 2.0),
            ("window.".to_string(), 2.0),
            ("addEventListener".to_string(), 2.0),
            ("this.".to_string(), 1.0),
            ("new ".to_string(), 1.0),
        ]);
    }

    /// 注册语言特征模式
    fn register_patterns(map: &mut HashMap<String, Vec<String>>) {
        // Rust 特征
        map.insert("rust".to_string(), vec![
            "fn ".to_string(),
            "struct ".to_string(),
            "enum ".to_string(),
            "impl ".to_string(),
            "trait ".to_string(),
            "use ".to_string(),
            "mod ".to_string(),
            "pub ".to_string(),
            "let ".to_string(),
            "mut ".to_string(),
            "match ".to_string(),
            "Result<".to_string(),
            "Option<".to_string(),
            "Vec<".to_string(),
            "println!".to_string(),
        ]);

        // JavaScript 特征
        map.insert("javascript".to_string(), vec![
            "function ".to_string(),
            "const ".to_string(),
            "let ".to_string(),
            "var ".to_string(),
            "class ".to_string(),
            "extends ".to_string(),
            "export ".to_string(),
            "import ".to_string(),
            "require(".to_string(),
            "console.log".to_string(),
            "=> ".to_string(),
            "typeof ".to_string(),
            "instanceof ".to_string(),
        ]);

        // TypeScript 特征
        map.insert("typescript".to_string(), vec![
            "interface ".to_string(),
            "type ".to_string(),
            "enum ".to_string(),
            ": string".to_string(),
            ": number".to_string(),
            ": boolean".to_string(),
            ": void".to_string(),
            "generic<".to_string(),
            "implements ".to_string(),
            "abstract ".to_string(),
            "readonly ".to_string(),
        ]);

        // Python 特征
        map.insert("python".to_string(), vec![
            "def ".to_string(),
            "class ".to_string(),
            "import ".to_string(),
            "from ".to_string(),
            "if __name__".to_string(),
            "elif ".to_string(),
            "print(".to_string(),
            "self.".to_string(),
            "__init__".to_string(),
            "__str__".to_string(),
            "lambda ".to_string(),
            "yield ".to_string(),
        ]);

        // Java 特征
        map.insert("java".to_string(), vec![
            "public class ".to_string(),
            "private ".to_string(),
            "protected ".to_string(),
            "public ".to_string(),
            "static ".to_string(),
            "final ".to_string(),
            "abstract ".to_string(),
            "interface ".to_string(),
            "extends ".to_string(),
            "implements ".to_string(),
            "package ".to_string(),
            "import ".to_string(),
            "System.out.println".to_string(),
        ]);

        // C 特征
        map.insert("c".to_string(), vec![
            "#include".to_string(),
            "#define".to_string(),
            "int main".to_string(),
            "printf(".to_string(),
            "scanf(".to_string(),
            "malloc(".to_string(),
            "free(".to_string(),
            "struct ".to_string(),
            "typedef ".to_string(),
            "static ".to_string(),
            "extern ".to_string(),
        ]);

        // C++ 特征
        map.insert("cpp".to_string(), vec![
            "#include".to_string(),
            "class ".to_string(),
            "namespace ".to_string(),
            "using namespace".to_string(),
            "std::".to_string(),
            "cout <<".to_string(),
            "cin >>".to_string(),
            "public:".to_string(),
            "private:".to_string(),
            "protected:".to_string(),
            "virtual ".to_string(),
            "template<".to_string(),
        ]);

        // Go 特征
        map.insert("go".to_string(), vec![
            "package ".to_string(),
            "import (".to_string(),
            "func ".to_string(),
            "type ".to_string(),
            "struct {".to_string(),
            "interface {".to_string(),
            "var ".to_string(),
            "const ".to_string(),
            "go ".to_string(),
            "defer ".to_string(),
            "range ".to_string(),
            "fmt.Print".to_string(),
        ]);
    }
}

impl Default for LanguageDetector {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::path::PathBuf;

    #[test]
    fn test_extension_detection() {
        let detector = LanguageDetector::new();
        
        assert_eq!(
            detector.detect_language_by_extension(&PathBuf::from("test.rs")),
            Some("rust".to_string())
        );
        
        assert_eq!(
            detector.detect_language_by_extension(&PathBuf::from("test.js")),
            Some("javascript".to_string())
        );
        
        assert_eq!(
            detector.detect_language_by_extension(&PathBuf::from("test.py")),
            Some("python".to_string())
        );
    }

    #[test]
    fn test_content_detection() {
        let detector = LanguageDetector::new();
        
        let rust_code = r#"
            fn main() {
                println!("Hello, world!");
            }
        "#;
        assert_eq!(
            detector.detect_language_by_content(rust_code),
            Some("rust".to_string())
        );

        let js_code = r#"
            function hello() {
                console.log("Hello, world!");
            }
        "#;
        assert_eq!(
            detector.detect_language_by_content(js_code),
            Some("javascript".to_string())
        );

        let python_code = r#"
            def hello():
                print("Hello, world!")
        "#;
        assert_eq!(
            detector.detect_language_by_content(python_code),
            Some("python".to_string())
        );
    }

    #[test]
    fn test_full_detection() {
        let detector = LanguageDetector::new();
        
        let rust_code = r#"
            fn main() {
                println!("Hello, world!");
            }
        "#;
        
        let result = detector.detect_language(&PathBuf::from("test.rs"), rust_code);
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), "rust");
    }

    #[test]
    fn test_supported_languages() {
        let detector = LanguageDetector::new();
        let languages = detector.supported_languages();
        
        assert!(languages.contains(&"rust".to_string()));
        assert!(languages.contains(&"javascript".to_string()));
        assert!(languages.contains(&"python".to_string()));
    }
}