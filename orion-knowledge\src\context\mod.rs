//! # 上下文合成模块
//!
//! 提供上下文信息合成和智能压缩功能。

pub mod context_builder;
pub mod content_ranker;
pub mod token_counter;

use crate::{
    config::ContextConfig,
    error::Result,
    knowledge_base::{SearchResult, ContextInfo},
};

/// 上下文片段
#[derive(Debug, Clone)]
pub struct ContextFragment {
    /// 内容
    pub content: String,
    /// 重要性分数
    pub importance: f32,
    /// 来源信息
    pub source: FragmentSource,
    /// Token 数量
    pub token_count: usize,
}

/// 片段来源
#[derive(Debug, Clone)]
pub struct FragmentSource {
    /// 代码块 ID
    pub chunk_id: String,
    /// 文件路径
    pub file_path: String,
    /// 符号名称
    pub symbol_name: Option<String>,
    /// 行号范围
    pub line_range: (usize, usize),
}

/// 上下文合成器
pub struct ContextSynthesizer {
    config: ContextConfig,
    builder: context_builder::ContextBuilder,
    ranker: content_ranker::ContentRanker,
    token_counter: token_counter::TokenCounter,
}

impl ContextSynthesizer {
    /// 创建新的上下文合成器
    pub fn new(config: &ContextConfig) -> Result<Self> {
        Ok(Self {
            config: config.clone(),
            builder: context_builder::ContextBuilder::new(config),
            ranker: content_ranker::ContentRanker::new(),
            token_counter: token_counter::TokenCounter::new(),
        })
    }

    /// 合成上下文信息
    pub async fn synthesize_context(
        &self,
        search_results: &[SearchResult],
    ) -> Result<ContextInfo> {
        if search_results.is_empty() {
            return Ok(ContextInfo {
                chunks: Vec::new(),
                synthesized_context: String::new(),
                token_count: 0,
                confidence: 0.0,
            });
        }

        // 1. 构建上下文片段
        let fragments = self.build_fragments(search_results).await?;
        
        // 2. 排序和选择重要片段  
        let selected_fragments = self.select_important_fragments(fragments).await?;
        
        // 3. 合成最终上下文
        let synthesized_context = self.synthesize_final_context(&selected_fragments).await?;
        
        // 4. 计算 token 数量和置信度
        let token_count = self.token_counter.count_tokens(&synthesized_context);
        let confidence = self.calculate_confidence(search_results, &selected_fragments);

        Ok(ContextInfo {
            chunks: search_results.to_vec(),
            synthesized_context,
            token_count,
            confidence,
        })
    }

    /// 构建上下文片段
    async fn build_fragments(
        &self,
        search_results: &[SearchResult],
    ) -> Result<Vec<ContextFragment>> {
        // 使用 builder 来构建上下文片段
        let fragments = self.builder.build_context_fragments(search_results);
        Ok(fragments)
    }



    /// 选择重要的上下文片段
    async fn select_important_fragments(
        &self,
        fragments: Vec<ContextFragment>,
    ) -> Result<Vec<ContextFragment>> {
        // 使用 ranker 来排序和选择重要片段
        let ranked_fragments = self.ranker.rank_fragments(&fragments);

        let mut selected = Vec::new();
        let mut total_tokens = 0;

        for fragment in ranked_fragments {
            // 检查是否超过最大上下文长度
            if total_tokens + fragment.token_count > self.config.max_context_length {
                if self.config.enable_compression {
                    // 尝试压缩内容
                    if let Ok(compressed) = self.compress_fragment(fragment).await {
                        if total_tokens + compressed.token_count <= self.config.max_context_length {
                            total_tokens += compressed.token_count;
                            selected.push(compressed);
                            continue; // 继续处理下一个片段
                        }
                    }
                }
                break;
            }

            total_tokens += fragment.token_count;
            selected.push(fragment);
        }

        Ok(selected)
    }

    /// 压缩上下文片段
    async fn compress_fragment(
        &self,
        fragment: ContextFragment,
    ) -> Result<ContextFragment> {
        let target_length = (fragment.content.len() as f32 * self.config.compression_ratio) as usize;
        
        // 简单的压缩策略：保留关键行
        let lines: Vec<&str> = fragment.content.lines().collect();
        let compressed_lines = self.select_key_lines(&lines, target_length);
        let compressed_content = compressed_lines.join("\n");
        
        let compressed_token_count = self.token_counter.count_tokens(&compressed_content);
        
        Ok(ContextFragment {
            content: compressed_content,
            importance: fragment.importance * 0.9, // 轻微降低重要性
            source: fragment.source,
            token_count: compressed_token_count,
        })
    }

    /// 选择关键行
    fn select_key_lines<'a>(&self, lines: &[&'a str], target_length: usize) -> Vec<&'a str> {
        if lines.len() * 20 <= target_length {
            return lines.to_vec();
        }
        
        let mut selected = Vec::new();
        let keep_ratio = target_length as f32 / (lines.len() * 20) as f32;
        let keep_count = (lines.len() as f32 * keep_ratio).max(1.0) as usize;
        
        // 优先保留函数定义、类定义等关键行
        for (i, line) in lines.iter().enumerate() {
            if selected.len() >= keep_count {
                break;
            }
            
            let trimmed = line.trim();
            if trimmed.starts_with("fn ") ||
               trimmed.starts_with("class ") ||
               trimmed.starts_with("function ") ||
               trimmed.starts_with("def ") ||
               trimmed.starts_with("struct ") ||
               trimmed.starts_with("enum ") ||
               trimmed.starts_with("interface ") ||
               i == 0 || i == lines.len() - 1 {
                selected.push(*line);
            }
        }
        
        // 如果关键行不够，随机选择其他行
        if selected.len() < keep_count {
            for line in lines.iter() {
                if selected.len() >= keep_count {
                    break;
                }
                if !selected.contains(line) {
                    selected.push(*line);
                }
            }
        }
        
        selected
    }

    /// 合成最终上下文
    async fn synthesize_final_context(
        &self,
        fragments: &[ContextFragment],
    ) -> Result<String> {
        if fragments.is_empty() {
            return Ok(String::new());
        }

        let mut context = String::new();
        context.push_str("=== Relevant Code Context ===\n\n");

        for (i, fragment) in fragments.iter().enumerate() {
            context.push_str(&format!("## Context {} (Importance: {:.2})\n", i + 1, fragment.importance));
            context.push_str(&fragment.content);
            context.push_str("\n\n");
        }

        context.push_str("=== End of Context ===\n");
        Ok(context)
    }

    /// 计算置信度
    fn calculate_confidence(
        &self,
        search_results: &[SearchResult],
        selected_fragments: &[ContextFragment],
    ) -> f32 {
        if search_results.is_empty() {
            return 0.0;
        }

        // 基于搜索结果的平均分数
        let avg_score: f32 = search_results.iter()
            .map(|r| r.score)
            .sum::<f32>() / search_results.len() as f32;

        // 基于选中片段的重要性
        let avg_importance: f32 = selected_fragments.iter()
            .map(|f| f.importance)
            .sum::<f32>() / selected_fragments.len().max(1) as f32;

        // 覆盖率（选中的片段数量占搜索结果的比例）
        let coverage = selected_fragments.len() as f32 / search_results.len() as f32;

        // 综合计算置信度
        (avg_score * 0.5 + avg_importance * 0.3 + coverage * 0.2).clamp(0.0, 1.0)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    fn create_test_search_result() -> SearchResult {
        SearchResult {
            chunk_id: "test".to_string(),
            file_path: "test.rs".to_string(),
            content: "fn hello() {\n    println!(\"Hello, world!\");\n}".to_string(),
            score: 0.9,
            language: "rust".to_string(),
            symbol_name: Some("hello".to_string()),
            start_line: 1,
            end_line: 3,
        }
    }

    #[tokio::test]
    async fn test_context_synthesis() {
        let config = ContextConfig::default();
        let synthesizer = ContextSynthesizer::new(&config).unwrap();
        
        let results = vec![create_test_search_result()];
        let context = synthesizer.synthesize_context(&results).await.unwrap();
        
        assert!(!context.synthesized_context.is_empty());
        assert!(context.confidence > 0.0);
        assert_eq!(context.chunks.len(), 1);
    }

    #[test]
    fn test_content_ranking() {
        let config = ContextConfig::default();
        let synthesizer = ContextSynthesizer::new(&config).unwrap();

        let result = create_test_search_result();
        let fragments = vec![ContextFragment {
            content: result.content.clone(),
            importance: result.score,
            source: FragmentSource {
                chunk_id: result.chunk_id.clone(),
                file_path: result.file_path.clone(),
                symbol_name: result.symbol_name.clone(),
                line_range: (result.start_line, result.end_line),
            },
            token_count: 50,
        }];

        let ranked = synthesizer.ranker.rank_fragments(&fragments);
        assert_eq!(ranked.len(), 1);
        assert!(ranked[0].importance > 0.0);
    }
}