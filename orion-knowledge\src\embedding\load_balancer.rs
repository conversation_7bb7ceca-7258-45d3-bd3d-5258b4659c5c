//! # 嵌入模型负载均衡器
//!
//! 实现多种负载均衡策略，智能分发请求到最合适的模型实例。

use crate::{
    error::Result,
    embedding::{
        Embedding, BatchEmbeddingResult,
        model_registry::{ModelRegistry, ModelSelectionCriteria},
        model_lifecycle::ModelLifecycleManager,
    },
    KnowledgeError,
};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use tracing::{debug, info, warn};
use rand::Rng;

/// 负载均衡策略
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum LoadBalancingStrategy {
    /// 轮询
    RoundRobin,
    /// 加权轮询
    WeightedRoundRobin,
    /// 最少连接
    LeastConnections,
    /// 最快响应
    FastestResponse,
    /// 随机
    Random,
    /// 加权随机
    WeightedRandom,
    /// 一致性哈希
    ConsistentHash,
    /// 基于性能的智能路由
    PerformanceBased,
}

/// 负载均衡配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoadBalancerConfig {
    /// 负载均衡策略
    pub strategy: LoadBalancingStrategy,
    /// 健康检查间隔（秒）
    pub health_check_interval_seconds: u64,
    /// 故障转移启用
    pub enable_failover: bool,
    /// 最大重试次数
    pub max_retries: u32,
    /// 重试延迟（毫秒）
    pub retry_delay_ms: u64,
    /// 断路器配置
    pub circuit_breaker: CircuitBreakerConfig,
}

impl Default for LoadBalancerConfig {
    fn default() -> Self {
        Self {
            strategy: LoadBalancingStrategy::PerformanceBased,
            health_check_interval_seconds: 60,
            enable_failover: true,
            max_retries: 3,
            retry_delay_ms: 100,
            circuit_breaker: CircuitBreakerConfig::default(),
        }
    }
}

/// 断路器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CircuitBreakerConfig {
    /// 失败阈值
    pub failure_threshold: u32,
    /// 恢复超时（秒）
    pub recovery_timeout_seconds: u64,
    /// 半开状态下的测试请求数
    pub half_open_max_calls: u32,
}

impl Default for CircuitBreakerConfig {
    fn default() -> Self {
        Self {
            failure_threshold: 5,
            recovery_timeout_seconds: 30,
            half_open_max_calls: 3,
        }
    }
}

/// 断路器状态
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum CircuitBreakerState {
    /// 关闭（正常）
    Closed,
    /// 开启（故障）
    Open,
    /// 半开（测试）
    HalfOpen,
}

/// 模型断路器
#[derive(Debug)]
pub struct ModelCircuitBreaker {
    /// 当前状态
    state: CircuitBreakerState,
    /// 失败计数
    failure_count: u32,
    /// 最后失败时间
    last_failure_time: Option<Instant>,
    /// 半开状态下的调用计数
    half_open_calls: u32,
    /// 配置
    config: CircuitBreakerConfig,
}

impl ModelCircuitBreaker {
    /// 创建新的断路器
    pub fn new(config: CircuitBreakerConfig) -> Self {
        Self {
            state: CircuitBreakerState::Closed,
            failure_count: 0,
            last_failure_time: None,
            half_open_calls: 0,
            config,
        }
    }
    
    /// 检查是否允许请求
    pub fn can_execute(&mut self) -> bool {
        match self.state {
            CircuitBreakerState::Closed => true,
            CircuitBreakerState::Open => {
                // 检查是否可以转换到半开状态
                if let Some(last_failure) = self.last_failure_time {
                    if last_failure.elapsed().as_secs() >= self.config.recovery_timeout_seconds {
                        self.state = CircuitBreakerState::HalfOpen;
                        self.half_open_calls = 0;
                        debug!("断路器转换到半开状态");
                        return true;
                    }
                }
                false
            }
            CircuitBreakerState::HalfOpen => {
                self.half_open_calls < self.config.half_open_max_calls
            }
        }
    }
    
    /// 记录成功
    pub fn record_success(&mut self) {
        match self.state {
            CircuitBreakerState::Closed => {
                self.failure_count = 0;
            }
            CircuitBreakerState::HalfOpen => {
                self.half_open_calls += 1;
                if self.half_open_calls >= self.config.half_open_max_calls {
                    self.state = CircuitBreakerState::Closed;
                    self.failure_count = 0;
                    debug!("断路器转换到关闭状态");
                }
            }
            CircuitBreakerState::Open => {
                // 不应该在开启状态下记录成功
            }
        }
    }
    
    /// 记录失败
    pub fn record_failure(&mut self) {
        self.failure_count += 1;
        self.last_failure_time = Some(Instant::now());
        
        match self.state {
            CircuitBreakerState::Closed => {
                if self.failure_count >= self.config.failure_threshold {
                    self.state = CircuitBreakerState::Open;
                    debug!("断路器转换到开启状态");
                }
            }
            CircuitBreakerState::HalfOpen => {
                self.state = CircuitBreakerState::Open;
                debug!("断路器从半开转换到开启状态");
            }
            CircuitBreakerState::Open => {
                // 已经是开启状态
            }
        }
    }
}

/// 负载均衡器
pub struct LoadBalancer {
    /// 配置
    config: LoadBalancerConfig,
    /// 模型注册表
    registry: Arc<ModelRegistry>,
    /// 模型生命周期管理器
    lifecycle_manager: Arc<ModelLifecycleManager>,
    /// 轮询计数器
    round_robin_counter: Arc<RwLock<usize>>,
    /// 模型连接计数
    connection_counts: Arc<RwLock<HashMap<String, u32>>>,
    /// 断路器
    circuit_breakers: Arc<RwLock<HashMap<String, ModelCircuitBreaker>>>,
}

impl LoadBalancer {
    /// 创建新的负载均衡器
    pub fn new(
        config: LoadBalancerConfig,
        registry: Arc<ModelRegistry>,
        lifecycle_manager: Arc<ModelLifecycleManager>,
    ) -> Self {
        info!("初始化负载均衡器，策略: {:?}", config.strategy);
        
        Self {
            config,
            registry,
            lifecycle_manager,
            round_robin_counter: Arc::new(RwLock::new(0)),
            connection_counts: Arc::new(RwLock::new(HashMap::new())),
            circuit_breakers: Arc::new(RwLock::new(HashMap::new())),
        }
    }
    
    /// 选择最佳模型
    pub async fn select_model(&self, criteria: &ModelSelectionCriteria) -> Result<String> {
        let available_models = self.get_available_models(criteria).await;
        
        if available_models.is_empty() {
            return Err(KnowledgeError::embedding_error("没有可用的模型"));
        }
        
        let selected_model = match self.config.strategy {
            LoadBalancingStrategy::RoundRobin => {
                self.select_round_robin(&available_models).await
            }
            LoadBalancingStrategy::WeightedRoundRobin => {
                self.select_weighted_round_robin(&available_models).await
            }
            LoadBalancingStrategy::LeastConnections => {
                self.select_least_connections(&available_models).await
            }
            LoadBalancingStrategy::FastestResponse => {
                self.select_fastest_response(&available_models).await
            }
            LoadBalancingStrategy::Random => {
                self.select_random(&available_models).await
            }
            LoadBalancingStrategy::WeightedRandom => {
                self.select_weighted_random(&available_models).await
            }
            LoadBalancingStrategy::ConsistentHash => {
                self.select_consistent_hash(&available_models, criteria).await
            }
            LoadBalancingStrategy::PerformanceBased => {
                self.select_performance_based(&available_models).await
            }
        };
        
        // 增加连接计数
        self.increment_connection_count(&selected_model).await;
        
        Ok(selected_model)
    }
    
    /// 执行嵌入计算（带负载均衡）
    pub async fn embed_text(&self, text: &str, criteria: &ModelSelectionCriteria) -> Result<Embedding> {
        let mut retries = 0;
        
        while retries <= self.config.max_retries {
            let model_id = self.select_model(criteria).await?;
            
            // 检查断路器
            if !self.can_execute_on_model(&model_id).await {
                retries += 1;
                continue;
            }
            
            let start_time = Instant::now();
            
            // 这里需要实际的模型实例调用
            // 由于 ModelInstance 没有实现 Clone，我们需要重新设计这部分
            // 暂时返回一个模拟的结果
            match self.try_embed_text(&model_id, text).await {
                Ok(embedding) => {
                    let duration = start_time.elapsed();
                    self.record_success(&model_id, duration).await;
                    self.decrement_connection_count(&model_id).await;
                    return Ok(embedding);
                }
                Err(e) => {
                    let duration = start_time.elapsed();
                    self.record_failure(&model_id, duration).await;
                    self.decrement_connection_count(&model_id).await;
                    
                    if !self.config.enable_failover || retries >= self.config.max_retries {
                        return Err(e);
                    }
                    
                    warn!("模型 {} 请求失败，尝试故障转移: {}", model_id, e);
                    retries += 1;
                    
                    if self.config.retry_delay_ms > 0 {
                        tokio::time::sleep(Duration::from_millis(self.config.retry_delay_ms)).await;
                    }
                }
            }
        }
        
        Err(KnowledgeError::embedding_error("所有重试都失败了"))
    }
    
    /// 批量嵌入计算（带负载均衡）
    pub async fn embed_batch(&self, texts: &[String], criteria: &ModelSelectionCriteria) -> Result<BatchEmbeddingResult> {
        // 可以实现更复杂的批量负载均衡策略
        // 比如将批次分割到多个模型上并行处理
        
        let model_id = self.select_model(criteria).await?;
        
        if !self.can_execute_on_model(&model_id).await {
            return Err(KnowledgeError::embedding_error("模型不可用"));
        }
        
        let start_time = Instant::now();
        
        match self.try_embed_batch(&model_id, texts).await {
            Ok(result) => {
                let duration = start_time.elapsed();
                self.record_success(&model_id, duration).await;
                Ok(result)
            }
            Err(e) => {
                let duration = start_time.elapsed();
                self.record_failure(&model_id, duration).await;
                Err(e)
            }
        }
    }
    
    /// 获取负载均衡统计信息
    pub async fn get_stats(&self) -> LoadBalancerStats {
        let connection_counts = self.connection_counts.read().await;
        let circuit_breakers = self.circuit_breakers.read().await;

        let mut stats = LoadBalancerStats {
            strategy: self.config.strategy.clone(),
            total_connections: connection_counts.values().sum(),
            model_connections: connection_counts.clone(),
            circuit_breaker_states: HashMap::new(),
        };

        for (model_id, breaker) in circuit_breakers.iter() {
            stats.circuit_breaker_states.insert(model_id.clone(), breaker.state.clone());
        }

        stats
    }

    /// 获取可用模型列表
    async fn get_available_models(&self, criteria: &ModelSelectionCriteria) -> Vec<String> {
        let all_models = self.lifecycle_manager.list_active_models().await;
        let mut available_models = Vec::new();

        for model_id in all_models {
            // 检查断路器状态
            if self.can_execute_on_model(&model_id).await {
                // 检查模型是否符合条件
                if let Some(config) = self.registry.get_model_config(&model_id).await {
                    let matches_criteria =
                        (criteria.dimension.is_none() || config.dimension == criteria.dimension.unwrap()) &&
                        (criteria.model_type.is_none() || config.model_type == *criteria.model_type.as_ref().unwrap()) &&
                        criteria.required_tags.iter().all(|(key, value)| {
                            config.tags.get(key) == Some(value)
                        });

                    if matches_criteria {
                        available_models.push(model_id);
                    }
                }
            }
        }

        available_models
    }

    /// 轮询选择
    async fn select_round_robin(&self, models: &[String]) -> String {
        let mut counter = self.round_robin_counter.write().await;
        let index = *counter % models.len();
        *counter = (*counter + 1) % models.len();
        models[index].clone()
    }

    /// 加权轮询选择
    async fn select_weighted_round_robin(&self, models: &[String]) -> String {
        // 简化实现，实际应该考虑权重
        self.select_round_robin(models).await
    }

    /// 最少连接选择
    async fn select_least_connections(&self, models: &[String]) -> String {
        let connection_counts = self.connection_counts.read().await;

        models.iter()
            .min_by_key(|model_id| connection_counts.get(*model_id).unwrap_or(&0))
            .unwrap()
            .clone()
    }

    /// 最快响应选择
    async fn select_fastest_response(&self, models: &[String]) -> String {
        let mut best_model = models[0].clone();
        let mut best_time = f64::MAX;

        for model_id in models {
            if let Some(metrics) = self.registry.get_model_metrics(model_id).await {
                if metrics.avg_response_time_ms < best_time {
                    best_time = metrics.avg_response_time_ms;
                    best_model = model_id.clone();
                }
            }
        }

        best_model
    }

    /// 随机选择
    async fn select_random(&self, models: &[String]) -> String {
        let mut rng = rand::thread_rng();
        let index = rng.gen_range(0..models.len());
        models[index].clone()
    }

    /// 加权随机选择
    async fn select_weighted_random(&self, models: &[String]) -> String {
        // 简化实现，实际应该考虑权重
        self.select_random(models).await
    }

    /// 一致性哈希选择
    async fn select_consistent_hash(&self, models: &[String], criteria: &ModelSelectionCriteria) -> String {
        // 简化实现，基于条件的哈希
        let hash_input = format!("{:?}", criteria);
        let hash = hash_input.len() % models.len();
        models[hash].clone()
    }

    /// 基于性能的选择
    async fn select_performance_based(&self, models: &[String]) -> String {
        let mut best_model = models[0].clone();
        let mut best_score = f64::MIN;

        for model_id in models {
            if let Some(metrics) = self.registry.get_model_metrics(model_id).await {
                // 综合评分：考虑响应时间、错误率、吞吐量
                let score = self.calculate_performance_score(&metrics);
                if score > best_score {
                    best_score = score;
                    best_model = model_id.clone();
                }
            }
        }

        best_model
    }

    /// 计算性能评分
    fn calculate_performance_score(&self, metrics: &crate::embedding::model_registry::ModelMetrics) -> f64 {
        // 响应时间权重：越低越好
        let response_time_score = if metrics.avg_response_time_ms > 0.0 {
            1000.0 / metrics.avg_response_time_ms
        } else {
            1000.0
        };

        // 错误率权重：越低越好
        let error_rate_score = 1.0 - metrics.error_rate;

        // 吞吐量权重：越高越好
        let throughput_score = metrics.throughput_rps;

        // 综合评分
        response_time_score * 0.4 + error_rate_score * 0.3 + throughput_score * 0.3
    }

    /// 检查模型是否可以执行
    async fn can_execute_on_model(&self, model_id: &str) -> bool {
        let mut circuit_breakers = self.circuit_breakers.write().await;

        let breaker = circuit_breakers.entry(model_id.to_string())
            .or_insert_with(|| ModelCircuitBreaker::new(self.config.circuit_breaker.clone()));

        breaker.can_execute()
    }

    /// 增加连接计数
    async fn increment_connection_count(&self, model_id: &str) {
        let mut counts = self.connection_counts.write().await;
        *counts.entry(model_id.to_string()).or_insert(0) += 1;
    }

    /// 减少连接计数
    async fn decrement_connection_count(&self, model_id: &str) {
        let mut counts = self.connection_counts.write().await;
        if let Some(count) = counts.get_mut(model_id) {
            if *count > 0 {
                *count -= 1;
            }
        }
    }

    /// 记录成功
    async fn record_success(&self, model_id: &str, duration: Duration) {
        let mut circuit_breakers = self.circuit_breakers.write().await;
        if let Some(breaker) = circuit_breakers.get_mut(model_id) {
            breaker.record_success();
        }

        // 更新模型指标
        let _ = self.registry.update_model_metrics(model_id, duration.as_millis() as f64, true).await;
    }

    /// 记录失败
    async fn record_failure(&self, model_id: &str, duration: Duration) {
        let mut circuit_breakers = self.circuit_breakers.write().await;
        if let Some(breaker) = circuit_breakers.get_mut(model_id) {
            breaker.record_failure();
        }

        // 更新模型指标
        let _ = self.registry.update_model_metrics(model_id, duration.as_millis() as f64, false).await;
    }

    /// 尝试执行文本嵌入（模拟实现）
    async fn try_embed_text(&self, _model_id: &str, _text: &str) -> Result<Embedding> {
        // 这里应该调用实际的模型实例
        // 由于架构限制，暂时返回模拟结果
        Err(KnowledgeError::embedding_error("模拟实现"))
    }

    /// 尝试执行批量嵌入（模拟实现）
    async fn try_embed_batch(&self, _model_id: &str, _texts: &[String]) -> Result<BatchEmbeddingResult> {
        // 这里应该调用实际的模型实例
        // 由于架构限制，暂时返回模拟结果
        Err(KnowledgeError::embedding_error("模拟实现"))
    }
}

/// 负载均衡统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoadBalancerStats {
    /// 负载均衡策略
    pub strategy: LoadBalancingStrategy,
    /// 总连接数
    pub total_connections: u32,
    /// 各模型连接数
    pub model_connections: HashMap<String, u32>,
    /// 断路器状态
    pub circuit_breaker_states: HashMap<String, CircuitBreakerState>,
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::embedding::model_registry::ModelRegistry;
    use crate::embedding::model_lifecycle::ModelLifecycleManager;
    use std::sync::Arc;

    fn create_test_circuit_breaker_config() -> CircuitBreakerConfig {
        CircuitBreakerConfig {
            failure_threshold: 3,
            recovery_timeout_seconds: 5,
            half_open_max_calls: 2,
        }
    }

    fn create_test_load_balancer_config() -> LoadBalancerConfig {
        LoadBalancerConfig {
            strategy: LoadBalancingStrategy::RoundRobin,
            health_check_interval_seconds: 30,
            enable_failover: true,
            max_retries: 2,
            retry_delay_ms: 50,
            circuit_breaker: create_test_circuit_breaker_config(),
        }
    }

    #[test]
    fn test_circuit_breaker_states() {
        let config = create_test_circuit_breaker_config();
        let mut breaker = ModelCircuitBreaker::new(config);

        // 初始状态应该是关闭
        assert_eq!(breaker.state, CircuitBreakerState::Closed);
        assert!(breaker.can_execute());

        // 记录失败，但未达到阈值
        breaker.record_failure();
        breaker.record_failure();
        assert_eq!(breaker.state, CircuitBreakerState::Closed);
        assert!(breaker.can_execute());

        // 达到失败阈值，应该开启
        breaker.record_failure();
        assert_eq!(breaker.state, CircuitBreakerState::Open);
        assert!(!breaker.can_execute());

        // 成功不应该在开启状态下改变状态
        breaker.record_success();
        assert_eq!(breaker.state, CircuitBreakerState::Open);
    }

    #[test]
    fn test_circuit_breaker_recovery() {
        let config = CircuitBreakerConfig {
            failure_threshold: 1,
            recovery_timeout_seconds: 0, // 立即恢复用于测试
            half_open_max_calls: 1,
        };
        let mut breaker = ModelCircuitBreaker::new(config);

        // 触发开启状态
        breaker.record_failure();
        assert_eq!(breaker.state, CircuitBreakerState::Open);

        // 应该可以转换到半开状态
        assert!(breaker.can_execute());
        assert_eq!(breaker.state, CircuitBreakerState::HalfOpen);

        // 在半开状态下成功应该转换到关闭状态
        breaker.record_success();
        assert_eq!(breaker.state, CircuitBreakerState::Closed);
    }

    #[test]
    fn test_circuit_breaker_half_open_failure() {
        let config = CircuitBreakerConfig {
            failure_threshold: 1,
            recovery_timeout_seconds: 0,
            half_open_max_calls: 1,
        };
        let mut breaker = ModelCircuitBreaker::new(config);

        // 触发开启状态
        breaker.record_failure();
        assert_eq!(breaker.state, CircuitBreakerState::Open);

        // 转换到半开状态
        assert!(breaker.can_execute());
        assert_eq!(breaker.state, CircuitBreakerState::HalfOpen);

        // 在半开状态下失败应该回到开启状态
        breaker.record_failure();
        assert_eq!(breaker.state, CircuitBreakerState::Open);
    }

    #[tokio::test]
    async fn test_load_balancer_creation() {
        let config = create_test_load_balancer_config();
        let registry = Arc::new(ModelRegistry::new());
        let lifecycle_manager = Arc::new(ModelLifecycleManager::new(registry.clone()));

        let load_balancer = LoadBalancer::new(config.clone(), registry, lifecycle_manager);

        let stats = load_balancer.get_stats().await;
        assert_eq!(stats.strategy, LoadBalancingStrategy::RoundRobin);
        assert_eq!(stats.total_connections, 0);
    }

    #[tokio::test]
    async fn test_round_robin_selection() {
        let config = create_test_load_balancer_config();
        let registry = Arc::new(ModelRegistry::new());
        let lifecycle_manager = Arc::new(ModelLifecycleManager::new(registry.clone()));
        let load_balancer = LoadBalancer::new(config, registry, lifecycle_manager);

        let models = vec!["model1".to_string(), "model2".to_string(), "model3".to_string()];

        // 测试轮询选择
        let selected1 = load_balancer.select_round_robin(&models).await;
        let selected2 = load_balancer.select_round_robin(&models).await;
        let selected3 = load_balancer.select_round_robin(&models).await;
        let selected4 = load_balancer.select_round_robin(&models).await;

        assert_eq!(selected1, "model1");
        assert_eq!(selected2, "model2");
        assert_eq!(selected3, "model3");
        assert_eq!(selected4, "model1"); // 应该循环回到第一个
    }

    #[tokio::test]
    async fn test_least_connections_selection() {
        let config = create_test_load_balancer_config();
        let registry = Arc::new(ModelRegistry::new());
        let lifecycle_manager = Arc::new(ModelLifecycleManager::new(registry.clone()));
        let load_balancer = LoadBalancer::new(config, registry, lifecycle_manager);

        let models = vec!["model1".to_string(), "model2".to_string()];

        // 增加 model1 的连接数
        load_balancer.increment_connection_count("model1").await;
        load_balancer.increment_connection_count("model1").await;

        // 最少连接应该选择 model2
        let selected = load_balancer.select_least_connections(&models).await;
        assert_eq!(selected, "model2");
    }

    #[tokio::test]
    async fn test_random_selection() {
        let config = create_test_load_balancer_config();
        let registry = Arc::new(ModelRegistry::new());
        let lifecycle_manager = Arc::new(ModelLifecycleManager::new(registry.clone()));
        let load_balancer = LoadBalancer::new(config, registry, lifecycle_manager);

        let models = vec!["model1".to_string(), "model2".to_string(), "model3".to_string()];

        // 随机选择应该返回列表中的一个模型
        let selected = load_balancer.select_random(&models).await;
        assert!(models.contains(&selected));
    }

    #[tokio::test]
    async fn test_performance_score_calculation() {
        let config = create_test_load_balancer_config();
        let registry = Arc::new(ModelRegistry::new());
        let lifecycle_manager = Arc::new(ModelLifecycleManager::new(registry.clone()));
        let load_balancer = LoadBalancer::new(config, registry, lifecycle_manager);

        let metrics = crate::embedding::model_registry::ModelMetrics {
            total_requests: 100,
            successful_requests: 95,
            failed_requests: 5,
            avg_response_time_ms: 100.0,
            min_response_time_ms: 50.0,
            max_response_time_ms: 200.0,
            throughput_rps: 10.0,
            error_rate: 0.05,
            last_updated: chrono::Utc::now(),
        };

        let score = load_balancer.calculate_performance_score(&metrics);
        assert!(score > 0.0);

        // 更好的指标应该有更高的分数
        let better_metrics = crate::embedding::model_registry::ModelMetrics {
            avg_response_time_ms: 50.0,  // 更快
            error_rate: 0.01,            // 更低错误率
            throughput_rps: 20.0,        // 更高吞吐量
            ..metrics
        };

        let better_score = load_balancer.calculate_performance_score(&better_metrics);
        assert!(better_score > score);
    }

    #[tokio::test]
    async fn test_connection_counting() {
        let config = create_test_load_balancer_config();
        let registry = Arc::new(ModelRegistry::new());
        let lifecycle_manager = Arc::new(ModelLifecycleManager::new(registry.clone()));
        let load_balancer = LoadBalancer::new(config, registry, lifecycle_manager);

        // 初始连接数应该为0
        let stats = load_balancer.get_stats().await;
        assert_eq!(stats.total_connections, 0);

        // 增加连接
        load_balancer.increment_connection_count("model1").await;
        load_balancer.increment_connection_count("model1").await;
        load_balancer.increment_connection_count("model2").await;

        let stats = load_balancer.get_stats().await;
        assert_eq!(stats.total_connections, 3);
        assert_eq!(stats.model_connections.get("model1"), Some(&2));
        assert_eq!(stats.model_connections.get("model2"), Some(&1));

        // 减少连接
        load_balancer.decrement_connection_count("model1").await;

        let stats = load_balancer.get_stats().await;
        assert_eq!(stats.total_connections, 2);
        assert_eq!(stats.model_connections.get("model1"), Some(&1));
    }

    #[tokio::test]
    async fn test_circuit_breaker_integration() {
        let config = create_test_load_balancer_config();
        let registry = Arc::new(ModelRegistry::new());
        let lifecycle_manager = Arc::new(ModelLifecycleManager::new(registry.clone()));
        let load_balancer = LoadBalancer::new(config, registry, lifecycle_manager);

        // 初始状态应该可以执行
        assert!(load_balancer.can_execute_on_model("model1").await);

        // 记录失败
        load_balancer.record_failure("model1", Duration::from_millis(100)).await;
        load_balancer.record_failure("model1", Duration::from_millis(100)).await;
        load_balancer.record_failure("model1", Duration::from_millis(100)).await;

        // 达到失败阈值后应该不能执行
        assert!(!load_balancer.can_execute_on_model("model1").await);

        // 记录成功应该恢复
        load_balancer.record_success("model1", Duration::from_millis(50)).await;
    }

    #[test]
    fn test_load_balancing_strategies() {
        // 测试所有负载均衡策略的枚举
        let strategies = vec![
            LoadBalancingStrategy::RoundRobin,
            LoadBalancingStrategy::WeightedRoundRobin,
            LoadBalancingStrategy::LeastConnections,
            LoadBalancingStrategy::FastestResponse,
            LoadBalancingStrategy::Random,
            LoadBalancingStrategy::WeightedRandom,
            LoadBalancingStrategy::ConsistentHash,
            LoadBalancingStrategy::PerformanceBased,
        ];

        // 确保所有策略都可以序列化和反序列化
        for strategy in strategies {
            let serialized = serde_json::to_string(&strategy).unwrap();
            let deserialized: LoadBalancingStrategy = serde_json::from_str(&serialized).unwrap();
            assert_eq!(strategy, deserialized);
        }
    }

    #[test]
    fn test_circuit_breaker_config() {
        let config = CircuitBreakerConfig {
            failure_threshold: 10,
            recovery_timeout_seconds: 60,
            half_open_max_calls: 5,
        };

        // 测试序列化
        let serialized = serde_json::to_string(&config).unwrap();
        let deserialized: CircuitBreakerConfig = serde_json::from_str(&serialized).unwrap();

        assert_eq!(config.failure_threshold, deserialized.failure_threshold);
        assert_eq!(config.recovery_timeout_seconds, deserialized.recovery_timeout_seconds);
        assert_eq!(config.half_open_max_calls, deserialized.half_open_max_calls);
    }
}
