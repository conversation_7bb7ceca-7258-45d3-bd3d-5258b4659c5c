//! # 嵌入向量缓存
//!
//! 提供嵌入向量的本地缓存功能。

use crate::{error::Result, embedding::Embedding};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;

/// 嵌入向量缓存
pub struct EmbeddingCache {
    /// 内存缓存
    memory_cache: Arc<RwLock<HashMap<String, Embedding>>>,
    /// 最大缓存大小
    max_size: usize,
}

impl EmbeddingCache {
    /// 创建新的嵌入向量缓存
    pub fn new() -> Result<Self> {
        Ok(Self {
            memory_cache: Arc::new(RwLock::new(HashMap::new())),
            max_size: 1000, // 默认缓存 1000 个嵌入向量
        })
    }

    /// 从缓存获取嵌入向量
    pub async fn get(&self, text: &str) -> Result<Option<Embedding>> {
        let cache = self.memory_cache.read().await;
        let key = self.create_cache_key(text);
        Ok(cache.get(&key).cloned())
    }

    /// 将嵌入向量存入缓存
    pub async fn put(&self, text: &str, embedding: &Embedding) -> Result<()> {
        let mut cache = self.memory_cache.write().await;
        let key = self.create_cache_key(text);
        
        // 如果缓存已满，清理一些旧条目
        if cache.len() >= self.max_size {
            self.evict_old_entries(&mut cache).await;
        }
        
        cache.insert(key, embedding.clone());
        Ok(())
    }

    /// 清空缓存
    pub async fn clear(&mut self) -> Result<()> {
        let mut cache = self.memory_cache.write().await;
        cache.clear();
        Ok(())
    }

    /// 获取缓存统计信息
    pub async fn stats(&self) -> CacheStats {
        let cache = self.memory_cache.read().await;
        CacheStats {
            size: cache.len(),
            max_size: self.max_size,
            hit_rate: 0.0, // 简化实现，实际应该跟踪命中率
        }
    }

    /// 创建缓存键
    fn create_cache_key(&self, text: &str) -> String {
        format!("{:x}", md5::compute(text.as_bytes()))
    }

    /// 清理旧条目（简单的 FIFO 策略）
    async fn evict_old_entries(&self, cache: &mut HashMap<String, Embedding>) {
        // 简化实现：删除一半的条目
        let keys_to_remove: Vec<String> = cache.keys()
            .take(cache.len() / 2)
            .cloned()
            .collect();
        
        for key in keys_to_remove {
            cache.remove(&key);
        }
    }
}

/// 缓存统计信息
#[derive(Debug, Clone)]
pub struct CacheStats {
    /// 当前缓存大小
    pub size: usize,
    /// 最大缓存大小
    pub max_size: usize,
    /// 命中率
    pub hit_rate: f32,
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::embedding::ModelInfo;

    fn create_test_embedding() -> Embedding {
        Embedding {
            vector: vec![0.1, 0.2, 0.3],
            dimension: 3,
            model_info: ModelInfo {
                name: "test".to_string(),
                version: "1.0".to_string(),
                model_type: "test".to_string(),
            },
            created_at: chrono::Utc::now(),
        }
    }

    #[tokio::test]
    async fn test_cache_put_and_get() {
        let cache = EmbeddingCache::new().unwrap();
        let embedding = create_test_embedding();
        let text = "hello world";
        
        // 初始状态应该没有缓存
        assert!(cache.get(text).await.unwrap().is_none());
        
        // 存入缓存
        cache.put(text, &embedding).await.unwrap();
        
        // 应该能获取到缓存
        let cached = cache.get(text).await.unwrap();
        assert!(cached.is_some());
        assert_eq!(cached.unwrap().dimension, 3);
    }

    #[tokio::test]
    async fn test_cache_clear() {
        let mut cache = EmbeddingCache::new().unwrap();
        let embedding = create_test_embedding();
        let text = "hello world";
        
        cache.put(text, &embedding).await.unwrap();
        assert!(cache.get(text).await.unwrap().is_some());
        
        cache.clear().await.unwrap();
        assert!(cache.get(text).await.unwrap().is_none());
    }

    #[tokio::test]
    async fn test_cache_stats() {
        let cache = EmbeddingCache::new().unwrap();
        let stats = cache.stats().await;
        
        assert_eq!(stats.size, 0);
        assert_eq!(stats.max_size, 1000);
    }
}