//! # 索引管理器
//!
//! 管理知识库索引的创建、更新和优化。

use crate::error::Result;
use rusqlite::Connection;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
// 暂时注释掉未使用的导入
// use tracing::{info, warn, debug};

/// 索引类型
#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
pub enum IndexType {
    /// 基础向量索引（暴力搜索）
    Basic,
    /// 层次化可导航小世界图（HNSW）
    HNSW,
    /// 倒排文件索引（IVF）
    IVF,
    /// 产品量化索引（PQ）
    ProductQuantization,
}

/// 索引配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IndexConfig {
    /// 索引类型
    pub index_type: IndexType,
    /// 索引参数
    pub parameters: HashMap<String, String>,
    /// 是否启用
    pub enabled: bool,
}

/// 索引性能指标
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IndexMetrics {
    /// 平均查询时间（毫秒）
    pub avg_query_time_ms: f64,
    /// 索引大小（字节）
    pub index_size_bytes: usize,
    /// 内存使用量（字节）
    pub memory_usage_bytes: usize,
    /// 查询准确率
    pub recall_rate: f64,
    /// 最后更新时间
    pub last_updated: chrono::DateTime<chrono::Utc>,
}

/// 索引管理器
pub struct IndexManager {
    connection: Arc<RwLock<Connection>>,
    /// 当前索引配置
    index_configs: HashMap<String, IndexConfig>,
    /// 索引性能指标
    metrics: HashMap<String, IndexMetrics>,
}

impl IndexManager {
    /// 创建新的索引管理器
    pub async fn new(connection: Arc<RwLock<Connection>>) -> Result<Self> {
        let conn = connection.write().await;
        Self::init_tables(&conn).await?;
        drop(conn);

        Ok(Self {
            connection,
            index_configs: HashMap::new(),
            metrics: HashMap::new(),
        })
    }

    /// 更新统计信息
    pub async fn update_stats(&self, new_chunks: usize) -> Result<()> {
        let conn = self.connection.write().await;
        
        // 更新索引统计
        conn.execute(
            r#"
            INSERT OR REPLACE INTO index_stats (key, value, updated_at)
            VALUES ('total_chunks', 
                    COALESCE((SELECT value FROM index_stats WHERE key = 'total_chunks'), 0) + ?1,
                    ?2)
            "#,
            rusqlite::params![new_chunks as i64, chrono::Utc::now().timestamp()],
        )?;

        Ok(())
    }

    /// 添加索引配置
    pub async fn add_index_config(&mut self, name: String, config: IndexConfig) -> Result<()> {
        self.index_configs.insert(name.clone(), config.clone());

        // 持久化配置到数据库
        let conn = self.connection.write().await;
        let config_json = serde_json::to_string(&config)?;

        conn.execute(
            r#"
            INSERT OR REPLACE INTO index_configs (name, config, created_at)
            VALUES (?1, ?2, ?3)
            "#,
            rusqlite::params![name, config_json, chrono::Utc::now().timestamp()],
        )?;

        tracing::info!("添加索引配置: {} ({:?})", name, config.index_type);
        Ok(())
    }

    /// 获取索引配置
    pub fn get_index_config(&self, name: &str) -> Option<&IndexConfig> {
        self.index_configs.get(name)
    }

    /// 列出所有索引配置
    pub fn list_index_configs(&self) -> Vec<(String, &IndexConfig)> {
        self.index_configs.iter().map(|(k, v)| (k.clone(), v)).collect()
    }

    /// 删除索引配置
    pub async fn remove_index_config(&mut self, name: &str) -> Result<bool> {
        if self.index_configs.remove(name).is_some() {
            // 从数据库中删除
            let conn = self.connection.write().await;
            let affected = conn.execute(
                "DELETE FROM index_configs WHERE name = ?1",
                rusqlite::params![name],
            )?;

            tracing::info!("删除索引配置: {}", name);
            Ok(affected > 0)
        } else {
            Ok(false)
        }
    }

    /// 获取索引统计信息
    pub async fn get_stats(&self) -> Result<IndexStats> {
        let conn = self.connection.read().await;
        
        let total_chunks: i64 = conn
            .query_row(
                "SELECT COALESCE(value, 0) FROM index_stats WHERE key = 'total_chunks'",
                [],
                |row| row.get(0),
            )
            .unwrap_or(0);

        let last_updated: Option<i64> = conn
            .query_row(
                "SELECT updated_at FROM index_stats WHERE key = 'total_chunks'",
                [],
                |row| row.get(0),
            )
            .ok();

        Ok(IndexStats {
            total_chunks: total_chunks as usize,
            last_updated: last_updated
                .map(|ts| chrono::DateTime::from_timestamp(ts, 0).unwrap_or_else(chrono::Utc::now))
                .unwrap_or_else(chrono::Utc::now),
        })
    }

    /// 重建索引
    pub async fn rebuild_index(&self) -> Result<()> {
        let conn = self.connection.write().await;
        
        // 删除所有索引数据
        conn.execute("DELETE FROM keywords", [])?;
        conn.execute("DELETE FROM vectors", [])?;
        conn.execute("DELETE FROM index_stats", [])?;
        
        // 重新分析所有表
        conn.execute("ANALYZE", [])?;
        
        tracing::info!("索引重建完成");
        Ok(())
    }

    /// 优化索引
    pub async fn optimize_index(&self) -> Result<()> {
        let conn = self.connection.write().await;
        
        // 更新统计信息
        conn.execute("ANALYZE", [])?;
        
        // 优化数据库
        conn.execute("PRAGMA optimize", [])?;
        
        tracing::info!("索引优化完成");
        Ok(())
    }

    /// 更新索引性能指标
    pub async fn update_metrics(&mut self, index_name: String, metrics: IndexMetrics) -> Result<()> {
        self.metrics.insert(index_name.clone(), metrics.clone());

        // 持久化指标到数据库
        let conn = self.connection.write().await;
        let metrics_json = serde_json::to_string(&metrics)?;

        conn.execute(
            r#"
            INSERT OR REPLACE INTO index_metrics (index_name, metrics, updated_at)
            VALUES (?1, ?2, ?3)
            "#,
            rusqlite::params![index_name, metrics_json, chrono::Utc::now().timestamp()],
        )?;

        Ok(())
    }

    /// 获取索引性能指标
    pub fn get_metrics(&self, index_name: &str) -> Option<&IndexMetrics> {
        self.metrics.get(index_name)
    }

    /// 获取所有索引的性能指标
    pub fn get_all_metrics(&self) -> &HashMap<String, IndexMetrics> {
        &self.metrics
    }

    /// 分析索引性能并提供优化建议
    pub async fn analyze_performance(&self) -> Result<Vec<String>> {
        let mut recommendations = Vec::new();

        for (index_name, metrics) in &self.metrics {
            // 查询时间过长
            if metrics.avg_query_time_ms > 100.0 {
                recommendations.push(format!(
                    "索引 '{}' 平均查询时间过长 ({:.2}ms)，建议考虑使用更高效的索引类型",
                    index_name, metrics.avg_query_time_ms
                ));
            }

            // 内存使用过高
            if metrics.memory_usage_bytes > 1024 * 1024 * 100 { // 100MB
                recommendations.push(format!(
                    "索引 '{}' 内存使用过高 ({:.2}MB)，建议考虑压缩或分片",
                    index_name, metrics.memory_usage_bytes as f64 / (1024.0 * 1024.0)
                ));
            }

            // 准确率过低
            if metrics.recall_rate < 0.9 {
                recommendations.push(format!(
                    "索引 '{}' 查询准确率过低 ({:.2}%)，建议调整索引参数",
                    index_name, metrics.recall_rate * 100.0
                ));
            }
        }

        if recommendations.is_empty() {
            recommendations.push("所有索引性能良好，无需优化".to_string());
        }

        Ok(recommendations)
    }

    /// 检查索引健康状态
    pub async fn check_index_health(&self) -> Result<IndexHealth> {
        let conn = self.connection.read().await;
        
        // 检查表完整性
        let integrity_check: String = conn
            .query_row("PRAGMA integrity_check", [], |row| row.get(0))?;
        
        let is_healthy = integrity_check == "ok";
        
        // 检查索引数量
        let index_count: i64 = conn
            .query_row(
                "SELECT COUNT(*) FROM sqlite_master WHERE type = 'index'",
                [],
                |row| row.get(0),
            )?;

        Ok(IndexHealth {
            is_healthy,
            integrity_check,
            index_count: index_count as usize,
            last_check: chrono::Utc::now(),
        })
    }

    /// 初始化数据库表
    async fn init_tables(conn: &Connection) -> Result<()> {
        // 创建索引统计表
        conn.execute(
            r#"
            CREATE TABLE IF NOT EXISTS index_stats (
                key TEXT PRIMARY KEY,
                value INTEGER NOT NULL,
                updated_at INTEGER NOT NULL
            )
            "#,
            [],
        )?;

        // 创建索引配置表
        conn.execute(
            r#"
            CREATE TABLE IF NOT EXISTS index_configs (
                name TEXT PRIMARY KEY,
                config TEXT NOT NULL,
                created_at INTEGER NOT NULL
            )
            "#,
            [],
        )?;

        // 创建索引性能指标表
        conn.execute(
            r#"
            CREATE TABLE IF NOT EXISTS index_metrics (
                index_name TEXT PRIMARY KEY,
                metrics TEXT NOT NULL,
                updated_at INTEGER NOT NULL
            )
            "#,
            [],
        )?;

        Ok(())
    }
}

/// 索引统计信息
#[derive(Debug, Clone)]
pub struct IndexStats {
    /// 总代码块数
    pub total_chunks: usize,
    /// 最后更新时间
    pub last_updated: chrono::DateTime<chrono::Utc>,
}

/// 索引健康状态
#[derive(Debug, Clone)]
pub struct IndexHealth {
    /// 是否健康
    pub is_healthy: bool,
    /// 完整性检查结果
    pub integrity_check: String,
    /// 索引数量
    pub index_count: usize,
    /// 最后检查时间
    pub last_check: chrono::DateTime<chrono::Utc>,
}

#[cfg(test)]
mod tests {
    use super::*;
    use rusqlite::Connection;
    use std::sync::Arc;
    use tokio::sync::RwLock;

    #[tokio::test]
    async fn test_index_manager_creation() {
        let conn = Arc::new(RwLock::new(Connection::open_in_memory().unwrap()));
        let manager = IndexManager::new(conn).await;
        assert!(manager.is_ok());
    }

    #[tokio::test]
    async fn test_stats_update() {
        let conn = Arc::new(RwLock::new(Connection::open_in_memory().unwrap()));
        let manager = IndexManager::new(conn).await.unwrap();
        
        manager.update_stats(10).await.unwrap();
        let stats = manager.get_stats().await.unwrap();
        
        assert_eq!(stats.total_chunks, 10);
    }

    #[tokio::test]
    async fn test_index_health_check() {
        let conn = Arc::new(RwLock::new(Connection::open_in_memory().unwrap()));
        let manager = IndexManager::new(conn).await.unwrap();
        
        let health = manager.check_index_health().await.unwrap();
        assert!(health.is_healthy);
        assert_eq!(health.integrity_check, "ok");
    }
}