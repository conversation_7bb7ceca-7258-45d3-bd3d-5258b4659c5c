//! # 知识库主类
//!
//! 提供知识库系统的统一入口和核心功能。

use crate::{
    config::Config,
    error::Result,
    parser::CodeParser,
    embedding::EmbeddingEngine,
    storage::VectorDatabase,
    query::QueryProcessor,
    context::ContextSynthesizer,
};
use std::path::{Path, PathBuf};
use std::collections::HashMap;
use tracing::{info, warn, error, debug};
use chrono::{DateTime, Utc};
use serde::{Serialize, Deserialize};
use tokio::fs;
use std::time::SystemTime;

/// 搜索结果
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct SearchResult {
    /// 代码块 ID
    pub chunk_id: String,
    /// 文件路径
    pub file_path: String,
    /// 代码内容
    pub content: String,
    /// 相似度分数
    pub score: f32,
    /// 代码语言
    pub language: String,
    /// 函数/类名称
    pub symbol_name: Option<String>,
    /// 开始行号
    pub start_line: usize,
    /// 结束行号
    pub end_line: usize,
}

/// 上下文信息
#[derive(Debug, Clone)]
pub struct ContextInfo {
    /// 相关代码块
    pub chunks: Vec<SearchResult>,
    /// 合成的上下文
    pub synthesized_context: String,
    /// 上下文长度（tokens）
    pub token_count: usize,
    /// 置信度
    pub confidence: f32,
}

/// 知识库主类
pub struct KnowledgeBase {
    /// 配置
    config: Config,
    /// 代码解析器
    parser: CodeParser,
    /// 嵌入引擎
    embedding: EmbeddingEngine,
    /// 向量数据库
    storage: VectorDatabase,
    /// 查询处理器
    query_processor: QueryProcessor,
    /// 上下文合成器
    context_synthesizer: ContextSynthesizer,
    /// 文件元数据缓存
    file_metadata: HashMap<PathBuf, FileMetadata>,
    /// 索引状态 
    indexing_stats: IndexPerformanceMetrics,
}

impl KnowledgeBase {
    /// 创建新的知识库实例
    pub async fn new(config: Config) -> Result<Self> {
        info!("初始化知识库系统...");
        
        // 验证配置
        config.validate()?;
        
        // 初始化各个组件
        let parser = CodeParser::new(&config.parser)?;
        let embedding = EmbeddingEngine::new(&config.embedding).await?;
        let storage = VectorDatabase::new(&config.database).await?;
        let query_processor = QueryProcessor::new(&config.query)?;
        let context_synthesizer = ContextSynthesizer::new(&config.context)?;
        
        info!("知识库系统初始化完成");
        
        Ok(Self {
            config,
            parser,
            embedding,
            storage,
            query_processor,
            context_synthesizer,
            file_metadata: HashMap::new(),
            indexing_stats: IndexPerformanceMetrics {
                avg_indexing_speed: 0.0,
                total_indexing_time: 0.0,
                peak_memory_usage: 0.0,
                last_index_duration: 0.0,
            },
        })
    }

    /// 从配置文件创建知识库
    pub async fn from_config_file(config_path: impl AsRef<Path>) -> Result<Self> {
        let config = Config::from_file(config_path)?;
        Self::new(config).await
    }

    /// 索引单个文件
    pub async fn index_file(&mut self, file_path: impl AsRef<Path>) -> Result<()> {
        let file_path = file_path.as_ref();
        let start_time = std::time::Instant::now();
        
        info!("索引文件: {}", file_path.display());

        // 检查文件是否需要更新
        if let Some(metadata) = self.file_metadata.get(file_path) {
            if metadata.status == IndexStatus::Indexed {
                let file_modified = fs::metadata(file_path).await?.modified()?;
                if metadata.modified_time >= file_modified {
                    debug!("文件 {} 未发生变化，跳过索引", file_path.display());
                    return Ok(());
                }
            }
        }

        // 计算文件哈希
        let content = fs::read_to_string(file_path).await?;
        let content_hash = format!("{:x}", md5::compute(&content));

        // 检查内容是否真的发生了变化
        if let Some(metadata) = self.file_metadata.get(file_path) {
            if metadata.content_hash == content_hash && metadata.status == IndexStatus::Indexed {
                debug!("文件内容未变化，跳过索引: {}", file_path.display());
                return Ok(());
            }
        }

        // 更新文件状态为索引中
        self.update_file_metadata(file_path, IndexStatus::Indexing).await?;

        // 解析代码文件
        let chunks = match self.parser.parse_file(file_path).await {
            Ok(chunks) => chunks,
            Err(e) => {
                error!("解析文件失败: {} - {}", file_path.display(), e);
                self.update_file_metadata(file_path, IndexStatus::Failed(e.to_string())).await?;
                return Err(e);
            }
        };
        
        if chunks.is_empty() {
            warn!("文件 {} 没有解析出有效代码块", file_path.display());
            // 仍然标记为已索引，避免重复处理
            self.update_file_metadata_complete(file_path, content_hash, 0).await?;
            return Ok(());
        }

        info!("文件 {} 解析出 {} 个代码块", file_path.display(), chunks.len());

        // 如果文件之前已索引，先删除旧的索引
        if let Some(metadata) = self.file_metadata.get(file_path) {
            if metadata.status == IndexStatus::Indexed {
                info!("删除文件 {} 的旧索引", file_path.display());
                self.storage.delete_file_chunks(file_path.to_string_lossy().as_ref()).await?;
            }
        }

        // 批量计算嵌入向量
        let embeddings = match self.embedding.embed_chunks(&chunks).await {
            Ok(embeddings) => embeddings,
            Err(e) => {
                error!("计算嵌入向量失败: {} - {}", file_path.display(), e);
                self.update_file_metadata(file_path, IndexStatus::Failed(e.to_string())).await?;
                return Err(e);
            }
        };

        // 存储到向量数据库
        match self.storage.insert_chunks_with_embeddings(&chunks, &embeddings).await {
            Ok(()) => {
                let duration = start_time.elapsed();
                self.indexing_stats.last_index_duration = duration.as_secs_f64();
                self.indexing_stats.total_indexing_time += duration.as_secs_f64();
                
                // 更新文件元数据
                self.update_file_metadata_complete(file_path, content_hash, chunks.len()).await?;
                
                info!("文件 {} 索引完成，耗时 {:.2}s", file_path.display(), duration.as_secs_f64());
                Ok(())
            }
            Err(e) => {
                error!("存储索引失败: {} - {}", file_path.display(), e);
                self.update_file_metadata(file_path, IndexStatus::Failed(e.to_string())).await?;
                Err(e)
            }
        }
    }

    /// 索引整个目录
    pub async fn index_directory(&mut self, dir_path: impl AsRef<Path>) -> Result<()> {
        let dir_path = dir_path.as_ref();
        info!("开始索引目录: {}", dir_path.display());

        // 获取目录中的所有代码文件
        let files = self.parser.discover_files(dir_path)?;
        info!("发现 {} 个代码文件", files.len());

        let mut success_count = 0;
        let mut error_count = 0;

        // 逐个处理文件
        for file_path in files {
            match self.index_file(&file_path).await {
                Ok(()) => {
                    success_count += 1;
                }
                Err(e) => {
                    error!("索引文件 {} 失败: {}", file_path.display(), e);
                    error_count += 1;
                }
            }
        }

        info!(
            "目录索引完成: 成功 {} 个文件, 失败 {} 个文件", 
            success_count, 
            error_count
        );

        Ok(())
    }

    /// 语义搜索
    pub async fn search(&self, query: &str) -> Result<Vec<SearchResult>> {
        info!("执行搜索查询: {}", query);

        // 处理查询
        let processed_query = self.query_processor.process_query(query).await?;

        // 计算查询向量
        let query_embedding = self.embedding.embed_query(&processed_query.text).await?;

        // 向量搜索
        let vector_results = self.storage
            .search_similar(&query_embedding, processed_query.top_k)
            .await?;

        // 关键词搜索（如果启用混合搜索）
        let keyword_results = if processed_query.enable_hybrid {
            self.storage
                .search_keywords(&processed_query.keywords, processed_query.top_k)
                .await?
        } else {
            Vec::new()
        };

        // 合并和排序结果
        let merged_results = self.query_processor
            .merge_results(vector_results, keyword_results, &processed_query)
            .await?;

        info!("搜索完成，返回 {} 个结果", merged_results.len());
        Ok(merged_results)
    }

    /// 获取相关上下文
    pub async fn get_context(&self, results: &[SearchResult]) -> Result<ContextInfo> {
        info!("合成上下文，基于 {} 个搜索结果", results.len());

        let context_info = self.context_synthesizer
            .synthesize_context(results)
            .await?;

        info!(
            "上下文合成完成，长度: {} tokens，置信度: {:.2}", 
            context_info.token_count,
            context_info.confidence
        );

        Ok(context_info)
    }

    /// 搜索并获取上下文（便捷方法）
    pub async fn search_with_context(&self, query: &str) -> Result<ContextInfo> {
        let results = self.search(query).await?;
        self.get_context(&results).await
    }

    /// 根据文件路径搜索
    pub async fn search_by_file(&self, file_path: &str) -> Result<Vec<SearchResult>> {
        info!("按文件路径搜索: {}", file_path);
        self.storage.search_by_file(file_path).await
    }

    /// 根据符号名称搜索
    pub async fn search_by_symbol(&self, symbol_name: &str) -> Result<Vec<SearchResult>> {
        info!("按符号名称搜索: {}", symbol_name);
        self.storage.search_by_symbol(symbol_name).await
    }

    /// 获取统计信息
    pub async fn get_stats(&self) -> Result<KnowledgeStats> {
        let storage_stats = self.storage.get_stats().await?;
        
        // 计算语言分布
        let mut language_distribution = HashMap::new();
        let mut file_stats = FileIndexStats {
            indexed_files: 0,
            pending_updates: 0,
            error_files: 0,
            recent_files: Vec::new(),
        };

        // 分析文件元数据
        for (path, metadata) in &self.file_metadata {
            match &metadata.status {
                IndexStatus::Indexed => file_stats.indexed_files += 1,
                IndexStatus::NeedsUpdate => file_stats.pending_updates += 1,
                IndexStatus::Failed(_) => file_stats.error_files += 1,
                _ => {}
            }

            // 提取语言信息
            if let Some(_ext) = path.extension().and_then(|e| e.to_str()) {
                let language = self.parser.detect_language_by_extension(path).unwrap_or("unknown".to_string());
                let lang_stats = language_distribution.entry(language).or_insert(LanguageStats {
                    file_count: 0,
                    chunk_count: 0,
                    total_lines: 0,
                    avg_chunk_size: 0.0,
                });
                lang_stats.file_count += 1;
                lang_stats.chunk_count += metadata.chunk_count;
            }
        }

        // 计算平均块大小
        for lang_stats in language_distribution.values_mut() {
            if lang_stats.chunk_count > 0 {
                lang_stats.avg_chunk_size = lang_stats.total_lines as f64 / lang_stats.chunk_count as f64;
            }
        }

        // 获取最近索引的文件
        let mut recent_files: Vec<_> = self.file_metadata
            .iter()
            .filter_map(|(path, metadata)| {
                metadata.indexed_time.map(|time| (path.to_string_lossy().to_string(), time))
            })
            .collect();
        recent_files.sort_by(|a, b| b.1.cmp(&a.1));
        file_stats.recent_files = recent_files.into_iter().take(10).map(|(path, _)| path).collect();

        // 计算平均索引速度
        let avg_indexing_speed = if self.indexing_stats.total_indexing_time > 0.0 {
            file_stats.indexed_files as f64 / self.indexing_stats.total_indexing_time
        } else {
            0.0
        };

        let performance_metrics = IndexPerformanceMetrics {
            avg_indexing_speed,
            total_indexing_time: self.indexing_stats.total_indexing_time,
            peak_memory_usage: self.indexing_stats.peak_memory_usage,
            last_index_duration: self.indexing_stats.last_index_duration,
        };
        
        Ok(KnowledgeStats {
            total_chunks: storage_stats.total_chunks,
            total_files: storage_stats.total_files,
            total_languages: language_distribution.len(),
            database_size: storage_stats.database_size,
            last_updated: storage_stats.last_updated,
            language_distribution,
            file_stats,
            performance_metrics,
        })
    }

    /// 更新文件元数据状态
    async fn update_file_metadata(&mut self, file_path: &Path, status: IndexStatus) -> Result<()> {
        let metadata = fs::metadata(file_path).await?;
        let file_metadata = FileMetadata {
            path: file_path.to_path_buf(),
            size: metadata.len(),
            modified_time: metadata.modified()?,
            indexed_time: None,
            content_hash: String::new(),
            chunk_count: 0,
            status,
        };
        self.file_metadata.insert(file_path.to_path_buf(), file_metadata);
        Ok(())
    }

    /// 完成文件索引后更新元数据
    async fn update_file_metadata_complete(&mut self, file_path: &Path, content_hash: String, chunk_count: usize) -> Result<()> {
        let metadata = fs::metadata(file_path).await?;
        let file_metadata = FileMetadata {
            path: file_path.to_path_buf(),
            size: metadata.len(),
            modified_time: metadata.modified()?,
            indexed_time: Some(Utc::now()),
            content_hash,
            chunk_count,
            status: IndexStatus::Indexed,
        };
        self.file_metadata.insert(file_path.to_path_buf(), file_metadata);
        Ok(())
    }

    /// 增量更新：扫描并更新已修改的文件
    pub async fn incremental_update(&mut self, root_path: impl AsRef<Path>) -> Result<Vec<String>> {
        let root_path = root_path.as_ref();
        info!("开始增量更新: {}", root_path.display());

        let mut updated_files = Vec::new();
        let files = self.parser.discover_files(root_path)?;

        for file_path in files {
            // 检查文件是否需要更新
            let needs_update = if let Some(metadata) = self.file_metadata.get(&file_path) {
                match &metadata.status {
                    IndexStatus::Indexed => {
                        let file_metadata = fs::metadata(&file_path).await?;
                        file_metadata.modified()? > metadata.modified_time
                    }
                    IndexStatus::Failed(_) | IndexStatus::NeedsUpdate => true,
                    IndexStatus::NotIndexed => true,
                    IndexStatus::Indexing => false, // 跳过正在索引的文件
                }
            } else {
                true // 新文件
            };

            if needs_update {
                match self.index_file(&file_path).await {
                    Ok(()) => {
                        updated_files.push(file_path.to_string_lossy().to_string());
                        info!("增量更新文件: {}", file_path.display());
                    }
                    Err(e) => {
                        error!("增量更新失败: {} - {}", file_path.display(), e);
                    }
                }
            }
        }

        info!("增量更新完成，更新了 {} 个文件", updated_files.len());
        Ok(updated_files)
    }

    /// 获取需要更新的文件列表
    pub async fn get_pending_updates(&self, root_path: impl AsRef<Path>) -> Result<Vec<PathBuf>> {
        let root_path = root_path.as_ref();
        let mut pending_files = Vec::new();
        let files = self.parser.discover_files(root_path)?;

        for file_path in files {
            let needs_update = if let Some(metadata) = self.file_metadata.get(&file_path) {
                match &metadata.status {
                    IndexStatus::Indexed => {
                        let file_metadata = fs::metadata(&file_path).await?;
                        file_metadata.modified()? > metadata.modified_time
                    }
                    IndexStatus::Failed(_) | IndexStatus::NeedsUpdate => true,
                    IndexStatus::NotIndexed => true,
                    IndexStatus::Indexing => false,
                }
            } else {
                true
            };

            if needs_update {
                pending_files.push(file_path);
            }
        }

        Ok(pending_files)
    }

    /// 重建索引（清除所有数据并重新索引）
    pub async fn rebuild_index(&mut self, root_path: impl AsRef<Path>) -> Result<()> {
        info!("开始重建索引...");
        
        // 清理所有数据
        self.clear().await?;
        self.file_metadata.clear();
        
        // 重新索引
        self.index_directory(root_path).await?;
        
        info!("索引重建完成");
        Ok(())
    }

    /// 清理知识库
    pub async fn clear(&mut self) -> Result<()> {
        info!("清理知识库...");
        self.storage.clear().await?;
        info!("知识库清理完成");
        Ok(())
    }

    /// 获取当前配置
    pub fn get_config(&self) -> &Config {
        &self.config
    }

    /// 更新配置（需要重新初始化相关组件）
    pub async fn update_config(&mut self, new_config: Config) -> Result<()> {
        info!("更新知识库配置...");

        // 验证新配置
        new_config.validate()?;

        // 如果数据库配置发生变化，需要重新连接
        if self.config.database.path != new_config.database.path {
            warn!("数据库路径发生变化，需要重新初始化存储引擎");
            self.storage = VectorDatabase::new(&new_config.database).await?;
        }

        // 如果嵌入配置发生变化，需要重新初始化嵌入引擎
        if self.config.embedding.model_type != new_config.embedding.model_type ||
           self.config.embedding.model_path != new_config.embedding.model_path {
            warn!("嵌入模型配置发生变化，需要重新初始化嵌入引擎");
            self.embedding = EmbeddingEngine::new(&new_config.embedding).await?;
        }

        // 更新配置
        self.config = new_config;

        info!("配置更新完成");
        Ok(())
    }

    /// 优化知识库
    pub async fn optimize(&mut self) -> Result<()> {
        info!("优化知识库...");
        self.storage.optimize().await?;
        info!("知识库优化完成");
        Ok(())
    }

    /// 创建备份
    pub async fn create_backup(&self, backup_path: impl AsRef<Path>) -> Result<BackupInfo> {
        let backup_path = backup_path.as_ref();
        info!("创建备份到: {}", backup_path.display());

        // 创建备份目录
        if let Some(parent) = backup_path.parent() {
            fs::create_dir_all(parent).await?;
        }

        // 备份数据库文件
        let db_source = &self.config.database.path;
        let db_backup = backup_path.join("knowledge.db");
        fs::copy(db_source, &db_backup).await?;

        // 备份文件元数据
        let metadata_backup = backup_path.join("metadata.json");
        let metadata_json = serde_json::to_string_pretty(&self.file_metadata)?;
        fs::write(&metadata_backup, metadata_json).await?;

        // 备份配置
        let config_backup = backup_path.join("config.toml");
        let config_toml = toml::to_string_pretty(&self.config)?;
        fs::write(&config_backup, config_toml).await?;

        // 计算备份大小和校验和
        let mut total_size = 0u64;
        let mut hasher = md5::Context::new();

        for entry in walkdir::WalkDir::new(backup_path) {
            let entry = entry.map_err(|e| crate::KnowledgeError::Io(std::io::Error::new(std::io::ErrorKind::Other, e)))?;
            if entry.file_type().is_file() {
                let metadata = entry.metadata().map_err(|e| crate::KnowledgeError::Io(std::io::Error::new(std::io::ErrorKind::Other, e)))?;
                total_size += metadata.len();

                // 更新校验和
                let content = std::fs::read(entry.path())?;
                hasher.consume(&content);
            }
        }

        let checksum = format!("{:x}", hasher.compute());

        let backup_info = BackupInfo {
            path: backup_path.to_path_buf(),
            created_at: Utc::now(),
            size: total_size,
            version: env!("CARGO_PKG_VERSION").to_string(),
            file_count: self.file_metadata.len(),
            checksum,
        };

        // 保存备份信息
        let backup_info_path = backup_path.join("backup_info.json");
        let backup_info_json = serde_json::to_string_pretty(&backup_info)?;
        fs::write(&backup_info_path, backup_info_json).await?;

        info!("备份创建完成: {} 字节", total_size);
        Ok(backup_info)
    }

    /// 从备份恢复
    pub async fn restore_from_backup(&mut self, backup_path: impl AsRef<Path>) -> Result<()> {
        let backup_path = backup_path.as_ref();
        info!("从备份恢复: {}", backup_path.display());

        // 读取备份信息
        let backup_info_path = backup_path.join("backup_info.json");
        let backup_info_json = fs::read_to_string(&backup_info_path).await?;
        let backup_info: BackupInfo = serde_json::from_str(&backup_info_json)?;

        info!("备份信息: 版本 {}, 文件数 {}, 大小 {} 字节", 
              backup_info.version, backup_info.file_count, backup_info.size);

        // 验证备份完整性
        if !self.verify_backup_integrity(backup_path, &backup_info).await? {
            return Err(crate::KnowledgeError::BackupCorrupted("备份文件校验失败".to_string()));
        }

        // 清理当前数据
        self.clear().await?;

        // 恢复数据库
        let db_backup = backup_path.join("knowledge.db");
        let db_target = &self.config.database.path;
        
        // 确保目标目录存在
        if let Some(parent) = Path::new(db_target).parent() {
            fs::create_dir_all(parent).await?;
        }
        
        fs::copy(&db_backup, db_target).await?;

        // 恢复文件元数据
        let metadata_backup = backup_path.join("metadata.json");
        if metadata_backup.exists() {
            let metadata_json = fs::read_to_string(&metadata_backup).await?;
            self.file_metadata = serde_json::from_str(&metadata_json)?;
        }

        // 重新连接数据库
        self.storage = VectorDatabase::new(&self.config.database).await?;

        info!("备份恢复完成");
        Ok(())
    }

    /// 验证备份完整性
    async fn verify_backup_integrity(&self, backup_path: &Path, backup_info: &BackupInfo) -> Result<bool> {
        info!("验证备份完整性...");

        let mut hasher = md5::Context::new();
        let mut total_size = 0u64;

        for entry in walkdir::WalkDir::new(backup_path) {
            let entry = entry.map_err(|e| crate::KnowledgeError::Io(std::io::Error::new(std::io::ErrorKind::Other, e)))?;
            if entry.file_type().is_file() && entry.file_name() != "backup_info.json" {
                let content = std::fs::read(entry.path())?;
                let metadata = entry.metadata().map_err(|e| crate::KnowledgeError::Io(std::io::Error::new(std::io::ErrorKind::Other, e)))?;
                
                hasher.consume(&content);
                total_size += metadata.len();
            }
        }

        let calculated_checksum = format!("{:x}", hasher.compute());
        let size_matches = total_size == backup_info.size;
        let checksum_matches = calculated_checksum == backup_info.checksum;

        if size_matches && checksum_matches {
            info!("备份完整性验证通过");
            Ok(true)
        } else {
            warn!("备份完整性验证失败: 大小匹配={}, 校验和匹配={}", size_matches, checksum_matches);
            Ok(false)
        }
    }

    /// 列出可用备份
    pub async fn list_backups(&self, backup_dir: impl AsRef<Path>) -> Result<Vec<BackupInfo>> {
        let backup_dir = backup_dir.as_ref();
        let mut backups = Vec::new();

        if !backup_dir.exists() {
            return Ok(backups);
        }

        let mut entries = fs::read_dir(backup_dir).await?;
        while let Some(entry) = entries.next_entry().await? {
            if entry.file_type().await?.is_dir() {
                let backup_info_path = entry.path().join("backup_info.json");
                if backup_info_path.exists() {
                    match fs::read_to_string(&backup_info_path).await {
                        Ok(content) => {
                            match serde_json::from_str::<BackupInfo>(&content) {
                                Ok(backup_info) => backups.push(backup_info),
                                Err(e) => warn!("解析备份信息失败: {} - {}", backup_info_path.display(), e),
                            }
                        }
                        Err(e) => warn!("读取备份信息失败: {} - {}", backup_info_path.display(), e),
                    }
                }
            }
        }

        // 按创建时间排序（最新的在前）
        backups.sort_by(|a, b| b.created_at.cmp(&a.created_at));

        Ok(backups)
    }

    /// 删除旧备份
    pub async fn cleanup_backups(&self, backup_dir: impl AsRef<Path>, keep_count: usize) -> Result<Vec<PathBuf>> {
        let backups = self.list_backups(&backup_dir).await?;
        let mut deleted_backups = Vec::new();

        if backups.len() > keep_count {
            for backup in backups.iter().skip(keep_count) {
                info!("删除旧备份: {}", backup.path.display());
                match fs::remove_dir_all(&backup.path).await {
                    Ok(()) => deleted_backups.push(backup.path.clone()),
                    Err(e) => error!("删除备份失败: {} - {}", backup.path.display(), e),
                }
            }
        }

        info!("清理了 {} 个旧备份", deleted_backups.len());
        Ok(deleted_backups)
    }
}

/// 知识库统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct KnowledgeStats {
    /// 总代码块数
    pub total_chunks: usize,
    /// 总文件数
    pub total_files: usize,
    /// 总语言数
    pub total_languages: usize,
    /// 数据库大小（字节）
    pub database_size: u64,
    /// 最后更新时间
    pub last_updated: DateTime<Utc>,
    /// 语言分布统计
    pub language_distribution: HashMap<String, LanguageStats>,
    /// 文件状态统计
    pub file_stats: FileIndexStats,
    /// 索引性能指标
    pub performance_metrics: IndexPerformanceMetrics,
}

/// 语言统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LanguageStats {
    /// 文件数量
    pub file_count: usize,
    /// 代码块数量
    pub chunk_count: usize,
    /// 总行数
    pub total_lines: usize,
    /// 平均代码块大小
    pub avg_chunk_size: f64,
}

/// 文件索引统计
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileIndexStats {
    /// 已索引文件数
    pub indexed_files: usize,
    /// 待更新文件数
    pub pending_updates: usize,
    /// 错误文件数
    pub error_files: usize,
    /// 最近索引的文件
    pub recent_files: Vec<String>,
}

/// 索引性能指标
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IndexPerformanceMetrics {
    /// 平均索引速度（文件/秒）
    pub avg_indexing_speed: f64,
    /// 总索引时间（秒）
    pub total_indexing_time: f64,
    /// 内存使用峰值（MB）
    pub peak_memory_usage: f64,
    /// 最后索引耗时（秒）
    pub last_index_duration: f64,
}

/// 文件元数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileMetadata {
    /// 文件路径
    pub path: PathBuf,
    /// 文件大小
    pub size: u64,
    /// 最后修改时间
    pub modified_time: SystemTime,
    /// 最后索引时间
    pub indexed_time: Option<DateTime<Utc>>,
    /// 文件哈希
    pub content_hash: String,
    /// 代码块数量
    pub chunk_count: usize,
    /// 索引状态
    pub status: IndexStatus,
}

/// 索引状态
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum IndexStatus {
    /// 未索引
    NotIndexed,
    /// 已索引
    Indexed,
    /// 需要更新
    NeedsUpdate,
    /// 索引中
    Indexing,
    /// 索引失败
    Failed(String),
}

/// 备份信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BackupInfo {
    /// 备份路径
    pub path: PathBuf,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 备份大小
    pub size: u64,
    /// 备份版本
    pub version: String,
    /// 包含的文件数
    pub file_count: usize,
    /// 校验和
    pub checksum: String,
}

#[cfg(test)]
mod tests {
    use super::*;


    #[tokio::test]
    async fn test_knowledge_base_creation() {
        let config = Config::default();
        let kb = KnowledgeBase::new(config).await;
        assert!(kb.is_ok());
    }

    #[tokio::test]
    async fn test_search_empty_database() {
        let config = Config::default();
        let kb = KnowledgeBase::new(config).await.unwrap();
        
        let results = kb.search("test query").await.unwrap();
        assert!(results.is_empty());
    }
}