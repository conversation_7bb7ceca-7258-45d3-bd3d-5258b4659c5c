//! # 向量数据库
//!
//! 提供向量存储和相似度搜索功能。

use crate::{
    error::Result,
    embedding::Embedding,
    knowledge_base::SearchResult,
};
use rusqlite::{Connection, Transaction};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tokio::sync::RwLock;

/// 距离度量类型
#[derive(<PERSON>bu<PERSON>, <PERSON>lone, Copy, PartialEq, Serialize, Deserialize)]
pub enum DistanceMetric {
    /// 余弦相似度
    Cosine,
    /// 欧几里得距离
    Euclidean,
    /// 曼哈顿距离
    Manhattan,
    /// 点积相似度
    DotProduct,
}

/// 向量索引
pub struct VectorIndex {
    connection: Arc<RwLock<Connection>>,
}

impl VectorIndex {
    /// 创建新的向量索引
    pub async fn new(connection: Arc<RwLock<Connection>>) -> Result<Self> {
        let conn = connection.write().await;
        Self::init_tables(&conn).await?;
        drop(conn);

        Ok(Self { connection })
    }

    /// 插入向量
    pub async fn insert_vector(
        &self,
        tx: &Transaction<'_>,
        chunk_id: &str,
        embedding: &Embedding,
    ) -> Result<()> {
        let vector_bytes = self.serialize_vector(&embedding.vector)?;
        
        tx.execute(
            r#"
            INSERT OR REPLACE INTO vectors (
                chunk_id, vector, dimension, model_name, model_version, created_at
            ) VALUES (?1, ?2, ?3, ?4, ?5, ?6)
            "#,
            rusqlite::params![
                chunk_id,
                vector_bytes,
                embedding.dimension,
                embedding.model_info.name,
                embedding.model_info.version,
                embedding.created_at.timestamp(),
            ],
        )?;

        Ok(())
    }

    /// 批量插入向量
    pub async fn batch_insert_vectors(
        &self,
        tx: &Transaction<'_>,
        vectors: &[(String, Embedding)],
    ) -> Result<()> {
        // 准备批量插入语句
        let mut stmt = tx.prepare(
            r#"
            INSERT OR REPLACE INTO vectors (
                chunk_id, vector, dimension, model_name, model_version, created_at
            ) VALUES (?1, ?2, ?3, ?4, ?5, ?6)
            "#,
        )?;

        // 批量插入
        for (chunk_id, embedding) in vectors {
            let vector_bytes = self.serialize_vector(&embedding.vector)?;

            stmt.execute(rusqlite::params![
                chunk_id,
                vector_bytes,
                embedding.dimension,
                embedding.model_info.name,
                embedding.model_info.version,
                embedding.created_at.timestamp(),
            ])?;
        }

        Ok(())
    }

    /// 搜索相似向量
    pub async fn search_similar(
        &self,
        query_embedding: &Embedding,
        top_k: usize,
    ) -> Result<Vec<SearchResult>> {
        self.search_similar_with_threshold(query_embedding, top_k, 0.0).await
    }

    /// 搜索相似向量（带阈值过滤）
    pub async fn search_similar_with_threshold(
        &self,
        query_embedding: &Embedding,
        top_k: usize,
        threshold: f32,
    ) -> Result<Vec<SearchResult>> {
        self.search_similar_with_metric(query_embedding, top_k, threshold, DistanceMetric::Cosine).await
    }

    /// 搜索相似向量（指定距离度量）
    pub async fn search_similar_with_metric(
        &self,
        query_embedding: &Embedding,
        top_k: usize,
        threshold: f32,
        metric: DistanceMetric,
    ) -> Result<Vec<SearchResult>> {
        let conn = self.connection.read().await;
        let mut stmt = conn.prepare(
            r#"
            SELECT v.chunk_id, c.file_path, c.content, c.language,
                   c.symbol_name, c.start_line, c.end_line, v.vector
            FROM vectors v
            INNER JOIN chunks c ON v.chunk_id = c.id
            WHERE v.dimension = ?1
            "#,
        )?;

        let rows = stmt.query_map([query_embedding.dimension], |row| {
            let chunk_id: String = row.get(0)?;
            let file_path: String = row.get(1)?;
            let content: String = row.get(2)?;
            let language: String = row.get(3)?;
            let symbol_name: Option<String> = row.get(4)?;
            let start_line: usize = row.get(5)?;
            let end_line: usize = row.get(6)?;
            let vector_bytes: Vec<u8> = row.get(7)?;

            Ok((chunk_id, file_path, content, language, symbol_name, start_line, end_line, vector_bytes))
        })?;

        let mut candidates = Vec::new();
        for row in rows {
            let (chunk_id, file_path, content, language, symbol_name, start_line, end_line, vector_bytes) = row?;

            // 反序列化向量并计算相似度
            let vector = self.deserialize_vector(&vector_bytes)?;
            let similarity = self.calculate_similarity(&query_embedding.vector, &vector, metric);

            // 只保留超过阈值的结果
            if similarity >= threshold {
                candidates.push(SearchResult {
                    chunk_id,
                    file_path,
                    content,
                    score: similarity,
                    language,
                    symbol_name,
                    start_line,
                    end_line,
                });
            }
        }

        // 按相似度排序
        candidates.sort_by(|a, b| b.score.partial_cmp(&a.score).unwrap_or(std::cmp::Ordering::Equal));
        candidates.truncate(top_k);

        Ok(candidates)
    }

    /// 序列化向量
    fn serialize_vector(&self, vector: &[f32]) -> Result<Vec<u8>> {
        let json = serde_json::to_string(vector)?;
        Ok(json.into_bytes())
    }

    /// 反序列化向量
    fn deserialize_vector(&self, bytes: &[u8]) -> Result<Vec<f32>> {
        let json = String::from_utf8(bytes.to_vec())
            .map_err(|e| crate::KnowledgeError::storage_error(format!("向量反序列化失败: {}", e)))?;
        let vector: Vec<f32> = serde_json::from_str(&json)?;
        Ok(vector)
    }

    /// 根据指定的度量方法计算相似度
    fn calculate_similarity(&self, a: &[f32], b: &[f32], metric: DistanceMetric) -> f32 {
        match metric {
            DistanceMetric::Cosine => self.cosine_similarity(a, b),
            DistanceMetric::Euclidean => self.euclidean_similarity(a, b),
            DistanceMetric::Manhattan => self.manhattan_similarity(a, b),
            DistanceMetric::DotProduct => self.dot_product_similarity(a, b),
        }
    }

    /// 计算余弦相似度
    fn cosine_similarity(&self, a: &[f32], b: &[f32]) -> f32 {
        if a.len() != b.len() {
            return 0.0;
        }

        let dot_product: f32 = a.iter().zip(b.iter()).map(|(x, y)| x * y).sum();
        let norm_a: f32 = a.iter().map(|x| x * x).sum::<f32>().sqrt();
        let norm_b: f32 = b.iter().map(|x| x * x).sum::<f32>().sqrt();

        if norm_a == 0.0 || norm_b == 0.0 {
            0.0
        } else {
            dot_product / (norm_a * norm_b)
        }
    }

    /// 计算欧几里得距离（转换为相似度分数）
    fn euclidean_similarity(&self, a: &[f32], b: &[f32]) -> f32 {
        if a.len() != b.len() {
            return 0.0;
        }

        let distance: f32 = a.iter()
            .zip(b.iter())
            .map(|(x, y)| (x - y).powi(2))
            .sum::<f32>()
            .sqrt();

        // 转换为相似度分数 (0-1)，距离越小相似度越高
        1.0 / (1.0 + distance)
    }

    /// 计算曼哈顿距离（转换为相似度分数）
    fn manhattan_similarity(&self, a: &[f32], b: &[f32]) -> f32 {
        if a.len() != b.len() {
            return 0.0;
        }

        let distance: f32 = a.iter()
            .zip(b.iter())
            .map(|(x, y)| (x - y).abs())
            .sum();

        // 转换为相似度分数
        1.0 / (1.0 + distance)
    }

    /// 计算点积相似度（归一化）
    fn dot_product_similarity(&self, a: &[f32], b: &[f32]) -> f32 {
        if a.len() != b.len() {
            return 0.0;
        }

        let dot_product: f32 = a.iter().zip(b.iter()).map(|(x, y)| x * y).sum();

        // 简单归一化到 [0, 1] 范围
        (dot_product + 1.0) / 2.0
    }

    /// 初始化数据库表
    async fn init_tables(conn: &Connection) -> Result<()> {
        conn.execute(
            r#"
            CREATE TABLE IF NOT EXISTS vectors (
                chunk_id TEXT PRIMARY KEY,
                vector BLOB NOT NULL,
                dimension INTEGER NOT NULL,
                model_name TEXT NOT NULL,
                model_version TEXT NOT NULL,
                created_at INTEGER NOT NULL,
                FOREIGN KEY (chunk_id) REFERENCES chunks (id) ON DELETE CASCADE
            )
            "#,
            [],
        )?;

        // 创建索引以提高查询性能
        conn.execute(
            "CREATE INDEX IF NOT EXISTS idx_vectors_model ON vectors (model_name, model_version)",
            [],
        )?;

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use rusqlite::Connection;
    use std::sync::Arc;
    use tokio::sync::RwLock;

    #[tokio::test]
    async fn test_cosine_similarity() {
        let conn = Arc::new(RwLock::new(Connection::open_in_memory().unwrap()));
        let index = VectorIndex::new(conn).await.unwrap();
        
        let a = vec![1.0, 0.0, 0.0];
        let b = vec![1.0, 0.0, 0.0];
        let similarity = index.cosine_similarity(&a, &b);
        
        assert!((similarity - 1.0).abs() < 1e-6);
    }

    #[test]
    fn test_vector_serialization() {
        let conn = Arc::new(RwLock::new(Connection::open_in_memory().unwrap()));
        let rt = tokio::runtime::Runtime::new().unwrap();
        let index = rt.block_on(VectorIndex::new(conn)).unwrap();
        
        let vector = vec![1.0, 2.0, 3.0];
        let serialized = index.serialize_vector(&vector).unwrap();
        let deserialized = index.deserialize_vector(&serialized).unwrap();
        
        assert_eq!(vector, deserialized);
    }
}