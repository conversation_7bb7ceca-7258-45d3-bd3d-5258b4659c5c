//! # 项目信息工具
//!
//! 提供项目信息检测功能，包括Git信息、编程语言、文件统计等。

use std::path::Path;
use std::process::Command;
use std::collections::HashMap;

/// 项目信息结构体
#[derive(Debug, Clone)]
pub struct ProjectInfo {
    /// Git 分支名
    pub git_branch: Option<String>,
    /// 是否为 Git 仓库
    pub is_git_repo: bool,
    /// 主要编程语言
    pub primary_language: Option<String>,
    /// 语言统计
    pub language_stats: HashMap<String, usize>,
    /// 文件统计
    pub file_count: usize,
    /// 目录统计
    pub dir_count: usize,
}

impl ProjectInfo {
    /// 分析当前目录的项目信息
    pub fn analyze_current_dir() -> Self {
        let current_dir = std::env::current_dir().unwrap_or_else(|_| std::path::PathBuf::from("."));
        Self::analyze_directory(&current_dir)
    }

    /// 分析指定目录的项目信息
    pub fn analyze_directory(path: &Path) -> Self {
        let mut info = ProjectInfo {
            git_branch: None,
            is_git_repo: false,
            primary_language: None,
            language_stats: HashMap::new(),
            file_count: 0,
            dir_count: 0,
        };

        // 检测 Git 信息
        info.detect_git_info(path);
        
        // 检测编程语言和文件统计
        info.analyze_files(path);

        info
    }

    /// 检测 Git 仓库信息
    fn detect_git_info(&mut self, path: &Path) {
        // 检查是否为 Git 仓库
        let git_dir = path.join(".git");
        if git_dir.exists() {
            self.is_git_repo = true;
            
            // 获取当前分支名
            if let Ok(output) = Command::new("git")
                .args(&["branch", "--show-current"])
                .current_dir(path)
                .output()
            {
                if output.status.success() {
                    let branch = String::from_utf8_lossy(&output.stdout)
                        .trim()
                        .to_string();
                    if !branch.is_empty() {
                        self.git_branch = Some(branch);
                    }
                }
            }
        }
    }

    /// 分析文件和编程语言
    fn analyze_files(&mut self, path: &Path) {
        if let Ok(entries) = std::fs::read_dir(path) {
            for entry in entries.flatten() {
                let file_path = entry.path();
                
                if file_path.is_dir() {
                    // 跳过常见的忽略目录
                    if let Some(dir_name) = file_path.file_name() {
                        let dir_name = dir_name.to_string_lossy();
                        if !should_ignore_dir(&dir_name) {
                            self.dir_count += 1;
                            // 递归分析子目录（限制深度）
                            let sub_info = Self::analyze_directory(&file_path);
                            self.file_count += sub_info.file_count;
                            self.dir_count += sub_info.dir_count;
                            
                            // 合并语言统计
                            for (lang, count) in sub_info.language_stats {
                                *self.language_stats.entry(lang).or_insert(0) += count;
                            }
                        }
                    }
                } else {
                    self.file_count += 1;
                    
                    // 根据文件扩展名判断编程语言
                    if let Some(extension) = file_path.extension() {
                        if let Some(language) = extension_to_language(extension.to_string_lossy().as_ref()) {
                            *self.language_stats.entry(language).or_insert(0) += 1;
                        }
                    }
                }
            }
        }

        // 确定主要编程语言
        self.primary_language = self.language_stats
            .iter()
            .max_by_key(|(_, count)| *count)
            .map(|(lang, _)| lang.clone());
    }

    /// 格式化显示 Git 信息
    pub fn format_git_info(&self) -> Option<String> {
        if self.is_git_repo {
            match &self.git_branch {
                Some(branch) => Some(format!("🌿 {}", branch)),
                None => Some("🌿 Git".to_string()),
            }
        } else {
            None
        }
    }

    /// 格式化显示语言信息
    pub fn format_language_info(&self) -> Option<String> {
        self.primary_language.as_ref().map(|lang| {
            let icon = language_icon(lang);
            format!("{} {}", icon, lang)
        })
    }

    /// 格式化显示文件统计
    pub fn format_file_stats(&self) -> String {
        format!("📄 {} 文件, 📁 {} 目录", self.file_count, self.dir_count)
    }
}

/// 判断是否应该忽略的目录
fn should_ignore_dir(dir_name: &str) -> bool {
    matches!(dir_name, 
        ".git" | ".svn" | ".hg" | 
        "node_modules" | "target" | "build" | "dist" | 
        ".vscode" | ".idea" | 
        "__pycache__" | ".pytest_cache" |
        "vendor" | "deps"
    )
}

/// 根据文件扩展名判断编程语言
fn extension_to_language(ext: &str) -> Option<String> {
    match ext.to_lowercase().as_str() {
        "rs" => Some("Rust".to_string()),
        "js" | "mjs" => Some("JavaScript".to_string()),
        "ts" => Some("TypeScript".to_string()),
        "py" => Some("Python".to_string()),
        "java" => Some("Java".to_string()),
        "cpp" | "cc" | "cxx" => Some("C++".to_string()),
        "c" => Some("C".to_string()),
        "cs" => Some("C#".to_string()),
        "go" => Some("Go".to_string()),
        "php" => Some("PHP".to_string()),
        "rb" => Some("Ruby".to_string()),
        "swift" => Some("Swift".to_string()),
        "kt" => Some("Kotlin".to_string()),
        "scala" => Some("Scala".to_string()),
        "sh" | "bash" => Some("Shell".to_string()),
        "html" | "htm" => Some("HTML".to_string()),
        "css" => Some("CSS".to_string()),
        "json" => Some("JSON".to_string()),
        "xml" => Some("XML".to_string()),
        "yaml" | "yml" => Some("YAML".to_string()),
        "toml" => Some("TOML".to_string()),
        "md" => Some("Markdown".to_string()),
        _ => None,
    }
}

/// 获取编程语言图标
fn language_icon(language: &str) -> &'static str {
    match language {
        "Rust" => "🦀",
        "JavaScript" => "🟨",
        "TypeScript" => "🔷",
        "Python" => "🐍",
        "Java" => "☕",
        "C++" => "⚡",
        "C" => "🔧",
        "C#" => "💎",
        "Go" => "🐹",
        "PHP" => "🐘",
        "Ruby" => "💎",
        "Swift" => "🍎",
        "Kotlin" => "🟠",
        "Scala" => "🔴",
        "Shell" => "🐚",
        "HTML" => "🌐",
        "CSS" => "🎨",
        "JSON" => "📋",
        "XML" => "📄",
        "YAML" => "⚙️",
        "TOML" => "⚙️",
        "Markdown" => "📝",
        _ => "📄",
    }
}