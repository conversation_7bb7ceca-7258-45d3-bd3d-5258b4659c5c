//! # Orion Knowledge Base 使用示例
//!
//! 演示如何使用知识库系统进行代码索引和搜索。

use orion_knowledge::{Config, KnowledgeBase};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 注意：如果需要日志输出，请添加 tracing-subscriber 依赖并取消下行注释
    // tracing_subscriber::init();

    // 1. 从配置文件创建知识库
    let mut kb = KnowledgeBase::from_config_file("knowledge.toml").await?;
    
    println!("🚀 知识库系统初始化完成");

    // 2. 索引代码目录
    println!("📚 开始索引代码目录...");
    kb.index_directory("./src").await?;
    
    // 3. 获取统计信息
    let stats = kb.get_stats().await?;
    println!("📊 索引统计：");
    println!("  - 代码块数量: {}", stats.total_chunks);
    println!("  - 文件数量: {}", stats.total_files);
    println!("  - 编程语言数量: {}", stats.total_languages);
    println!("  - 数据库大小: {} bytes", stats.database_size);

    // 4. 执行语义搜索
    println!("\n🔍 执行搜索查询...");
    
    let queries = vec![
        "find function that handles errors",
        "lang:rust struct definition",
        "how to parse JSON data",
        "async function implementation",
    ];

    for query in queries {
        println!("\n查询: \"{}\"", query);
        
        // 搜索相关代码
        let results = kb.search(query).await?;
        
        if results.is_empty() {
            println!("  没有找到相关结果");
            continue;
        }

        println!("  找到 {} 个相关结果:", results.len());
        
        // 显示前3个结果
        for (i, result) in results.iter().take(3).enumerate() {
            println!("  {}. {} (相似度: {:.2})", 
                i + 1, 
                result.file_path,
                result.score
            );
            
            if let Some(ref symbol) = result.symbol_name {
                println!("     符号: {}", symbol);
            }
            
            println!("     行号: {}-{}", result.start_line, result.end_line);
        }

        // 5. 获取上下文信息
        let context_info = kb.get_context(&results).await?;
        println!("  上下文信息:");
        println!("    - Token 数量: {}", context_info.token_count);
        println!("    - 置信度: {:.2}", context_info.confidence);
        
        // 可以将上下文发送给 LLM 进行进一步处理
        // let llm_response = send_to_llm(&context_info.synthesized_context).await;
    }

    // 6. 优化知识库
    println!("\n🔧 优化知识库...");
    kb.optimize().await?;
    
    println!("✅ 知识库操作完成！");

    Ok(())
}

// 使用示例函数
#[allow(dead_code)]
async fn example_file_indexing() -> Result<(), Box<dyn std::error::Error>> {
    // 使用默认配置创建知识库
    let config = Config::default();
    let mut kb = KnowledgeBase::new(config).await?;
    
    // 索引单个文件
    kb.index_file("src/main.rs").await?;
    
    // 搜索特定符号
    let results = kb.search("main function").await?;
    
    for result in results {
        println!("找到函数: {} 在文件 {}", 
            result.symbol_name.unwrap_or_default(),
            result.file_path
        );
    }
    
    Ok(())
}

// 自定义配置示例
#[allow(dead_code)]
async fn example_custom_config() -> Result<(), Box<dyn std::error::Error>> {
    use orion_knowledge::{
        Config, DatabaseConfig, ParserConfig, EmbeddingConfig,
        ChunkingStrategy, EmbeddingModelType
    };
    
    let config = Config {
        database: DatabaseConfig {
            path: "./custom_knowledge.db".into(),
            pool_size: 5,
            query_timeout: 15,
            enable_wal: true,
            cache_size_mb: 32,
        },
        parser: ParserConfig {
            supported_languages: vec![
                "rust".to_string(),
                "python".to_string(),
            ],
            max_file_size: 5 * 1024 * 1024, // 5MB
            chunking_strategy: ChunkingStrategy::Hybrid,
            chunk_size: 800,
            chunk_overlap: 100,
        },
        embedding: EmbeddingConfig {
            model_type: EmbeddingModelType::Local,
            model_path: "./models/embedding_model".to_string(),
            dimension: 256,
            batch_size: 16,
            api_key: None,
        },
        query: Default::default(),
        context: Default::default(),
    };
    
    let _kb = KnowledgeBase::new(config).await?;
    
    // 使用自定义配置的知识库...
    
    Ok(())
}

// 高级搜索示例
#[allow(dead_code)]
async fn example_advanced_search() -> Result<(), Box<dyn std::error::Error>> {
    let kb = KnowledgeBase::from_config_file("knowledge.toml").await?;
    
    // 不同类型的搜索查询
    let searches = vec![
        ("语义搜索", "error handling in async functions"),
        ("语言过滤", "lang:rust fn main"),
        ("文件过滤", "file:main.rs hello world"),
        ("精确匹配", "exact:\"pub fn new\""),
        ("代码搜索", "code:impl Display"),
        ("文档搜索", "doc:configuration options"),
    ];
    
    for (search_type, query) in searches {
        println!("\n{}: {}", search_type, query);
        
        let results = kb.search(query).await?;
        let context = kb.get_context(&results).await?;
        
        println!("  结果数量: {}", results.len());
        println!("  上下文置信度: {:.2}", context.confidence);
    }
    
    Ok(())
}