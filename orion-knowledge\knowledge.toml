# Orion Knowledge Base 配置文件示例

[database]
path = "./knowledge.db"
pool_size = 10
query_timeout = 30
enable_wal = true
cache_size_mb = 64

[parser]
supported_languages = [
    "rust",
    "javascript", 
    "typescript",
    "python",
    "java",
    "cpp",
    "c",
    "go"
]
max_file_size = 10485760  # 10MB 
chunking_strategy = "Hybrid"
chunk_size = 1000
chunk_overlap = 150

[embedding]
model_type = "Local"
model_path = "./models/local_model"
dimension = 384
batch_size = 32
api_key = ""

[query]
default_top_k = 10
max_top_k = 50
similarity_threshold = 0.3
hybrid_weight = 0.7

[context]
max_context_length = 4000
context_window = 2048
enable_compression = true
compression_ratio = 0.8