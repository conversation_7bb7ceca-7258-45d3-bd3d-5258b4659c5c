//! # 嵌入模型管理系统
//!
//! 统一管理所有嵌入模型，提供模型注册、生命周期管理、负载均衡、监控等功能。

use crate::{
    error::Result,
    embedding::{
        Embedding, BatchEmbeddingResult,
        model_registry::{ModelRegistry, ModelConfig, ModelSelectionCriteria, RegistrySummary},
        model_lifecycle::ModelLifecycleManager,
        load_balancer::{LoadBalancer, LoadBalancerConfig, LoadBalancerStats},
        manager::EmbedderType,
        performance::PerformanceConfig,
    },
};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::Duration;
use tokio::sync::RwLock;
use tokio::time::interval;
use tracing::{debug, info, warn};

/// 模型管理系统配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModelManagerConfig {
    /// 负载均衡配置
    pub load_balancer: LoadBalancerConfig,
    /// 默认性能配置
    pub default_performance: PerformanceConfig,
    /// 监控配置
    pub monitoring: MonitoringConfig,
    /// 自动发现配置
    pub auto_discovery: AutoDiscoveryConfig,
}

impl Default for ModelManagerConfig {
    fn default() -> Self {
        Self {
            load_balancer: LoadBalancerConfig::default(),
            default_performance: PerformanceConfig::default(),
            monitoring: MonitoringConfig::default(),
            auto_discovery: AutoDiscoveryConfig::default(),
        }
    }
}

/// 监控配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MonitoringConfig {
    /// 是否启用监控
    pub enabled: bool,
    /// 指标收集间隔（秒）
    pub metrics_interval_seconds: u64,
    /// 报告间隔（秒）
    pub report_interval_seconds: u64,
    /// 是否启用详细日志
    pub verbose_logging: bool,
}

impl Default for MonitoringConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            metrics_interval_seconds: 60,
            report_interval_seconds: 300,
            verbose_logging: false,
        }
    }
}

/// 自动发现配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AutoDiscoveryConfig {
    /// 是否启用自动发现
    pub enabled: bool,
    /// 发现间隔（秒）
    pub discovery_interval_seconds: u64,
    /// 预定义模型配置
    pub predefined_models: Vec<PredefinedModelConfig>,
}

impl Default for AutoDiscoveryConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            discovery_interval_seconds: 600, // 10分钟
            predefined_models: Self::default_predefined_models(),
        }
    }
}

impl AutoDiscoveryConfig {
    fn default_predefined_models() -> Vec<PredefinedModelConfig> {
        vec![
            PredefinedModelConfig {
                model_id: "local-bge-m3".to_string(),
                model_type: EmbedderType::Local,
                model_name: "bge-m3".to_string(),
                dimension: 1024,
                priority: 10,
                auto_register: true,
                tags: [("provider".to_string(), "local".to_string())].into(),
            },
            PredefinedModelConfig {
                model_id: "openai-text-embedding-3-small".to_string(),
                model_type: EmbedderType::OpenAI,
                model_name: "text-embedding-3-small".to_string(),
                dimension: 1536,
                priority: 20,
                auto_register: false, // 需要 API 密钥
                tags: [("provider".to_string(), "openai".to_string())].into(),
            },
            PredefinedModelConfig {
                model_id: "cohere-embed-english-v3".to_string(),
                model_type: EmbedderType::Cohere,
                model_name: "embed-english-v3.0".to_string(),
                dimension: 1024,
                priority: 30,
                auto_register: false, // 需要 API 密钥
                tags: [("provider".to_string(), "cohere".to_string())].into(),
            },
        ]
    }
}

/// 预定义模型配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PredefinedModelConfig {
    pub model_id: String,
    pub model_type: EmbedderType,
    pub model_name: String,
    pub dimension: usize,
    pub priority: u32,
    pub auto_register: bool,
    pub tags: HashMap<String, String>,
}

/// 模型管理系统
pub struct ModelManager {
    /// 配置
    config: ModelManagerConfig,
    /// 模型注册表
    registry: Arc<ModelRegistry>,
    /// 生命周期管理器
    lifecycle_manager: Arc<ModelLifecycleManager>,
    /// 负载均衡器
    load_balancer: Arc<LoadBalancer>,
    /// 系统统计
    stats: Arc<RwLock<SystemStats>>,
}

impl ModelManager {
    /// 创建新的模型管理系统
    pub async fn new(config: ModelManagerConfig) -> Result<Self> {
        info!("初始化模型管理系统");

        // 创建模型注册表
        let registry = Arc::new(ModelRegistry::new());

        // 创建生命周期管理器
        let lifecycle_manager = Arc::new(ModelLifecycleManager::new(registry.clone()));

        // 创建负载均衡器
        let load_balancer = Arc::new(LoadBalancer::new(
            config.load_balancer.clone(),
            registry.clone(),
            lifecycle_manager.clone(),
        ));

        let manager = Self {
            config,
            registry,
            lifecycle_manager,
            load_balancer,
            stats: Arc::new(RwLock::new(SystemStats::default())),
        };

        info!("模型管理系统初始化完成");
        Ok(manager)
    }

    /// 启动模型管理系统
    pub async fn start(&self) -> Result<()> {
        info!("启动模型管理系统");

        // 自动发现和注册预定义模型
        if self.config.auto_discovery.enabled {
            self.auto_discover_models().await?;
        }

        // 启动生命周期管理器
        self.lifecycle_manager.start().await?;

        // 启动监控任务
        if self.config.monitoring.enabled {
            self.start_monitoring_tasks().await;
        }

        info!("模型管理系统启动完成");
        Ok(())
    }

    /// 注册模型
    pub async fn register_model(&self, config: ModelConfig) -> Result<()> {
        info!("注册模型: {}", config.model_id);

        // 注册到注册表
        self.registry.register_model(config.clone()).await?;

        // 初始化模型
        self.lifecycle_manager.initialize_model(&config.model_id).await?;

        // 更新统计
        {
            let mut stats = self.stats.write().await;
            stats.total_registered_models += 1;
        }

        info!("模型注册完成: {}", config.model_id);
        Ok(())
    }

    /// 注销模型
    pub async fn unregister_model(&self, model_id: &str) -> Result<()> {
        info!("注销模型: {}", model_id);

        // 销毁模型实例
        self.lifecycle_manager.destroy_model(model_id).await?;

        // 从注册表移除
        self.registry.unregister_model(model_id).await?;

        // 更新统计
        {
            let mut stats = self.stats.write().await;
            if stats.total_registered_models > 0 {
                stats.total_registered_models -= 1;
            }
        }

        info!("模型注销完成: {}", model_id);
        Ok(())
    }

    /// 计算文本嵌入向量
    pub async fn embed_text(&self, text: &str, criteria: Option<ModelSelectionCriteria>) -> Result<Embedding> {
        let criteria = criteria.unwrap_or_default();
        
        // 更新请求统计
        {
            let mut stats = self.stats.write().await;
            stats.total_requests += 1;
        }

        let result = self.load_balancer.embed_text(text, &criteria).await;

        // 更新结果统计
        {
            let mut stats = self.stats.write().await;
            match &result {
                Ok(_) => stats.successful_requests += 1,
                Err(_) => stats.failed_requests += 1,
            }
        }

        result
    }

    /// 批量计算嵌入向量
    pub async fn embed_batch(&self, texts: &[String], criteria: Option<ModelSelectionCriteria>) -> Result<BatchEmbeddingResult> {
        let criteria = criteria.unwrap_or_default();
        
        // 更新请求统计
        {
            let mut stats = self.stats.write().await;
            stats.total_batch_requests += 1;
        }

        let result = self.load_balancer.embed_batch(texts, &criteria).await;

        // 更新结果统计
        {
            let mut stats = self.stats.write().await;
            match &result {
                Ok(_) => stats.successful_batch_requests += 1,
                Err(_) => stats.failed_batch_requests += 1,
            }
        }

        result
    }

    /// 列出所有模型
    pub async fn list_models(&self) -> Vec<String> {
        self.registry.list_models().await
    }

    /// 获取模型配置
    pub async fn get_model_config(&self, model_id: &str) -> Option<ModelConfig> {
        self.registry.get_model_config(model_id).await
    }

    /// 设置默认模型
    pub async fn set_default_model(&self, model_id: &str) -> Result<()> {
        self.registry.set_default_model(model_id).await
    }

    /// 获取默认模型
    pub async fn get_default_model(&self) -> Option<String> {
        self.registry.get_default_model_id().await
    }

    /// 重启模型
    pub async fn restart_model(&self, model_id: &str) -> Result<()> {
        self.lifecycle_manager.restart_model(model_id).await
    }

    /// 执行健康检查
    pub async fn health_check(&self, model_id: Option<&str>) -> Result<HashMap<String, Result<Duration>>> {
        match model_id {
            Some(id) => {
                // 单个模型健康检查
                let mut results = HashMap::new();
                if let Some(_model_instance) = self.lifecycle_manager.get_model_instance(id).await {
                    // 由于 ModelInstance 的限制，这里需要重新设计
                    results.insert(id.to_string(), Ok(Duration::from_millis(100))); // 模拟结果
                }
                Ok(results)
            }
            None => {
                // 所有模型健康检查
                self.lifecycle_manager.health_check_all().await
            }
        }
    }

    /// 获取系统统计信息
    pub async fn get_system_stats(&self) -> SystemStats {
        let stats = self.stats.read().await;
        stats.clone()
    }

    /// 获取注册表摘要
    pub async fn get_registry_summary(&self) -> RegistrySummary {
        self.registry.get_registry_summary().await
    }

    /// 获取负载均衡统计
    pub async fn get_load_balancer_stats(&self) -> LoadBalancerStats {
        self.load_balancer.get_stats().await
    }

    /// 自动发现模型
    async fn auto_discover_models(&self) -> Result<()> {
        info!("开始自动发现模型");

        for predefined in &self.config.auto_discovery.predefined_models {
            if predefined.auto_register {
                let config = ModelConfig::new(
                    predefined.model_id.clone(),
                    predefined.model_type.clone(),
                    predefined.model_name.clone(),
                    predefined.dimension,
                )
                .with_priority(predefined.priority);

                // 添加标签
                let mut config = config;
                for (key, value) in &predefined.tags {
                    config = config.with_tag(key.clone(), value.clone());
                }

                match self.register_model(config).await {
                    Ok(_) => {
                        info!("自动注册模型成功: {}", predefined.model_id);
                    }
                    Err(e) => {
                        warn!("自动注册模型失败: {} - {}", predefined.model_id, e);
                    }
                }
            }
        }

        Ok(())
    }

    /// 启动监控任务
    async fn start_monitoring_tasks(&self) {
        let _stats = self.stats.clone();
        let _registry = self.registry.clone();
        let config = self.config.monitoring.clone();

        // 指标收集任务
        tokio::spawn(async move {
            let mut interval = interval(Duration::from_secs(config.metrics_interval_seconds));

            loop {
                interval.tick().await;

                if config.verbose_logging {
                    debug!("收集系统指标");
                }

                // 这里可以添加更多的指标收集逻辑
            }
        });

        // 报告任务
        let stats_clone = self.stats.clone();
        let config_clone = self.config.monitoring.clone();

        tokio::spawn(async move {
            let mut interval = interval(Duration::from_secs(config_clone.report_interval_seconds));

            loop {
                interval.tick().await;

                let stats = stats_clone.read().await;
                info!("系统统计报告: {:?}", *stats);
            }
        });
    }
}

/// 系统统计信息
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct SystemStats {
    /// 总注册模型数
    pub total_registered_models: u64,
    /// 总请求数
    pub total_requests: u64,
    /// 成功请求数
    pub successful_requests: u64,
    /// 失败请求数
    pub failed_requests: u64,
    /// 总批量请求数
    pub total_batch_requests: u64,
    /// 成功批量请求数
    pub successful_batch_requests: u64,
    /// 失败批量请求数
    pub failed_batch_requests: u64,
    /// 系统启动时间
    pub system_start_time: chrono::DateTime<chrono::Utc>,
}

impl SystemStats {
    /// 计算成功率
    pub fn success_rate(&self) -> f64 {
        if self.total_requests > 0 {
            self.successful_requests as f64 / self.total_requests as f64
        } else {
            0.0
        }
    }

    /// 计算批量成功率
    pub fn batch_success_rate(&self) -> f64 {
        if self.total_batch_requests > 0 {
            self.successful_batch_requests as f64 / self.total_batch_requests as f64
        } else {
            0.0
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    fn create_test_manager_config() -> ModelManagerConfig {
        ModelManagerConfig {
            load_balancer: LoadBalancerConfig::default(),
            default_performance: PerformanceConfig::default(),
            monitoring: MonitoringConfig {
                enabled: false, // 禁用监控避免后台任务
                ..Default::default()
            },
            auto_discovery: AutoDiscoveryConfig {
                enabled: false, // 禁用自动发现
                ..Default::default()
            },
        }
    }

    #[tokio::test]
    async fn test_model_manager_creation() {
        let config = create_test_manager_config();
        let manager = ModelManager::new(config).await;
        assert!(manager.is_ok());

        let manager = manager.unwrap();
        let models = manager.list_models().await;
        assert!(models.is_empty());
    }

    #[tokio::test]
    async fn test_model_registration_and_listing() {
        let config = create_test_manager_config();
        let manager = ModelManager::new(config).await.unwrap();

        // 创建测试模型配置
        let model_config = ModelConfig::new(
            "test-model".to_string(),
            EmbedderType::Local,
            "test-local-model".to_string(),
            768,
        );

        // 注册模型（这会失败因为没有实际的模型实现，但我们可以测试接口）
        let result = manager.register_model(model_config).await;
        // 由于没有实际的本地模型，这应该失败
        assert!(result.is_err());

        // 测试列表功能
        let _models = manager.list_models().await;
        // 即使注册失败，模型ID也可能被添加到注册表中
        // 具体行为取决于实现细节
    }

    #[tokio::test]
    async fn test_default_model_management() {
        let config = create_test_manager_config();
        let manager = ModelManager::new(config).await.unwrap();

        // 初始状态下没有默认模型
        let default_model = manager.get_default_model().await;
        assert!(default_model.is_none());

        // 尝试设置不存在的模型为默认（应该失败）
        let result = manager.set_default_model("nonexistent").await;
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_system_stats() {
        let config = create_test_manager_config();
        let manager = ModelManager::new(config).await.unwrap();

        let stats = manager.get_system_stats().await;
        assert_eq!(stats.total_registered_models, 0);
        assert_eq!(stats.total_requests, 0);
        assert_eq!(stats.successful_requests, 0);
        assert_eq!(stats.failed_requests, 0);
        assert_eq!(stats.success_rate(), 0.0);
    }

    #[tokio::test]
    async fn test_registry_summary() {
        let config = create_test_manager_config();
        let manager = ModelManager::new(config).await.unwrap();

        let summary = manager.get_registry_summary().await;
        assert_eq!(summary.total_models, 0);
        assert_eq!(summary.available_models, 0);
        assert_eq!(summary.unavailable_models, 0);
        assert_eq!(summary.error_models, 0);
    }

    #[tokio::test]
    async fn test_load_balancer_stats() {
        let config = create_test_manager_config();
        let manager = ModelManager::new(config).await.unwrap();

        let stats = manager.get_load_balancer_stats().await;
        assert_eq!(stats.total_connections, 0);
        assert!(stats.model_connections.is_empty());
        assert!(stats.circuit_breaker_states.is_empty());
    }

    #[test]
    fn test_system_stats_calculations() {
        let mut stats = SystemStats::default();

        // 测试初始状态
        assert_eq!(stats.success_rate(), 0.0);
        assert_eq!(stats.batch_success_rate(), 0.0);

        // 添加一些请求
        stats.total_requests = 10;
        stats.successful_requests = 8;
        stats.failed_requests = 2;

        assert_eq!(stats.success_rate(), 0.8);

        // 添加批量请求
        stats.total_batch_requests = 5;
        stats.successful_batch_requests = 4;
        stats.failed_batch_requests = 1;

        assert_eq!(stats.batch_success_rate(), 0.8);
    }

    #[test]
    fn test_monitoring_config() {
        let config = MonitoringConfig {
            enabled: true,
            metrics_interval_seconds: 30,
            report_interval_seconds: 120,
            verbose_logging: true,
        };

        // 测试序列化
        let serialized = serde_json::to_string(&config).unwrap();
        let deserialized: MonitoringConfig = serde_json::from_str(&serialized).unwrap();

        assert_eq!(config.enabled, deserialized.enabled);
        assert_eq!(config.metrics_interval_seconds, deserialized.metrics_interval_seconds);
        assert_eq!(config.report_interval_seconds, deserialized.report_interval_seconds);
        assert_eq!(config.verbose_logging, deserialized.verbose_logging);
    }

    #[test]
    fn test_auto_discovery_config() {
        let config = AutoDiscoveryConfig::default();

        assert!(config.enabled);
        assert_eq!(config.discovery_interval_seconds, 600);
        assert!(!config.predefined_models.is_empty());

        // 检查预定义模型
        let local_model = config.predefined_models.iter()
            .find(|m| m.model_type == EmbedderType::Local);
        assert!(local_model.is_some());

        let openai_model = config.predefined_models.iter()
            .find(|m| m.model_type == EmbedderType::OpenAI);
        assert!(openai_model.is_some());
    }

    #[test]
    fn test_predefined_model_config() {
        let config = PredefinedModelConfig {
            model_id: "test-model".to_string(),
            model_type: EmbedderType::Local,
            model_name: "test".to_string(),
            dimension: 512,
            priority: 5,
            auto_register: true,
            tags: [("env".to_string(), "test".to_string())].into(),
        };

        // 测试序列化
        let serialized = serde_json::to_string(&config).unwrap();
        let deserialized: PredefinedModelConfig = serde_json::from_str(&serialized).unwrap();

        assert_eq!(config.model_id, deserialized.model_id);
        assert_eq!(config.model_type, deserialized.model_type);
        assert_eq!(config.dimension, deserialized.dimension);
        assert_eq!(config.priority, deserialized.priority);
        assert_eq!(config.auto_register, deserialized.auto_register);
    }

    #[test]
    fn test_model_manager_config_default() {
        let config = ModelManagerConfig::default();

        // 验证默认配置
        assert!(matches!(config.load_balancer.strategy, crate::embedding::load_balancer::LoadBalancingStrategy::PerformanceBased));
        assert!(config.monitoring.enabled);
        assert!(config.auto_discovery.enabled);
        assert!(!config.auto_discovery.predefined_models.is_empty());
    }

    #[tokio::test]
    async fn test_health_check_interface() {
        let config = create_test_manager_config();
        let manager = ModelManager::new(config).await.unwrap();

        // 测试全局健康检查
        let result = manager.health_check(None).await;
        assert!(result.is_ok());
        let health_results = result.unwrap();
        assert!(health_results.is_empty()); // 没有模型，所以结果为空

        // 测试特定模型健康检查
        let result = manager.health_check(Some("nonexistent")).await;
        assert!(result.is_ok());
        let health_results = result.unwrap();
        assert!(health_results.is_empty()); // 模型不存在
    }

    #[tokio::test]
    async fn test_embed_text_interface() {
        let config = create_test_manager_config();
        let manager = ModelManager::new(config).await.unwrap();

        // 尝试嵌入文本（应该失败，因为没有可用的模型）
        let result = manager.embed_text("test text", None).await;
        assert!(result.is_err());

        // 验证统计信息被更新
        let stats = manager.get_system_stats().await;
        assert_eq!(stats.total_requests, 1);
        assert_eq!(stats.failed_requests, 1);
        assert_eq!(stats.successful_requests, 0);
    }

    #[tokio::test]
    async fn test_embed_batch_interface() {
        let config = create_test_manager_config();
        let manager = ModelManager::new(config).await.unwrap();

        let texts = vec!["text1".to_string(), "text2".to_string()];

        // 尝试批量嵌入（应该失败，因为没有可用的模型）
        let result = manager.embed_batch(&texts, None).await;
        assert!(result.is_err());

        // 验证统计信息被更新
        let stats = manager.get_system_stats().await;
        assert_eq!(stats.total_batch_requests, 1);
        assert_eq!(stats.failed_batch_requests, 1);
        assert_eq!(stats.successful_batch_requests, 0);
    }
}
