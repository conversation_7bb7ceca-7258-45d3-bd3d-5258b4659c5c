//! # 搜索优化器
//!
//! 优化查询以提高搜索效果。

use crate::{
    config::QueryConfig,
    error::Result,
    query::{ProcessedQuery, QueryType},
};
use regex::Regex;
use std::collections::{HashMap, HashSet};
use serde::{Deserialize, Serialize};

/// 查询重写规则
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct QueryRewriteRule {
    /// 匹配模式
    pub pattern: String,
    /// 替换模板
    pub replacement: String,
    /// 规则权重
    pub weight: f32,
    /// 是否启用
    pub enabled: bool,
}

/// 查询优化配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OptimizerConfig {
    /// 是否启用查询重写
    pub enable_query_rewrite: bool,
    /// 是否启用停用词过滤
    pub enable_stop_word_filter: bool,
    /// 是否启用同义词扩展
    pub enable_synonym_expansion: bool,
    /// 是否启用词干提取
    pub enable_stemming: bool,
    /// 是否启用拼写纠正
    pub enable_spell_correction: bool,
    /// 同义词扩展的最大数量
    pub max_synonym_expansion: usize,
    /// 查询重写规则
    pub rewrite_rules: Vec<QueryRewriteRule>,
}

impl Default for OptimizerConfig {
    fn default() -> Self {
        Self {
            enable_query_rewrite: true,
            enable_stop_word_filter: true,
            enable_synonym_expansion: true,
            enable_stemming: true,
            enable_spell_correction: false, // 暂时禁用，需要更复杂的实现
            max_synonym_expansion: 3,
            rewrite_rules: Self::default_rewrite_rules(),
        }
    }
}

impl OptimizerConfig {
    /// 默认查询重写规则
    fn default_rewrite_rules() -> Vec<QueryRewriteRule> {
        vec![
            QueryRewriteRule {
                pattern: r"how to (.+)".to_string(),
                replacement: "$1 tutorial example".to_string(),
                weight: 1.2,
                enabled: true,
            },
            QueryRewriteRule {
                pattern: r"(.+) function".to_string(),
                replacement: "$1 method implementation".to_string(),
                weight: 1.1,
                enabled: true,
            },
            QueryRewriteRule {
                pattern: r"error (.+)".to_string(),
                replacement: "$1 exception handling debug".to_string(),
                weight: 1.3,
                enabled: true,
            },
        ]
    }
}

/// 搜索优化器
pub struct SearchOptimizer {
    config: QueryConfig,
    optimizer_config: OptimizerConfig,
    /// 停用词集合
    stop_words: HashSet<String>,
    /// 同义词词典
    synonyms: HashMap<String, Vec<String>>,
    /// 编程语言特定词典
    language_terms: HashMap<String, HashSet<String>>,
    /// 预编译的重写规则
    compiled_rules: Vec<(Regex, String, f32)>,
}

impl SearchOptimizer {
    /// 创建新的搜索优化器
    pub fn new(config: &QueryConfig) -> Self {
        let optimizer_config = OptimizerConfig::default();
        let compiled_rules = Self::compile_rules(&optimizer_config.rewrite_rules);

        Self {
            config: config.clone(),
            optimizer_config,
            stop_words: Self::load_stop_words(),
            synonyms: Self::load_synonyms(),
            language_terms: Self::load_language_terms(),
            compiled_rules,
        }
    }

    /// 使用自定义优化配置创建搜索优化器
    pub fn with_optimizer_config(config: &QueryConfig, optimizer_config: OptimizerConfig) -> Self {
        let compiled_rules = Self::compile_rules(&optimizer_config.rewrite_rules);

        Self {
            config: config.clone(),
            optimizer_config,
            stop_words: Self::load_stop_words(),
            synonyms: Self::load_synonyms(),
            language_terms: Self::load_language_terms(),
            compiled_rules,
        }
    }

    /// 编译重写规则
    fn compile_rules(rules: &[QueryRewriteRule]) -> Vec<(Regex, String, f32)> {
        rules.iter()
            .filter(|rule| rule.enabled)
            .filter_map(|rule| {
                Regex::new(&rule.pattern)
                    .ok()
                    .map(|regex| (regex, rule.replacement.clone(), rule.weight))
            })
            .collect()
    }

    /// 加载停用词
    fn load_stop_words() -> HashSet<String> {
        let mut stop_words = HashSet::new();

        // 英文停用词
        let english_stop_words = vec![
            "a", "an", "and", "are", "as", "at", "be", "by", "for", "from",
            "has", "he", "in", "is", "it", "its", "of", "on", "that", "the",
            "to", "was", "will", "with", "this", "but", "they", "have",
            "had", "what", "said", "each", "which", "she", "do", "how", "their",
            "if", "up", "out", "many", "then", "them", "these", "so", "some",
        ];

        for word in english_stop_words {
            stop_words.insert(word.to_string());
        }

        // 编程相关停用词
        let programming_stop_words = vec![
            "get", "set", "new", "old", "add", "remove", "delete", "update",
            "create", "make", "build", "run", "start", "stop", "end", "begin",
            "init", "setup", "config", "util", "helper", "manager", "handler",
        ];

        for word in programming_stop_words {
            stop_words.insert(word.to_string());
        }

        stop_words
    }

    /// 加载同义词词典
    fn load_synonyms() -> HashMap<String, Vec<String>> {
        let mut synonyms = HashMap::new();

        // 编程概念同义词
        synonyms.insert("function".to_string(), vec![
            "method".to_string(), "procedure".to_string(), "routine".to_string(),
            "subroutine".to_string(), "func".to_string(),
        ]);

        synonyms.insert("variable".to_string(), vec![
            "var".to_string(), "field".to_string(), "property".to_string(),
            "attribute".to_string(), "member".to_string(),
        ]);

        synonyms.insert("class".to_string(), vec![
            "type".to_string(), "struct".to_string(), "object".to_string(),
            "interface".to_string(), "component".to_string(),
        ]);

        synonyms.insert("error".to_string(), vec![
            "exception".to_string(), "failure".to_string(), "bug".to_string(),
            "issue".to_string(), "problem".to_string(),
        ]);

        synonyms.insert("implement".to_string(), vec![
            "code".to_string(), "develop".to_string(), "build".to_string(),
            "create".to_string(), "write".to_string(),
        ]);

        synonyms.insert("algorithm".to_string(), vec![
            "logic".to_string(), "approach".to_string(), "method".to_string(),
            "technique".to_string(), "strategy".to_string(),
        ]);

        synonyms
    }

    /// 加载编程语言特定术语
    fn load_language_terms() -> HashMap<String, HashSet<String>> {
        let mut terms = HashMap::new();

        // Rust 特定术语
        let mut rust_terms = HashSet::new();
        rust_terms.extend(vec![
            "ownership".to_string(), "borrowing".to_string(), "lifetime".to_string(),
            "trait".to_string(), "impl".to_string(), "macro".to_string(),
            "cargo".to_string(), "crate".to_string(), "module".to_string(),
        ]);
        terms.insert("rust".to_string(), rust_terms);

        // JavaScript 特定术语
        let mut js_terms = HashSet::new();
        js_terms.extend(vec![
            "promise".to_string(), "async".to_string(), "await".to_string(),
            "callback".to_string(), "closure".to_string(), "prototype".to_string(),
            "npm".to_string(), "node".to_string(), "react".to_string(),
        ]);
        terms.insert("javascript".to_string(), js_terms);

        // Python 特定术语
        let mut python_terms = HashSet::new();
        python_terms.extend(vec![
            "decorator".to_string(), "generator".to_string(), "comprehension".to_string(),
            "pip".to_string(), "virtualenv".to_string(), "django".to_string(),
            "flask".to_string(), "pandas".to_string(), "numpy".to_string(),
        ]);
        terms.insert("python".to_string(), python_terms);

        terms
    }

    /// 优化查询
    pub async fn optimize(&self, mut query: ProcessedQuery) -> Result<ProcessedQuery> {
        // 1. 查询重写
        if self.optimizer_config.enable_query_rewrite {
            query.text = self.rewrite_query(&query.text);
        }

        // 2. 停用词过滤
        if self.optimizer_config.enable_stop_word_filter {
            query.keywords = self.filter_stop_words(&query.keywords);
        }

        // 3. 词干提取
        if self.optimizer_config.enable_stemming {
            query.keywords = self.apply_stemming(&query.keywords);
        }

        // 4. 同义词扩展
        if self.optimizer_config.enable_synonym_expansion {
            query.keywords = self.expand_synonyms_advanced(&query.keywords);
        }

        // 5. 语言特定优化
        if let Some(ref language) = query.language_filter {
            query.keywords = self.apply_language_specific_optimization(&query.keywords, language);
        }

        // 6. 优化搜索范围
        query.top_k = self.optimize_top_k(query.top_k);

        // 7. 决定是否启用混合搜索
        query.enable_hybrid = self.should_enable_hybrid_advanced(&query);

        // 8. 去重和排序
        query.keywords = self.deduplicate_and_sort(&query.keywords);

        Ok(query)
    }

    /// 查询重写
    fn rewrite_query(&self, query_text: &str) -> String {
        let mut rewritten = query_text.to_string();

        for (regex, replacement, _weight) in &self.compiled_rules {
            if regex.is_match(&rewritten) {
                rewritten = regex.replace_all(&rewritten, replacement).to_string();
                // 只应用第一个匹配的规则
                break;
            }
        }

        rewritten
    }

    /// 过滤停用词
    fn filter_stop_words(&self, keywords: &[String]) -> Vec<String> {
        keywords.iter()
            .filter(|word| !self.stop_words.contains(&word.to_lowercase()))
            .cloned()
            .collect()
    }

    /// 应用词干提取
    fn apply_stemming(&self, keywords: &[String]) -> Vec<String> {
        keywords.iter()
            .map(|word| self.simple_stem(word))
            .collect()
    }

    /// 高级同义词扩展
    fn expand_synonyms_advanced(&self, keywords: &[String]) -> Vec<String> {
        let mut expanded = keywords.to_vec();
        let mut expansion_count = 0;

        for keyword in keywords {
            if expansion_count >= self.optimizer_config.max_synonym_expansion {
                break;
            }

            if let Some(synonyms) = self.synonyms.get(&keyword.to_lowercase()) {
                for synonym in synonyms.iter().take(2) { // 限制每个词最多2个同义词
                    if !expanded.contains(synonym) {
                        expanded.push(synonym.clone());
                        expansion_count += 1;

                        if expansion_count >= self.optimizer_config.max_synonym_expansion {
                            break;
                        }
                    }
                }
            }
        }

        expanded
    }

    /// 应用语言特定优化
    fn apply_language_specific_optimization(&self, keywords: &[String], language: &str) -> Vec<String> {
        let mut optimized = keywords.to_vec();

        if let Some(lang_terms) = self.language_terms.get(language) {
            // 为语言特定术语增加权重（通过重复添加）
            for keyword in keywords {
                if lang_terms.contains(&keyword.to_lowercase()) {
                    optimized.push(keyword.clone()); // 增加权重
                }
            }
        }

        optimized
    }

    /// 高级混合搜索决策
    fn should_enable_hybrid_advanced(&self, query: &ProcessedQuery) -> bool {
        match query.query_type {
            QueryType::Exact => false, // 精确匹配不使用混合搜索
            QueryType::Code => true,   // 代码搜索使用混合搜索
            QueryType::Symbol => {
                // 符号搜索：如果关键词少，使用混合搜索
                query.keywords.len() <= 3
            },
            QueryType::Documentation => true, // 文档搜索使用混合搜索
            QueryType::Semantic | QueryType::Hybrid => true, // 语义和混合搜索
        }
    }

    /// 去重和排序
    fn deduplicate_and_sort(&self, keywords: &[String]) -> Vec<String> {
        let mut unique_keywords: Vec<String> = keywords.iter()
            .map(|k| k.to_lowercase())
            .collect::<HashSet<_>>()
            .into_iter()
            .collect();

        unique_keywords.sort();
        unique_keywords
    }

    /// 优化搜索结果数量
    fn optimize_top_k(&self, requested_top_k: usize) -> usize {
        // 限制在配置的范围内
        requested_top_k
            .max(1)
            .min(self.config.max_top_k)
    }

    /// 简单的词干提取
    fn simple_stem(&self, word: &str) -> String {
        let word = word.to_lowercase();
        
        // 移除常见后缀
        if word.ends_with("ing") && word.len() > 6 {
            word[..word.len() - 3].to_string()
        } else if word.ends_with("ed") && word.len() > 5 {
            word[..word.len() - 2].to_string()
        } else if word.ends_with("er") && word.len() > 5 {
            word[..word.len() - 2].to_string()
        } else if word.ends_with("est") && word.len() > 6 {
            word[..word.len() - 3].to_string()
        } else if word.ends_with("ly") && word.len() > 5 {
            word[..word.len() - 2].to_string()
        } else if word.ends_with("tion") && word.len() > 7 {
            word[..word.len() - 4].to_string()
        } else if word.ends_with("sion") && word.len() > 7 {
            word[..word.len() - 4].to_string()
        } else {
            word
        }
    }


}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::query::{QueryType, ProcessedQuery};

    fn create_test_query() -> ProcessedQuery {
        ProcessedQuery {
            original_text: "find function hello".to_string(),
            text: "find function hello".to_string(),
            keywords: vec!["find".to_string(), "function".to_string(), "hello".to_string()],
            query_type: QueryType::Semantic,
            top_k: 10,
            enable_hybrid: true,
            language_filter: None,
            file_filter: None,
            symbol_filter: None,
        }
    }

    #[tokio::test]
    async fn test_query_optimization() {
        let config = QueryConfig::default();
        let optimizer = SearchOptimizer::new(&config);
        
        let query = create_test_query();
        let optimized = optimizer.optimize(query).await.unwrap();
        
        assert!(!optimized.keywords.is_empty());
        assert!(optimized.top_k <= config.max_top_k);
    }

    #[test]
    fn test_stop_word_filtering() {
        let config = QueryConfig::default();
        let optimizer = SearchOptimizer::new(&config);

        let keywords = vec!["the".to_string(), "function".to_string(), "and".to_string(), "hello".to_string()];
        let filtered = optimizer.filter_stop_words(&keywords);

        assert!(!filtered.contains(&"the".to_string()));
        assert!(!filtered.contains(&"and".to_string()));
        assert!(filtered.contains(&"function".to_string()));
        assert!(filtered.contains(&"hello".to_string()));
    }

    #[test]
    fn test_synonym_expansion() {
        let config = QueryConfig::default();
        let optimizer = SearchOptimizer::new(&config);

        let keywords = vec!["function".to_string()];
        let expanded = optimizer.expand_synonyms_advanced(&keywords);

        assert!(expanded.len() >= keywords.len());
        // 检查是否包含原始关键词
        assert!(expanded.contains(&"function".to_string()));
    }

    #[test]
    fn test_stemming() {
        let config = QueryConfig::default();
        let optimizer = SearchOptimizer::new(&config);

        let keywords = vec!["running".to_string(), "testing".to_string()];
        let stemmed = optimizer.apply_stemming(&keywords);

        assert!(stemmed.contains(&"runn".to_string())); // "running" -> "runn"
        assert!(stemmed.contains(&"test".to_string())); // "testing" -> "test"
    }
}