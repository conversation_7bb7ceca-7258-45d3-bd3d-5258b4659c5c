//! # 远程嵌入模型
//!
//! 支持 OpenAI、Cohere、HuggingFace、Ollama 和自定义 API 的嵌入模型。

use crate::{
    config::EmbeddingConfig,
    error::Result,
    embedding::{Embedding, EmbeddingProvider, ModelInfo, BatchEmbeddingResult},
    KnowledgeError,
};
use backoff::{ExponentialBackoff, future::retry};
use governor::{Quota, RateLimiter, state::{InMemoryState, NotKeyed}, clock::DefaultClock};
use reqwest::{Client, header::{HeaderMap, HeaderValue, AUTHORIZATION, CONTENT_TYPE}};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use std::time::{Duration, Instant};
use tracing::{debug, info, warn};

/// 远程 API 配置
#[derive(Debug, Clone)]
pub struct RemoteApiConfig {
    /// API 基础 URL
    pub base_url: String,
    /// API 密钥
    pub api_key: String,
    /// 模型名称
    pub model_name: String,
    /// 请求超时时间（秒）
    pub timeout_seconds: u64,
    /// 每分钟最大请求数
    pub max_requests_per_minute: u32,
    /// 最大重试次数
    pub max_retries: u32,
    /// 批量处理大小
    pub batch_size: usize,
}

impl Default for RemoteApiConfig {
    fn default() -> Self {
        Self {
            base_url: "https://api.openai.com/v1".to_string(),
            api_key: String::new(),
            model_name: "text-embedding-3-small".to_string(),
            timeout_seconds: 30,
            max_requests_per_minute: 60,
            max_retries: 3,
            batch_size: 100,
        }
    }
}

/// OpenAI API 请求结构
#[derive(Debug, Serialize)]
struct OpenAIEmbeddingRequest {
    input: Vec<String>,
    model: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    dimensions: Option<usize>,
}

/// OpenAI API 响应结构
#[derive(Debug, Deserialize)]
struct OpenAIEmbeddingResponse {
    data: Vec<OpenAIEmbeddingData>,
    usage: OpenAIUsage,
}

#[derive(Debug, Deserialize)]
struct OpenAIEmbeddingData {
    embedding: Vec<f32>,
    index: usize,
}

#[derive(Debug, Deserialize)]
struct OpenAIUsage {
    total_tokens: u32,
}

/// Cohere API 请求结构
#[derive(Debug, Serialize)]
struct CohereEmbeddingRequest {
    texts: Vec<String>,
    model: String,
    input_type: String,
}

/// Cohere API 响应结构
#[derive(Debug, Deserialize)]
struct CohereEmbeddingResponse {
    embeddings: Vec<Vec<f32>>,
    meta: CohereMetadata,
}

#[derive(Debug, Deserialize)]
struct CohereMetadata {
    billed_units: CohereBilledUnits,
}

#[derive(Debug, Deserialize)]
struct CohereBilledUnits {
    input_tokens: u32,
}


/// OpenAI 嵌入模型
#[derive(Debug)]
pub struct OpenAIEmbedder {
    config: EmbeddingConfig,
    api_config: RemoteApiConfig,
    model_info: ModelInfo,
    client: Client,
    rate_limiter: Arc<RateLimiter<NotKeyed, InMemoryState, DefaultClock>>,
}

impl OpenAIEmbedder {
    /// 创建新的 OpenAI 嵌入器
    pub async fn new(config: &EmbeddingConfig) -> Result<Self> {
        let api_config = Self::parse_api_config(config)?;

        // 验证 API 密钥
        if api_config.api_key.is_empty() {
            return Err(KnowledgeError::embedding_error("OpenAI API 密钥不能为空"));
        }

        let model_info = ModelInfo {
            name: api_config.model_name.clone(),
            version: "1.0.0".to_string(),
            model_type: "openai".to_string(),
        };

        // 创建 HTTP 客户端
        let client = Client::builder()
            .timeout(Duration::from_secs(api_config.timeout_seconds))
            .build()
            .map_err(|e| KnowledgeError::embedding_error(&format!("创建 HTTP 客户端失败: {}", e)))?;

        // 创建速率限制器
        let requests_per_minute = api_config.max_requests_per_minute.max(1); // 确保至少为1
        let quota = Quota::per_minute(std::num::NonZeroU32::new(requests_per_minute).unwrap());
        let rate_limiter = Arc::new(RateLimiter::direct(quota));

        info!("OpenAI 嵌入器初始化完成，模型: {}", api_config.model_name);

        Ok(Self {
            config: config.clone(),
            api_config,
            model_info,
            client,
            rate_limiter,
        })
    }

    /// 使用自定义 API 配置创建 OpenAI 嵌入器
    pub async fn with_api_config(config: &EmbeddingConfig, api_config: RemoteApiConfig) -> Result<Self> {
        let model_info = ModelInfo {
            name: api_config.model_name.clone(),
            version: "1.0.0".to_string(),
            model_type: "openai".to_string(),
        };

        let client = Client::builder()
            .timeout(Duration::from_secs(api_config.timeout_seconds))
            .build()
            .map_err(|e| KnowledgeError::embedding_error(&format!("创建 HTTP 客户端失败: {}", e)))?;

        let quota = Quota::per_minute(std::num::NonZeroU32::new(api_config.max_requests_per_minute).unwrap());
        let rate_limiter = Arc::new(RateLimiter::direct(quota));

        Ok(Self {
            config: config.clone(),
            api_config,
            model_info,
            client,
            rate_limiter,
        })
    }

    /// 解析 API 配置
    fn parse_api_config(config: &EmbeddingConfig) -> Result<RemoteApiConfig> {
        let mut api_config = RemoteApiConfig::default();

        // 从环境变量或配置中获取 API 密钥
        api_config.api_key = std::env::var("OPENAI_API_KEY")
            .or_else(|_| std::env::var("OPENAI_TOKEN"))
            .unwrap_or_default();

        // 从模型路径解析模型名称
        if !config.model_path.is_empty() && config.model_path != "openai" {
            api_config.model_name = config.model_path.clone();
        }

        Ok(api_config)
    }

    /// 调用 OpenAI API 获取嵌入向量
    async fn call_openai_api(&self, texts: &[String]) -> Result<Vec<Vec<f32>>> {
        // 应用速率限制
        self.rate_limiter.until_ready().await;

        let request = OpenAIEmbeddingRequest {
            input: texts.to_vec(),
            model: self.api_config.model_name.clone(),
            dimensions: if self.config.dimension != 1536 {
                Some(self.config.dimension)
            } else {
                None
            },
        };

        let mut headers = HeaderMap::new();
        headers.insert(
            AUTHORIZATION,
            HeaderValue::from_str(&format!("Bearer {}", self.api_config.api_key))
                .map_err(|e| KnowledgeError::embedding_error(&format!("无效的 API 密钥格式: {}", e)))?
        );
        headers.insert(CONTENT_TYPE, HeaderValue::from_static("application/json"));

        let url = format!("{}/embeddings", self.api_config.base_url);

        // 使用重试机制
        let response = retry(ExponentialBackoff::default(), || async {
            let response = self.client
                .post(&url)
                .headers(headers.clone())
                .json(&request)
                .send()
                .await
                .map_err(|e| {
                    warn!("OpenAI API 请求失败: {}", e);
                    backoff::Error::transient(KnowledgeError::embedding_error(&format!("API 请求失败: {}", e)))
                })?;

            if !response.status().is_success() {
                let status = response.status();
                let error_text = response.text().await.unwrap_or_default();
                warn!("OpenAI API 返回错误: {} - {}", status, error_text);

                // 根据状态码决定是否重试
                if status.is_server_error() || status == 429 {
                    return Err(backoff::Error::transient(
                        KnowledgeError::embedding_error(&format!("API 错误 {}: {}", status, error_text))
                    ));
                } else {
                    return Err(backoff::Error::permanent(
                        KnowledgeError::embedding_error(&format!("API 错误 {}: {}", status, error_text))
                    ));
                }
            }

            Ok(response)
        }).await?;

        let api_response: OpenAIEmbeddingResponse = response.json().await
            .map_err(|e| KnowledgeError::embedding_error(&format!("解析 API 响应失败: {}", e)))?;

        debug!("OpenAI API 调用成功，使用 {} tokens", api_response.usage.total_tokens);

        // 按索引排序并提取嵌入向量
        let mut embeddings = vec![Vec::new(); texts.len()];
        for data in api_response.data {
            if data.index < embeddings.len() {
                embeddings[data.index] = data.embedding;
            }
        }

        Ok(embeddings)
    }
}

#[async_trait::async_trait]
impl EmbeddingProvider for OpenAIEmbedder {
    async fn embed_text(&self, text: &str) -> Result<Embedding> {
        let start_time = Instant::now();

        // 调用批量接口处理单个文本
        let texts = vec![text.to_string()];
        let embeddings = self.call_openai_api(&texts).await?;

        if embeddings.is_empty() || embeddings[0].is_empty() {
            return Err(KnowledgeError::embedding_error("API 返回空的嵌入向量"));
        }

        let duration = start_time.elapsed();
        debug!("OpenAI 单文本嵌入计算耗时: {:?}", duration);

        Ok(Embedding {
            vector: embeddings[0].clone(),
            dimension: self.config.dimension,
            model_info: self.model_info.clone(),
            created_at: chrono::Utc::now(),
        })
    }

    async fn embed_batch(&self, texts: &[String]) -> Result<BatchEmbeddingResult> {
        let start_time = Instant::now();
        let mut all_embeddings = Vec::new();
        let mut success_count = 0;
        let mut error_count = 0;

        // 按批次大小分割文本
        for chunk in texts.chunks(self.api_config.batch_size) {
            match self.call_openai_api(chunk).await {
                Ok(embeddings) => {
                    for (i, embedding_vec) in embeddings.into_iter().enumerate() {
                        if !embedding_vec.is_empty() {
                            all_embeddings.push(Embedding {
                                vector: embedding_vec,
                                dimension: self.config.dimension,
                                model_info: self.model_info.clone(),
                                created_at: chrono::Utc::now(),
                            });
                            success_count += 1;
                        } else {
                            warn!("批次中第 {} 个文本的嵌入向量为空", i);
                            error_count += 1;
                        }
                    }
                }
                Err(e) => {
                    warn!("批次嵌入计算失败: {}", e);
                    error_count += chunk.len();
                }
            }
        }

        let duration_ms = start_time.elapsed().as_millis() as u64;
        info!("OpenAI 批量嵌入计算完成: {} 成功, {} 失败, 耗时: {}ms",
              success_count, error_count, duration_ms);

        Ok(BatchEmbeddingResult {
            embeddings: all_embeddings,
            duration_ms,
            success_count,
            error_count,
        })
    }

    fn model_info(&self) -> &ModelInfo {
        &self.model_info
    }

    fn dimension(&self) -> usize {
        self.config.dimension
    }
}

/// Cohere 嵌入模型
#[derive(Debug)]
pub struct CohereEmbedder {
    config: EmbeddingConfig,
    api_config: RemoteApiConfig,
    model_info: ModelInfo,
    client: Client,
    rate_limiter: Arc<RateLimiter<NotKeyed, InMemoryState, DefaultClock>>,
}

impl CohereEmbedder {
    /// 创建新的 Cohere 嵌入器
    pub async fn new(config: &EmbeddingConfig) -> Result<Self> {
        let api_config = Self::parse_api_config(config)?;

        if api_config.api_key.is_empty() {
            return Err(KnowledgeError::embedding_error("Cohere API 密钥不能为空"));
        }

        let model_info = ModelInfo {
            name: api_config.model_name.clone(),
            version: "1.0.0".to_string(),
            model_type: "cohere".to_string(),
        };

        let client = Client::builder()
            .timeout(Duration::from_secs(api_config.timeout_seconds))
            .build()
            .map_err(|e| KnowledgeError::embedding_error(&format!("创建 HTTP 客户端失败: {}", e)))?;

        let requests_per_minute = api_config.max_requests_per_minute.max(1);
        let quota = Quota::per_minute(std::num::NonZeroU32::new(requests_per_minute).unwrap());
        let rate_limiter = Arc::new(RateLimiter::direct(quota));

        info!("Cohere 嵌入器初始化完成，模型: {}", api_config.model_name);

        Ok(Self {
            config: config.clone(),
            api_config,
            model_info,
            client,
            rate_limiter,
        })
    }

    /// 解析 API 配置
    fn parse_api_config(config: &EmbeddingConfig) -> Result<RemoteApiConfig> {
        let mut api_config = RemoteApiConfig {
            base_url: "https://api.cohere.ai/v1".to_string(),
            model_name: "embed-english-v3.0".to_string(),
            ..Default::default()
        };

        api_config.api_key = std::env::var("COHERE_API_KEY")
            .or_else(|_| std::env::var("CO_API_KEY"))
            .unwrap_or_default();

        if !config.model_path.is_empty() && config.model_path != "cohere" {
            api_config.model_name = config.model_path.clone();
        }

        Ok(api_config)
    }

    /// 调用 Cohere API 获取嵌入向量
    async fn call_cohere_api(&self, texts: &[String]) -> Result<Vec<Vec<f32>>> {
        self.rate_limiter.until_ready().await;

        let request = CohereEmbeddingRequest {
            texts: texts.to_vec(),
            model: self.api_config.model_name.clone(),
            input_type: "search_document".to_string(),
        };

        let mut headers = HeaderMap::new();
        headers.insert(
            AUTHORIZATION,
            HeaderValue::from_str(&format!("Bearer {}", self.api_config.api_key))
                .map_err(|e| KnowledgeError::embedding_error(&format!("无效的 API 密钥格式: {}", e)))?
        );
        headers.insert(CONTENT_TYPE, HeaderValue::from_static("application/json"));

        let url = format!("{}/embed", self.api_config.base_url);

        let response = retry(ExponentialBackoff::default(), || async {
            let response = self.client
                .post(&url)
                .headers(headers.clone())
                .json(&request)
                .send()
                .await
                .map_err(|e| {
                    warn!("Cohere API 请求失败: {}", e);
                    backoff::Error::transient(KnowledgeError::embedding_error(&format!("API 请求失败: {}", e)))
                })?;

            if !response.status().is_success() {
                let status = response.status();
                let error_text = response.text().await.unwrap_or_default();
                warn!("Cohere API 返回错误: {} - {}", status, error_text);

                if status.is_server_error() || status == 429 {
                    return Err(backoff::Error::transient(
                        KnowledgeError::embedding_error(&format!("API 错误 {}: {}", status, error_text))
                    ));
                } else {
                    return Err(backoff::Error::permanent(
                        KnowledgeError::embedding_error(&format!("API 错误 {}: {}", status, error_text))
                    ));
                }
            }

            Ok(response)
        }).await?;

        let api_response: CohereEmbeddingResponse = response.json().await
            .map_err(|e| KnowledgeError::embedding_error(&format!("解析 API 响应失败: {}", e)))?;

        debug!("Cohere API 调用成功，使用 {} tokens", api_response.meta.billed_units.input_tokens);

        Ok(api_response.embeddings)
    }
}

#[async_trait::async_trait]
impl EmbeddingProvider for CohereEmbedder {
    async fn embed_text(&self, text: &str) -> Result<Embedding> {
        let start_time = Instant::now();

        let texts = vec![text.to_string()];
        let embeddings = self.call_cohere_api(&texts).await?;

        if embeddings.is_empty() || embeddings[0].is_empty() {
            return Err(KnowledgeError::embedding_error("API 返回空的嵌入向量"));
        }

        let duration = start_time.elapsed();
        debug!("Cohere 单文本嵌入计算耗时: {:?}", duration);

        Ok(Embedding {
            vector: embeddings[0].clone(),
            dimension: self.config.dimension,
            model_info: self.model_info.clone(),
            created_at: chrono::Utc::now(),
        })
    }

    async fn embed_batch(&self, texts: &[String]) -> Result<BatchEmbeddingResult> {
        let start_time = Instant::now();
        let mut all_embeddings = Vec::new();
        let mut success_count = 0;
        let mut error_count = 0;

        for chunk in texts.chunks(self.api_config.batch_size) {
            match self.call_cohere_api(chunk).await {
                Ok(embeddings) => {
                    for embedding_vec in embeddings {
                        if !embedding_vec.is_empty() {
                            all_embeddings.push(Embedding {
                                vector: embedding_vec,
                                dimension: self.config.dimension,
                                model_info: self.model_info.clone(),
                                created_at: chrono::Utc::now(),
                            });
                            success_count += 1;
                        } else {
                            error_count += 1;
                        }
                    }
                }
                Err(e) => {
                    warn!("批次嵌入计算失败: {}", e);
                    error_count += chunk.len();
                }
            }
        }

        let duration_ms = start_time.elapsed().as_millis() as u64;
        info!("Cohere 批量嵌入计算完成: {} 成功, {} 失败, 耗时: {}ms",
              success_count, error_count, duration_ms);

        Ok(BatchEmbeddingResult {
            embeddings: all_embeddings,
            duration_ms,
            success_count,
            error_count,
        })
    }

    fn model_info(&self) -> &ModelInfo {
        &self.model_info
    }

    fn dimension(&self) -> usize {
        self.config.dimension
    }
}

/// Ollama 嵌入模型
#[derive(Debug)]
pub struct OllamaEmbedder {
    config: EmbeddingConfig,
    #[allow(dead_code)]
    api_config: RemoteApiConfig,
    model_info: ModelInfo,
    #[allow(dead_code)]
    client: Client,
}

impl OllamaEmbedder {
    pub async fn new(config: &EmbeddingConfig) -> Result<Self> {
        let api_config = RemoteApiConfig {
            base_url: "http://localhost:11434".to_string(),
            model_name: "nomic-embed-text".to_string(),
            api_key: String::new(), // Ollama 通常不需要 API 密钥
            ..Default::default()
        };

        let model_info = ModelInfo {
            name: api_config.model_name.clone(),
            version: "1.0.0".to_string(),
            model_type: "ollama".to_string(),
        };

        let client = Client::builder()
            .timeout(Duration::from_secs(api_config.timeout_seconds))
            .build()
            .map_err(|e| KnowledgeError::embedding_error(&format!("创建 HTTP 客户端失败: {}", e)))?;

        Ok(Self {
            config: config.clone(),
            api_config,
            model_info,
            client,
        })
    }
}

#[async_trait::async_trait]
impl EmbeddingProvider for OllamaEmbedder {
    async fn embed_text(&self, _text: &str) -> Result<Embedding> {
        // 这里应该实现实际的 Ollama API 调用
        tracing::warn!("Ollama embedder 尚未完全实现，使用模拟数据");
        
        let vector = vec![0.2; self.config.dimension];
        Ok(Embedding {
            vector,
            dimension: self.config.dimension,
            model_info: self.model_info.clone(),
            created_at: chrono::Utc::now(),
        })
    }

    async fn embed_batch(&self, texts: &[String]) -> Result<BatchEmbeddingResult> {
        let start_time = Instant::now();
        let mut embeddings = Vec::new();

        for text in texts {
            let embedding = self.embed_text(text).await?;
            embeddings.push(embedding);
        }

        let duration_ms = start_time.elapsed().as_millis() as u64;

        Ok(BatchEmbeddingResult {
            embeddings,
            duration_ms,
            success_count: texts.len(),
            error_count: 0,
        })
    }

    fn model_info(&self) -> &ModelInfo {
        &self.model_info
    }

    fn dimension(&self) -> usize {
        self.config.dimension
    }
}

/// 自定义 API 嵌入模型
pub struct CustomEmbedder {
    config: EmbeddingConfig,
    model_info: ModelInfo,
}

impl CustomEmbedder {
    pub async fn new(config: &EmbeddingConfig) -> Result<Self> {
        let model_info = ModelInfo {
            name: "custom-model".to_string(),
            version: "1.0.0".to_string(),
            model_type: "custom".to_string(),
        };

        Ok(Self {
            config: config.clone(),
            model_info,
        })
    }
}

#[async_trait::async_trait]
impl EmbeddingProvider for CustomEmbedder {
    async fn embed_text(&self, _text: &str) -> Result<Embedding> {
        // 这里应该实现自定义 API 调用
        tracing::warn!("Custom embedder 尚未完全实现，使用模拟数据");
        
        let vector = vec![0.3; self.config.dimension];
        Ok(Embedding {
            vector,
            dimension: self.config.dimension,
            model_info: self.model_info.clone(),
            created_at: chrono::Utc::now(),
        })
    }

    async fn embed_batch(&self, texts: &[String]) -> Result<BatchEmbeddingResult> {
        let start_time = Instant::now();
        let mut embeddings = Vec::new();

        for text in texts {
            let embedding = self.embed_text(text).await?;
            embeddings.push(embedding);
        }

        let duration_ms = start_time.elapsed().as_millis() as u64;

        Ok(BatchEmbeddingResult {
            embeddings,
            duration_ms,
            success_count: texts.len(),
            error_count: 0,
        })
    }

    fn model_info(&self) -> &ModelInfo {
        &self.model_info
    }

    fn dimension(&self) -> usize {
        self.config.dimension
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    fn create_test_config() -> EmbeddingConfig {
        let mut config = EmbeddingConfig::default();
        config.model_path = "text-embedding-3-small".to_string();
        config
    }

    #[test]
    fn test_remote_api_config_default() {
        let config = RemoteApiConfig::default();
        assert_eq!(config.base_url, "https://api.openai.com/v1");
        assert_eq!(config.model_name, "text-embedding-3-small");
        assert_eq!(config.timeout_seconds, 30);
        assert_eq!(config.max_requests_per_minute, 60);
    }

    #[tokio::test]
    async fn test_openai_embedder_creation_without_api_key() {
        let config = create_test_config();

        // 清除环境变量
        std::env::remove_var("OPENAI_API_KEY");
        std::env::remove_var("OPENAI_TOKEN");

        let result = OpenAIEmbedder::new(&config).await;
        assert!(result.is_err());

        let error_msg = result.unwrap_err().to_string();
        assert!(error_msg.contains("API 密钥不能为空"));
    }

    #[tokio::test]
    async fn test_openai_embedder_creation_with_api_key() {
        let config = create_test_config();

        // 设置测试 API 密钥
        std::env::set_var("OPENAI_API_KEY", "test-api-key");

        let result = OpenAIEmbedder::new(&config).await;
        assert!(result.is_ok());

        let embedder = result.unwrap();
        assert_eq!(embedder.model_info().model_type, "openai");
        assert_eq!(embedder.dimension(), config.dimension);

        // 清理环境变量
        std::env::remove_var("OPENAI_API_KEY");
    }

    #[tokio::test]
    async fn test_cohere_embedder_creation_without_api_key() {
        let config = create_test_config();

        std::env::remove_var("COHERE_API_KEY");
        std::env::remove_var("CO_API_KEY");

        let result = CohereEmbedder::new(&config).await;
        assert!(result.is_err());

        let error_msg = result.unwrap_err().to_string();
        assert!(error_msg.contains("API 密钥不能为空"));
    }

    #[tokio::test]
    async fn test_cohere_embedder_creation_with_api_key() {
        let config = create_test_config();

        std::env::set_var("COHERE_API_KEY", "test-api-key");

        let result = CohereEmbedder::new(&config).await;
        assert!(result.is_ok());

        let embedder = result.unwrap();
        assert_eq!(embedder.model_info().model_type, "cohere");
        assert_eq!(embedder.dimension(), config.dimension);

        std::env::remove_var("COHERE_API_KEY");
    }

    #[tokio::test]
    async fn test_ollama_embedder_creation() {
        let config = create_test_config();

        let result = OllamaEmbedder::new(&config).await;
        assert!(result.is_ok());

        let embedder = result.unwrap();
        assert_eq!(embedder.model_info().model_type, "ollama");
        assert_eq!(embedder.dimension(), config.dimension);
    }
}