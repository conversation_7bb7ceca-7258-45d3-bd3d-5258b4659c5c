//! # AST 解析器（简化版本）
//!
//! 简化的代码解析器，用于基本的结构分析。

use crate::{config::ParserConfig, error::Result, parser::ChunkType};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tree_sitter::{Parser, Node};

/// AST 节点
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AstNode {
    /// 节点类型
    pub node_type: String,
    /// 节点文本
    pub text: String,
    /// 开始位置
    pub start_byte: usize,
    /// 结束位置
    pub end_byte: usize,
    /// 开始行号
    pub start_line: usize,
    /// 结束行号
    pub end_line: usize,
    /// 子节点
    pub children: Vec<AstNode>,
    /// 元数据
    pub metadata: HashMap<String, String>,
}

/// 语法树
#[derive(Debug, Clone)]
pub struct SyntaxTree {
    /// 根节点
    pub root: AstNode,
    /// 编程语言
    pub language: String,
    /// 源代码
    pub source_code: String,
}

/// AST 解析器
pub struct AstParser {
    config: ParserConfig,
    parsers: HashMap<String, Box<dyn LanguageParser>>,
}

/// 语言解析器特征
pub trait LanguageParser: Send + Sync {
    /// 解析源代码
    fn parse(&self, source_code: &str) -> Result<SyntaxTree>;
    
    /// 获取支持的语言
    fn language(&self) -> &str;
    
    /// 提取符号信息
    fn extract_symbols(&self, ast: &SyntaxTree) -> Result<Vec<SymbolInfo>>;
}

/// 符号信息
#[derive(Debug, Clone)]
pub struct SymbolInfo {
    /// 符号名称
    pub name: String,
    /// 符号类型
    pub symbol_type: ChunkType,
    /// 开始行号
    pub start_line: usize,
    /// 结束行号
    pub end_line: usize,
    /// 父级符号
    pub parent: Option<String>,
    /// 可见性
    pub visibility: Option<String>,
    /// 修饰符
    pub modifiers: Vec<String>,
}

impl AstParser {
    /// 创建新的 AST 解析器
    pub fn new(config: &ParserConfig) -> Result<Self> {
        let mut parsers: HashMap<String, Box<dyn LanguageParser>> = HashMap::new();
        
        // 注册支持的语言解析器
        for language in &config.supported_languages {
            match language.as_str() {
                "rust" => {
                    parsers.insert(language.clone(), Box::new(RustParser::new()));
                }
                "javascript" | "typescript" => {
                    parsers.insert(language.clone(), Box::new(JavaScriptParser::new()));
                }
                "python" => {
                    parsers.insert(language.clone(), Box::new(PythonParser::new()));
                }
                "java" => {
                    parsers.insert(language.clone(), Box::new(JavaParser::new()));
                }
                "cpp" | "c" => {
                    parsers.insert(language.clone(), Box::new(CppParser::new()));
                }
                "go" => {
                    parsers.insert(language.clone(), Box::new(GoParser::new()));
                }
                _ => {
                    tracing::warn!("不支持的语言: {}", language);
                }
            }
        }

        Ok(Self {
            config: config.clone(),
            parsers,
        })
    }

    /// 解析源代码
    pub fn parse(&self, source_code: &str, language: &str) -> Result<SyntaxTree> {
        let parser = self.parsers.get(language)
            .ok_or_else(|| crate::KnowledgeError::parse_error(format!("不支持的语言: {}", language)))?;
        
        parser.parse(source_code)
    }

    /// 提取符号信息
    pub fn extract_symbols(&self, ast: &SyntaxTree) -> Result<Vec<SymbolInfo>> {
        let parser = self.parsers.get(&ast.language)
            .ok_or_else(|| crate::KnowledgeError::parse_error(format!("不支持的语言: {}", ast.language)))?;

        parser.extract_symbols(ast)
    }

    /// 获取当前配置
    pub fn get_config(&self) -> &ParserConfig {
        &self.config
    }

    /// 检查文件大小是否在允许范围内
    pub fn is_file_size_valid(&self, file_size: usize) -> bool {
        file_size <= self.config.max_file_size
    }

    /// 获取支持的语言列表
    pub fn supported_languages(&self) -> &[String] {
        &self.config.supported_languages
    }

    /// 检查文件是否支持解析
    pub fn is_supported(&self, language: &str) -> bool {
        self.parsers.contains_key(language)
    }

    /// 获取已注册的解析器语言列表
    pub fn registered_parsers(&self) -> Vec<String> {
        self.parsers.keys().cloned().collect()
    }
}

/// Rust 解析器
pub struct RustParser;

impl RustParser {
    pub fn new() -> Self {
        Self
    }

    /// 将 tree-sitter 节点转换为 AST 节点
    fn convert_node_to_ast(&self, node: &Node, source_code: &str) -> AstNode {
        let mut children = Vec::new();
        let mut cursor = node.walk();

        // 遍历子节点
        if cursor.goto_first_child() {
            loop {
                let child = cursor.node();
                children.push(self.convert_node_to_ast(&child, source_code));
                if !cursor.goto_next_sibling() {
                    break;
                }
            }
        }

        // 获取节点文本
        let text = node.utf8_text(source_code.as_bytes())
            .unwrap_or("")
            .to_string();

        // 计算行号
        let start_line = node.start_position().row + 1;
        let end_line = node.end_position().row + 1;

        AstNode {
            node_type: node.kind().to_string(),
            text,
            start_byte: node.start_byte(),
            end_byte: node.end_byte(),
            start_line,
            end_line,
            children,
            metadata: HashMap::new(),
        }
    }
}

impl LanguageParser for RustParser {
    fn parse(&self, source_code: &str) -> Result<SyntaxTree> {
        // 使用 tree-sitter-rust 进行实际解析
        let mut parser = Parser::new();
        let language = tree_sitter_rust::LANGUAGE.into();
        parser.set_language(&language)
            .map_err(|e| crate::KnowledgeError::parse_error(format!("设置 Rust 语言失败: {}", e)))?;

        let tree = parser.parse(source_code, None)
            .ok_or_else(|| crate::KnowledgeError::parse_error("解析 Rust 代码失败".to_string()))?;

        let root_node = tree.root_node();
        let root = self.convert_node_to_ast(&root_node, source_code);

        Ok(SyntaxTree {
            root,
            language: "rust".to_string(),
            source_code: source_code.to_string(),
        })
    }

    fn language(&self) -> &str {
        "rust"
    }

    fn extract_symbols(&self, ast: &SyntaxTree) -> Result<Vec<SymbolInfo>> {
        let mut symbols = Vec::new();
        self.extract_symbols_from_node(&ast.root, &mut symbols, None);
        Ok(symbols)
    }


}

impl RustParser {
    /// 从 AST 节点递归提取符号信息
    fn extract_symbols_from_node(
        &self,
        node: &AstNode,
        symbols: &mut Vec<SymbolInfo>,
        parent_symbol: Option<String>,
    ) {
        match node.node_type.as_str() {
            "function_item" => {
                if let Some(name) = self.extract_function_name_from_node(node) {
                    symbols.push(SymbolInfo {
                        name: name.clone(),
                        symbol_type: ChunkType::Function,
                        start_line: node.start_line,
                        end_line: node.end_line,
                        parent: parent_symbol.clone(),
                        visibility: self.extract_visibility_from_node(node),
                        modifiers: self.extract_modifiers_from_node(node),
                    });

                    // 递归处理子节点，传递当前函数名作为父符号
                    for child in &node.children {
                        self.extract_symbols_from_node(child, symbols, Some(name.clone()));
                    }
                }
            }
            "struct_item" => {
                if let Some(name) = self.extract_struct_name_from_node(node) {
                    symbols.push(SymbolInfo {
                        name: name.clone(),
                        symbol_type: ChunkType::Struct,
                        start_line: node.start_line,
                        end_line: node.end_line,
                        parent: parent_symbol.clone(),
                        visibility: self.extract_visibility_from_node(node),
                        modifiers: self.extract_modifiers_from_node(node),
                    });

                    // 递归处理子节点
                    for child in &node.children {
                        self.extract_symbols_from_node(child, symbols, Some(name.clone()));
                    }
                }
            }
            "impl_item" => {
                if let Some(name) = self.extract_impl_name_from_node(node) {
                    symbols.push(SymbolInfo {
                        name: name.clone(),
                        symbol_type: ChunkType::Implementation,
                        start_line: node.start_line,
                        end_line: node.end_line,
                        parent: parent_symbol.clone(),
                        visibility: Some("public".to_string()),
                        modifiers: Vec::new(),
                    });

                    // 递归处理子节点
                    for child in &node.children {
                        self.extract_symbols_from_node(child, symbols, Some(name.clone()));
                    }
                }
            }
            "mod_item" => {
                if let Some(name) = self.extract_mod_name_from_node(node) {
                    symbols.push(SymbolInfo {
                        name: name.clone(),
                        symbol_type: ChunkType::Module,
                        start_line: node.start_line,
                        end_line: node.end_line,
                        parent: parent_symbol.clone(),
                        visibility: self.extract_visibility_from_node(node),
                        modifiers: Vec::new(),
                    });

                    // 递归处理子节点
                    for child in &node.children {
                        self.extract_symbols_from_node(child, symbols, Some(name.clone()));
                    }
                }
            }
            _ => {
                // 对于其他节点类型，继续递归处理子节点
                for child in &node.children {
                    self.extract_symbols_from_node(child, symbols, parent_symbol.clone());
                }
            }
        }
    }



    /// 从 AST 节点提取函数名
    fn extract_function_name_from_node(&self, node: &AstNode) -> Option<String> {
        // 在 function_item 节点中查找 identifier 子节点
        for child in &node.children {
            if child.node_type == "identifier" {
                return Some(child.text.clone());
            }
        }
        None
    }

    /// 从 AST 节点提取结构体名
    fn extract_struct_name_from_node(&self, node: &AstNode) -> Option<String> {
        // 在 struct_item 节点中查找 type_identifier 子节点
        for child in &node.children {
            if child.node_type == "type_identifier" {
                return Some(child.text.clone());
            }
        }
        None
    }

    /// 从 AST 节点提取实现块名
    fn extract_impl_name_from_node(&self, node: &AstNode) -> Option<String> {
        // 在 impl_item 节点中查找类型标识符
        for child in &node.children {
            if child.node_type == "type_identifier" || child.node_type == "generic_type" {
                return Some(child.text.clone());
            }
        }
        Some("impl".to_string()) // 默认名称
    }

    /// 从 AST 节点提取模块名
    fn extract_mod_name_from_node(&self, node: &AstNode) -> Option<String> {
        // 在 mod_item 节点中查找 identifier 子节点
        for child in &node.children {
            if child.node_type == "identifier" {
                return Some(child.text.clone());
            }
        }
        None
    }

    /// 从 AST 节点提取可见性信息
    fn extract_visibility_from_node(&self, node: &AstNode) -> Option<String> {
        // 查找 visibility_modifier 子节点
        for child in &node.children {
            if child.node_type == "visibility_modifier" {
                return Some("public".to_string());
            }
        }
        Some("private".to_string())
    }

    /// 从 AST 节点提取修饰符
    fn extract_modifiers_from_node(&self, node: &AstNode) -> Vec<String> {
        let mut modifiers = Vec::new();

        // 查找各种修饰符
        for child in &node.children {
            match child.node_type.as_str() {
                "async" => modifiers.push("async".to_string()),
                "unsafe" => modifiers.push("unsafe".to_string()),
                "const" => modifiers.push("const".to_string()),
                "static" => modifiers.push("static".to_string()),
                _ => {}
            }
        }

        modifiers
    }
}

// 简化的其他语言解析器实现
pub struct JavaScriptParser;
impl JavaScriptParser {
    pub fn new() -> Self { Self }
}
impl LanguageParser for JavaScriptParser {
    fn parse(&self, source_code: &str) -> Result<SyntaxTree> {
        // 简化实现
        let root = AstNode {
            node_type: "program".to_string(),
            text: source_code.to_string(),
            start_byte: 0,
            end_byte: source_code.len(),
            start_line: 1,
            end_line: source_code.lines().count(),
            children: Vec::new(),
            metadata: HashMap::new(),
        };
        Ok(SyntaxTree { root, language: "javascript".to_string(), source_code: source_code.to_string() })
    }
    fn language(&self) -> &str { "javascript" }
    fn extract_symbols(&self, _ast: &SyntaxTree) -> Result<Vec<SymbolInfo>> { Ok(Vec::new()) }
}

pub struct PythonParser;
impl PythonParser {
    pub fn new() -> Self { Self }
}
impl LanguageParser for PythonParser {
    fn parse(&self, source_code: &str) -> Result<SyntaxTree> {
        let root = AstNode {
            node_type: "module".to_string(),
            text: source_code.to_string(),
            start_byte: 0,
            end_byte: source_code.len(),
            start_line: 1,
            end_line: source_code.lines().count(),
            children: Vec::new(),
            metadata: HashMap::new(),
        };
        Ok(SyntaxTree { root, language: "python".to_string(), source_code: source_code.to_string() })
    }
    fn language(&self) -> &str { "python" }
    fn extract_symbols(&self, _ast: &SyntaxTree) -> Result<Vec<SymbolInfo>> { Ok(Vec::new()) }
}

pub struct JavaParser;
impl JavaParser {
    pub fn new() -> Self { Self }
}
impl LanguageParser for JavaParser {
    fn parse(&self, source_code: &str) -> Result<SyntaxTree> {
        let root = AstNode {
            node_type: "compilation_unit".to_string(),
            text: source_code.to_string(),
            start_byte: 0,
            end_byte: source_code.len(),
            start_line: 1,
            end_line: source_code.lines().count(),
            children: Vec::new(),
            metadata: HashMap::new(),
        };
        Ok(SyntaxTree { root, language: "java".to_string(), source_code: source_code.to_string() })
    }
    fn language(&self) -> &str { "java" }
    fn extract_symbols(&self, _ast: &SyntaxTree) -> Result<Vec<SymbolInfo>> { Ok(Vec::new()) }
}

pub struct CppParser;
impl CppParser {
    pub fn new() -> Self { Self }
}
impl LanguageParser for CppParser {
    fn parse(&self, source_code: &str) -> Result<SyntaxTree> {
        let root = AstNode {
            node_type: "translation_unit".to_string(),
            text: source_code.to_string(),
            start_byte: 0,
            end_byte: source_code.len(),
            start_line: 1,
            end_line: source_code.lines().count(),
            children: Vec::new(),
            metadata: HashMap::new(),
        };
        Ok(SyntaxTree { root, language: "cpp".to_string(), source_code: source_code.to_string() })
    }
    fn language(&self) -> &str { "cpp" }
    fn extract_symbols(&self, _ast: &SyntaxTree) -> Result<Vec<SymbolInfo>> { Ok(Vec::new()) }
}

pub struct GoParser;
impl GoParser {
    pub fn new() -> Self { Self }
}
impl LanguageParser for GoParser {
    fn parse(&self, source_code: &str) -> Result<SyntaxTree> {
        let root = AstNode {
            node_type: "source_file".to_string(),
            text: source_code.to_string(),
            start_byte: 0,
            end_byte: source_code.len(),
            start_line: 1,
            end_line: source_code.lines().count(),
            children: Vec::new(),
            metadata: HashMap::new(),
        };
        Ok(SyntaxTree { root, language: "go".to_string(), source_code: source_code.to_string() })
    }
    fn language(&self) -> &str { "go" }
    fn extract_symbols(&self, _ast: &SyntaxTree) -> Result<Vec<SymbolInfo>> { Ok(Vec::new()) }
}

