//! # 代码解析模块
//!
//! 提供多语言代码解析、AST分析和语义分块功能。

pub mod ast_parser;
pub mod chunker;
pub mod dependency_analyzer;
pub mod language_detector;

#[cfg(test)]
mod tests;

use crate::{config::ParserConfig, error::Result};
use serde::{Deserialize, Serialize};
use std::path::{Path, PathBuf};

/// 代码块
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CodeChunk {
    /// 唯一标识符
    pub id: String,
    /// 文件路径
    pub file_path: PathBuf,
    /// 代码内容
    pub content: String,
    /// 编程语言
    pub language: String,
    /// 代码类型
    pub chunk_type: ChunkType,
    /// 开始行号
    pub start_line: usize,
    /// 结束行号
    pub end_line: usize,
    /// 函数/类名称
    pub symbol_name: Option<String>,
    /// 父级符号
    pub parent_symbol: Option<String>,
    /// 依赖关系
    pub dependencies: Vec<String>,
    /// 元数据
    pub metadata: std::collections::HashMap<String, String>,
    /// 创建时间
    pub created_at: chrono::DateTime<chrono::Utc>,
}

/// 代码块类型
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum ChunkType {
    /// 函数
    Function,
    /// 类
    Class,
    /// 结构体
    Struct,
    /// 接口
    Interface,
    /// 枚举
    Enum,
    /// 常量
    Constant,
    /// 变量
    Variable,
    /// 注释
    Comment,
    /// 导入语句
    Import,
    /// 文档
    Documentation,
    /// 模块
    Module,
    /// 实现块
    Implementation,
    /// 其他
    Other,
}

/// 代码解析器
pub struct CodeParser {
    config: ParserConfig,
    language_detector: language_detector::LanguageDetector,
    ast_parser: ast_parser::AstParser,
    chunker: chunker::SemanticChunker,
    dependency_analyzer: dependency_analyzer::DependencyAnalyzer,
}

impl CodeParser {
    /// 创建新的代码解析器
    pub fn new(config: &ParserConfig) -> Result<Self> {
        Ok(Self {
            config: config.clone(),
            language_detector: language_detector::LanguageDetector::new(),
            ast_parser: ast_parser::AstParser::new(config)?,
            chunker: chunker::SemanticChunker::new(config),
            dependency_analyzer: dependency_analyzer::DependencyAnalyzer::new(),
        })
    }

    /// 解析单个文件
    pub async fn parse_file(&self, file_path: &Path) -> Result<Vec<CodeChunk>> {
        // 检查文件大小
        let metadata = std::fs::metadata(file_path)?;
        if metadata.len() > self.config.max_file_size as u64 {
            return Err(crate::KnowledgeError::parse_error(format!(
                "文件 {} 太大: {} 字节，超过限制 {} 字节",
                file_path.display(),
                metadata.len(),
                self.config.max_file_size
            )));
        }

        // 读取文件内容
        let content = std::fs::read_to_string(file_path)?;
        
        // 检测编程语言
        let language = self.language_detector.detect_language(file_path, &content)?;
        
        // 检查是否支持该语言
        if !self.config.supported_languages.contains(&language) {
            return Err(crate::KnowledgeError::parse_error(format!(
                "不支持的编程语言: {}",
                language
            )));
        }

        // 解析 AST
        let ast = self.ast_parser.parse(&content, &language)?;

        // 语义分块
        let chunks = self.chunker.chunk_ast(&ast, file_path, &language)?;

        Ok(chunks)
    }

    /// 解析文件内容
    pub fn parse_file_content(&self, file_path: &Path, content: &str) -> Result<Vec<CodeChunk>> {
        // 检测编程语言
        let language = self.language_detector.detect_language(file_path, content)?;

        // 检查是否支持该语言
        if !self.config.supported_languages.contains(&language) {
            return Err(crate::KnowledgeError::parse_error(format!(
                "不支持的编程语言: {}",
                language
            )));
        }

        // 解析 AST
        let ast = self.ast_parser.parse(content, &language)?;

        // 语义分块
        let chunks = self.chunker.chunk_ast(&ast, file_path, &language)?;

        Ok(chunks)
    }

    /// 发现目录中的代码文件
    pub fn discover_files(&self, dir_path: &Path) -> Result<Vec<PathBuf>> {
        let mut files = Vec::new();
        
        for entry in walkdir::WalkDir::new(dir_path) {
            let entry = entry?;
            let path = entry.path();
            
            if path.is_file() {
                if let Some(language) = self.language_detector.detect_language_by_extension(path) {
                    if self.config.supported_languages.contains(&language) {
                        files.push(path.to_path_buf());
                    }
                }
            }
        }

        Ok(files)
    }

    /// 批量解析文件
    pub async fn parse_files(&self, file_paths: &[PathBuf]) -> Result<Vec<CodeChunk>> {
        let mut all_chunks = Vec::new();
        
        for file_path in file_paths {
            match self.parse_file(file_path).await {
                Ok(mut chunks) => {
                    all_chunks.append(&mut chunks);
                }
                Err(e) => {
                    tracing::warn!("解析文件 {} 失败: {}", file_path.display(), e);
                }
            }
        }

        Ok(all_chunks)
    }

    /// 分析代码依赖关系
    pub fn analyze_dependencies(&self, file_path: &Path, content: &str) -> Result<dependency_analyzer::DependencyGraph> {
        // 检测语言
        let language = self.language_detector.detect_language(file_path, content)?;

        // 解析 AST
        let ast = self.ast_parser.parse(content, &language)?;

        // 提取符号信息
        let symbols = self.ast_parser.extract_symbols(&ast)?;

        // 分析依赖关系
        self.dependency_analyzer.analyze(&ast, &symbols)
    }

    /// 获取支持的语言列表
    pub fn supported_languages(&self) -> &[String] {
        &self.config.supported_languages
    }

    /// 根据文件扩展名检测语言
    pub fn detect_language_by_extension(&self, file_path: &Path) -> Option<String> {
        self.language_detector.detect_language_by_extension(file_path)
    }
}

