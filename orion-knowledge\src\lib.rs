//! # Orion 智能代码知识库
//!
//! 基于 RAG（Retrieval-Augmented Generation）技术的代码智能平台，
//! 提供代码解析、语义索引、智能检索等功能。
//!
//! ## 架构概览
//!
//! ```text
//! ┌─────────────────────────────────────────────────────────────┐
//! │                    应用层 (Application Layer)               │
//! │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
//! │  │ 查询接口     │  │ 管理控制台   │  │ 可视化面板   │        │
//! │  └─────────────┘  └─────────────┘  └─────────────┘        │
//! ├─────────────────────────────────────────────────────────────┤
//! │                    服务层 (Service Layer)                   │
//! │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
//! │  │ 查询转换     │  │ 上下文合成   │  │ 结果排序     │        │
//! │  └─────────────┘  └─────────────┘  └─────────────┘        │
//! ├─────────────────────────────────────────────────────────────┤
//! │                    核心层 (Core Layer)                      │
//! │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
//! │  │ 语义分块     │  │ 向量存储     │  │ 嵌入模型     │        │
//! │  └─────────────┘  └─────────────┘  └─────────────┘        │
//! ├─────────────────────────────────────────────────────────────┤
//! │                    基础层 (Infrastructure Layer)            │
//! │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
//! │  │ 存储引擎     │  │ 计算引擎     │  │ 监控系统     │        │
//! │  └─────────────┘  └─────────────┘  └─────────────┘        │
//! └─────────────────────────────────────────────────────────────┘
//! ```
//!
//! ## 主要模块
//!
//! - [`parser`] - 代码解析层，提供多语言AST解析和语义分块
//! - [`embedding`] - 嵌入计算层，管理代码专用嵌入模型
//! - [`storage`] - 向量存储层，实现高性能本地向量数据库
//! - [`query`] - 查询处理层，提供意图理解和混合检索
//! - [`context`] - 上下文生成层，智能组装和压缩上下文
//!
//! ## 使用示例
//!
//! ```rust
//! use orion_knowledge::{KnowledgeBase, Config};
//!
//! #[tokio::main]
//! async fn main() -> anyhow::Result<()> {
//!     // 初始化知识库
//!     let config = Config::default();
//!     let mut kb = KnowledgeBase::new(config).await?;
//!     
//!     // 索引代码库
//!     kb.index_directory("./src").await?;
//!     
//!     // 语义搜索
//!     let results = kb.search("如何处理错误").await?;
//!     
//!     // 获取相关上下文
//!     let context = kb.get_context(&results).await?;
//!     
//!     Ok(())
//! }
//! ```

pub mod parser;
pub mod embedding;
pub mod storage;
pub mod query;
pub mod context;
pub mod config;

mod error;
mod knowledge_base;

pub use error::{KnowledgeError, Result};
pub use config::{
    Config, DatabaseConfig, ParserConfig, EmbeddingConfig, QueryConfig, ContextConfig,
    ChunkingStrategy, EmbeddingModelType
};
pub use knowledge_base::{
    KnowledgeBase, SearchResult, ContextInfo, KnowledgeStats, 
    LanguageStats, FileIndexStats, IndexPerformanceMetrics,
    FileMetadata, IndexStatus, BackupInfo
};

/// 知识库版本信息
pub const VERSION: &str = env!("CARGO_PKG_VERSION");

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_version() {
        assert!(!VERSION.is_empty());
    }
}
