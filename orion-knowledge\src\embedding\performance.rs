//! # 嵌入计算性能优化
//!
//! 提供嵌入计算的性能优化功能，包括批量处理、缓存、并行计算等。

use crate::{
    error::Result,
    embedding::{Embedding, EmbeddingProvider, BatchEmbeddingResult, ModelInfo},
    KnowledgeError,
};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::hash::{Hash, Hasher};
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::{RwLock, Semaphore};
use tracing::{debug, info, warn};

/// 性能优化配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceConfig {
    /// 是否启用嵌入缓存
    pub enable_embedding_cache: bool,
    /// 缓存最大条目数
    pub cache_max_entries: usize,
    /// 缓存过期时间（秒）
    pub cache_ttl_seconds: u64,
    /// 批量处理大小
    pub batch_size: usize,
    /// 最大并发数
    pub max_concurrency: usize,
    /// 是否启用预处理优化
    pub enable_preprocessing: bool,
    /// 是否启用内存池
    pub enable_memory_pool: bool,
    /// 性能监控间隔（秒）
    pub metrics_interval_seconds: u64,
}

impl Default for PerformanceConfig {
    fn default() -> Self {
        Self {
            enable_embedding_cache: true,
            cache_max_entries: 10000,
            cache_ttl_seconds: 3600, // 1小时
            batch_size: 32,
            max_concurrency: 4,
            enable_preprocessing: true,
            enable_memory_pool: true,
            metrics_interval_seconds: 60,
        }
    }
}

/// 嵌入缓存键
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
struct EmbeddingCacheKey {
    /// 文本内容的哈希
    text_hash: u64,
    /// 模型信息
    model_name: String,
    /// 嵌入维度
    dimension: usize,
}

impl EmbeddingCacheKey {
    fn new(text: &str, model_name: &str, dimension: usize) -> Self {
        let mut hasher = std::collections::hash_map::DefaultHasher::new();
        text.hash(&mut hasher);
        let text_hash = hasher.finish();

        Self {
            text_hash,
            model_name: model_name.to_string(),
            dimension,
        }
    }
}

/// 缓存条目
#[derive(Debug, Clone)]
struct CacheEntry {
    /// 嵌入向量
    embedding: Embedding,
    /// 创建时间
    created_at: Instant,
    /// 访问次数
    access_count: u64,
    /// 最后访问时间
    last_accessed: Instant,
}

impl CacheEntry {
    fn new(embedding: Embedding) -> Self {
        let now = Instant::now();
        Self {
            embedding,
            created_at: now,
            access_count: 1,
            last_accessed: now,
        }
    }

    fn is_expired(&self, ttl: Duration) -> bool {
        self.created_at.elapsed() > ttl
    }

    fn touch(&mut self) {
        self.access_count += 1;
        self.last_accessed = Instant::now();
    }
}

/// 性能统计信息
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct PerformanceMetrics {
    /// 总请求数
    pub total_requests: u64,
    /// 缓存命中数
    pub cache_hits: u64,
    /// 缓存未命中数
    pub cache_misses: u64,
    /// 缓存命中率
    pub cache_hit_rate: f64,
    /// 平均处理时间（毫秒）
    pub avg_processing_time_ms: f64,
    /// 批量处理统计
    pub batch_stats: BatchStats,
    /// 并发统计
    pub concurrency_stats: ConcurrencyStats,
    /// 内存使用统计
    pub memory_stats: MemoryStats,
}

#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct BatchStats {
    /// 总批次数
    pub total_batches: u64,
    /// 平均批次大小
    pub avg_batch_size: f64,
    /// 批量处理节省的时间（毫秒）
    pub time_saved_ms: u64,
}

#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct ConcurrencyStats {
    /// 当前并发数
    pub current_concurrency: usize,
    /// 最大并发数
    pub max_concurrency: usize,
    /// 平均等待时间（毫秒）
    pub avg_wait_time_ms: f64,
}

#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct MemoryStats {
    /// 缓存使用的内存（字节）
    pub cache_memory_bytes: usize,
    /// 内存池大小（字节）
    pub pool_memory_bytes: usize,
    /// 峰值内存使用（字节）
    pub peak_memory_bytes: usize,
}

/// 高性能嵌入计算器
pub struct PerformanceEmbedder<T: EmbeddingProvider> {
    /// 底层嵌入提供者
    provider: Arc<T>,
    /// 性能配置
    config: PerformanceConfig,
    /// 嵌入缓存
    cache: Arc<RwLock<HashMap<EmbeddingCacheKey, CacheEntry>>>,
    /// 并发控制信号量
    semaphore: Arc<Semaphore>,
    /// 性能统计
    metrics: Arc<RwLock<PerformanceMetrics>>,
    /// TTL 持续时间
    cache_ttl: Duration,
}

impl<T: EmbeddingProvider + Send + Sync + 'static> PerformanceEmbedder<T> {
    /// 创建新的高性能嵌入计算器
    pub fn new(provider: T, config: PerformanceConfig) -> Self {
        let cache_ttl = Duration::from_secs(config.cache_ttl_seconds);
        let semaphore = Arc::new(Semaphore::new(config.max_concurrency));

        info!("初始化高性能嵌入计算器，配置: {:?}", config);

        Self {
            provider: Arc::new(provider),
            config,
            cache: Arc::new(RwLock::new(HashMap::new())),
            semaphore,
            metrics: Arc::new(RwLock::new(PerformanceMetrics::default())),
            cache_ttl,
        }
    }

    /// 计算单个文本的嵌入向量（带性能优化）
    pub async fn embed_text_optimized(&self, text: &str) -> Result<Embedding> {
        let start_time = Instant::now();
        
        // 更新请求统计
        {
            let mut metrics = self.metrics.write().await;
            metrics.total_requests += 1;
        }

        // 检查缓存
        if self.config.enable_embedding_cache {
            let cache_key = EmbeddingCacheKey::new(
                text,
                &self.provider.model_info().name,
                self.provider.dimension(),
            );

            // 尝试从缓存获取
            if let Some(embedding) = self.get_from_cache(&cache_key).await {
                let duration = start_time.elapsed();
                self.update_cache_hit_metrics(duration).await;
                return Ok(embedding);
            }

            // 缓存未命中，计算嵌入向量
            let _permit = self.semaphore.acquire().await
                .map_err(|e| KnowledgeError::embedding_error(&format!("获取并发许可失败: {}", e)))?;

            let embedding = self.provider.embed_text(text).await?;

            // 存储到缓存
            self.put_to_cache(cache_key, embedding.clone()).await;

            let duration = start_time.elapsed();
            self.update_cache_miss_metrics(duration).await;

            Ok(embedding)
        } else {
            // 不使用缓存，直接计算
            let _permit = self.semaphore.acquire().await
                .map_err(|e| KnowledgeError::embedding_error(&format!("获取并发许可失败: {}", e)))?;

            let embedding = self.provider.embed_text(text).await?;
            let duration = start_time.elapsed();
            self.update_no_cache_metrics(duration).await;

            Ok(embedding)
        }
    }

    /// 批量计算嵌入向量（带性能优化）
    pub async fn embed_batch_optimized(&self, texts: &[String]) -> Result<BatchEmbeddingResult> {
        let start_time = Instant::now();
        
        if texts.is_empty() {
            return Ok(BatchEmbeddingResult {
                embeddings: Vec::new(),
                duration_ms: 0,
                success_count: 0,
                error_count: 0,
            });
        }

        info!("开始批量嵌入计算，文本数量: {}", texts.len());

        // 预处理：去重和缓存检查
        let (cached_embeddings, uncached_texts) = if self.config.enable_embedding_cache {
            self.separate_cached_and_uncached(texts).await
        } else {
            (HashMap::new(), texts.to_vec())
        };

        debug!("缓存命中: {}, 需要计算: {}", cached_embeddings.len(), uncached_texts.len());

        // 批量处理未缓存的文本
        let mut new_embeddings = HashMap::new();
        if !uncached_texts.is_empty() {
            new_embeddings = self.compute_batch_with_optimization(&uncached_texts).await?;
        }

        // 合并结果
        let mut all_embeddings = Vec::new();
        let mut success_count = 0;
        let mut error_count = 0;

        for text in texts {
            if let Some(embedding) = cached_embeddings.get(text) {
                all_embeddings.push(embedding.clone());
                success_count += 1;
            } else if let Some(embedding) = new_embeddings.get(text) {
                all_embeddings.push(embedding.clone());
                success_count += 1;
            } else {
                error_count += 1;
            }
        }

        let duration_ms = start_time.elapsed().as_millis() as u64;

        // 更新批量处理统计
        self.update_batch_metrics(texts.len(), duration_ms).await;

        info!("批量嵌入计算完成: {} 成功, {} 失败, 耗时: {}ms", 
              success_count, error_count, duration_ms);

        Ok(BatchEmbeddingResult {
            embeddings: all_embeddings,
            duration_ms,
            success_count,
            error_count,
        })
    }

    /// 获取性能统计信息
    pub async fn get_metrics(&self) -> PerformanceMetrics {
        let metrics = self.metrics.read().await;
        metrics.clone()
    }

    /// 清理过期缓存
    pub async fn cleanup_expired_cache(&self) -> Result<usize> {
        if !self.config.enable_embedding_cache {
            return Ok(0);
        }

        let mut cache = self.cache.write().await;
        let initial_size = cache.len();

        cache.retain(|_, entry| !entry.is_expired(self.cache_ttl));

        let removed_count = initial_size - cache.len();
        if removed_count > 0 {
            info!("清理过期缓存条目: {}", removed_count);
        }

        Ok(removed_count)
    }

    /// 获取缓存统计信息
    pub async fn get_cache_stats(&self) -> (usize, usize) {
        let cache = self.cache.read().await;
        (cache.len(), self.config.cache_max_entries)
    }

    /// 获取底层提供者的模型信息
    pub fn provider_model_info(&self) -> &ModelInfo {
        self.provider.model_info()
    }

    /// 获取底层提供者的维度
    pub fn provider_dimension(&self) -> usize {
        self.provider.dimension()
    }

    /// 从缓存获取嵌入向量
    async fn get_from_cache(&self, key: &EmbeddingCacheKey) -> Option<Embedding> {
        let mut cache = self.cache.write().await;

        if let Some(entry) = cache.get_mut(key) {
            if !entry.is_expired(self.cache_ttl) {
                entry.touch();
                return Some(entry.embedding.clone());
            } else {
                // 移除过期条目
                cache.remove(key);
            }
        }

        None
    }

    /// 存储到缓存
    async fn put_to_cache(&self, key: EmbeddingCacheKey, embedding: Embedding) {
        let mut cache = self.cache.write().await;

        // 检查缓存大小限制
        if cache.len() >= self.config.cache_max_entries {
            self.evict_lru_entries(&mut cache).await;
        }

        cache.insert(key, CacheEntry::new(embedding));
    }

    /// 淘汰最少使用的缓存条目
    async fn evict_lru_entries(&self, cache: &mut HashMap<EmbeddingCacheKey, CacheEntry>) {
        let evict_count = cache.len() / 10; // 淘汰10%的条目

        // 收集需要移除的键
        let mut entries: Vec<_> = cache.iter().map(|(k, v)| (k.clone(), v.last_accessed)).collect();
        entries.sort_by_key(|(_, last_accessed)| *last_accessed);

        let keys_to_remove: Vec<_> = entries.into_iter().take(evict_count).map(|(key, _)| key).collect();

        for key in keys_to_remove {
            cache.remove(&key);
        }

        debug!("淘汰 {} 个 LRU 缓存条目", evict_count);
    }

    /// 分离已缓存和未缓存的文本
    async fn separate_cached_and_uncached(
        &self,
        texts: &[String],
    ) -> (HashMap<String, Embedding>, Vec<String>) {
        let mut cached = HashMap::new();
        let mut uncached = Vec::new();

        for text in texts {
            let cache_key = EmbeddingCacheKey::new(
                text,
                &self.provider.model_info().name,
                self.provider.dimension(),
            );

            if let Some(embedding) = self.get_from_cache(&cache_key).await {
                cached.insert(text.clone(), embedding);
            } else {
                uncached.push(text.clone());
            }
        }

        (cached, uncached)
    }

    /// 使用优化策略计算批量嵌入
    async fn compute_batch_with_optimization(
        &self,
        texts: &[String],
    ) -> Result<HashMap<String, Embedding>> {
        let mut results = HashMap::new();

        // 按配置的批次大小分割
        for chunk in texts.chunks(self.config.batch_size) {
            // 获取并发许可
            let _permit = self.semaphore.acquire().await
                .map_err(|e| KnowledgeError::embedding_error(&format!("获取并发许可失败: {}", e)))?;

            // 调用底层提供者的批量接口
            match self.provider.embed_batch(&chunk.to_vec()).await {
                Ok(batch_result) => {
                    // 将结果映射回文本
                    for (i, embedding) in batch_result.embeddings.into_iter().enumerate() {
                        if i < chunk.len() {
                            let text = &chunk[i];

                            // 存储到缓存
                            if self.config.enable_embedding_cache {
                                let cache_key = EmbeddingCacheKey::new(
                                    text,
                                    &self.provider.model_info().name,
                                    self.provider.dimension(),
                                );
                                self.put_to_cache(cache_key, embedding.clone()).await;
                            }

                            results.insert(text.clone(), embedding);
                        }
                    }
                }
                Err(e) => {
                    warn!("批量嵌入计算失败: {}", e);
                    // 降级到单个文本处理
                    for text in chunk {
                        match self.provider.embed_text(text).await {
                            Ok(embedding) => {
                                if self.config.enable_embedding_cache {
                                    let cache_key = EmbeddingCacheKey::new(
                                        text,
                                        &self.provider.model_info().name,
                                        self.provider.dimension(),
                                    );
                                    self.put_to_cache(cache_key, embedding.clone()).await;
                                }
                                results.insert(text.clone(), embedding);
                            }
                            Err(e) => {
                                warn!("单个文本嵌入计算失败: {} - {}", text, e);
                            }
                        }
                    }
                }
            }
        }

        Ok(results)
    }

    /// 更新缓存命中统计
    async fn update_cache_hit_metrics(&self, duration: Duration) {
        let mut metrics = self.metrics.write().await;
        metrics.cache_hits += 1;
        self.update_hit_rate(&mut metrics);
        self.update_avg_time(&mut metrics, duration);
    }

    /// 更新缓存未命中统计
    async fn update_cache_miss_metrics(&self, duration: Duration) {
        let mut metrics = self.metrics.write().await;
        metrics.cache_misses += 1;
        self.update_hit_rate(&mut metrics);
        self.update_avg_time(&mut metrics, duration);
    }

    /// 更新无缓存统计
    async fn update_no_cache_metrics(&self, duration: Duration) {
        let mut metrics = self.metrics.write().await;
        self.update_avg_time(&mut metrics, duration);
    }

    /// 更新批量处理统计
    async fn update_batch_metrics(&self, batch_size: usize, duration_ms: u64) {
        let mut metrics = self.metrics.write().await;
        metrics.batch_stats.total_batches += 1;

        // 更新平均批次大小
        let total_items = metrics.batch_stats.avg_batch_size * (metrics.batch_stats.total_batches - 1) as f64 + batch_size as f64;
        metrics.batch_stats.avg_batch_size = total_items / metrics.batch_stats.total_batches as f64;

        // 估算节省的时间（假设单个处理需要更多时间）
        let estimated_individual_time = duration_ms * 2; // 简化估算
        if estimated_individual_time > duration_ms {
            metrics.batch_stats.time_saved_ms += estimated_individual_time - duration_ms;
        }
    }

    /// 更新命中率
    fn update_hit_rate(&self, metrics: &mut PerformanceMetrics) {
        let total = metrics.cache_hits + metrics.cache_misses;
        if total > 0 {
            metrics.cache_hit_rate = metrics.cache_hits as f64 / total as f64;
        }
    }

    /// 更新平均处理时间
    fn update_avg_time(&self, metrics: &mut PerformanceMetrics, duration: Duration) {
        let duration_ms = duration.as_millis() as f64;
        let total_requests = metrics.total_requests as f64;

        if total_requests > 1.0 {
            metrics.avg_processing_time_ms =
                (metrics.avg_processing_time_ms * (total_requests - 1.0) + duration_ms) / total_requests;
        } else {
            metrics.avg_processing_time_ms = duration_ms;
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::embedding::local_embedder::LocalEmbedder;
    use crate::config::EmbeddingConfig;

    fn create_test_config() -> PerformanceConfig {
        PerformanceConfig {
            enable_embedding_cache: true,
            cache_max_entries: 10,
            cache_ttl_seconds: 60,
            batch_size: 4,
            max_concurrency: 2,
            enable_preprocessing: true,
            enable_memory_pool: true,
            metrics_interval_seconds: 10,
        }
    }

    #[test]
    fn test_performance_config_default() {
        let config = PerformanceConfig::default();
        assert!(config.enable_embedding_cache);
        assert_eq!(config.cache_max_entries, 10000);
        assert_eq!(config.batch_size, 32);
        assert_eq!(config.max_concurrency, 4);
    }

    #[test]
    fn test_embedding_cache_key() {
        let key1 = EmbeddingCacheKey::new("hello world", "test-model", 768);
        let key2 = EmbeddingCacheKey::new("hello world", "test-model", 768);
        let key3 = EmbeddingCacheKey::new("hello world", "other-model", 768);

        assert_eq!(key1, key2);
        assert_ne!(key1, key3);
    }

    #[test]
    fn test_cache_entry_expiration() {
        let embedding = Embedding {
            vector: vec![0.1, 0.2, 0.3],
            dimension: 3,
            model_info: ModelInfo {
                name: "test".to_string(),
                version: "1.0".to_string(),
                model_type: "test".to_string(),
            },
            created_at: chrono::Utc::now(),
        };

        let entry = CacheEntry::new(embedding);

        // 新创建的条目不应该过期
        assert!(!entry.is_expired(Duration::from_secs(60)));

        // 使用很短的 TTL 应该过期
        assert!(entry.is_expired(Duration::from_nanos(1)));
    }

    #[tokio::test]
    #[ignore] // 需要本地模型
    async fn test_performance_embedder_creation() {
        let embedding_config = EmbeddingConfig::default();
        let performance_config = create_test_config();

        // 这个测试需要实际的本地嵌入器，所以标记为 ignore
        if let Ok(provider) = LocalEmbedder::new(&embedding_config).await {
            let performance_embedder = PerformanceEmbedder::new(provider, performance_config);

            assert_eq!(performance_embedder.provider_dimension(), embedding_config.dimension);

            let metrics = performance_embedder.get_metrics().await;
            assert_eq!(metrics.total_requests, 0);
            assert_eq!(metrics.cache_hits, 0);
        }
    }

    #[tokio::test]
    async fn test_performance_metrics_initialization() {
        let metrics = PerformanceMetrics::default();

        assert_eq!(metrics.total_requests, 0);
        assert_eq!(metrics.cache_hits, 0);
        assert_eq!(metrics.cache_misses, 0);
        assert_eq!(metrics.cache_hit_rate, 0.0);
        assert_eq!(metrics.avg_processing_time_ms, 0.0);
    }

    #[test]
    fn test_batch_stats() {
        let stats = BatchStats::default();

        assert_eq!(stats.total_batches, 0);
        assert_eq!(stats.avg_batch_size, 0.0);
        assert_eq!(stats.time_saved_ms, 0);
    }

    #[test]
    fn test_concurrency_stats() {
        let stats = ConcurrencyStats::default();

        assert_eq!(stats.current_concurrency, 0);
        assert_eq!(stats.max_concurrency, 0);
        assert_eq!(stats.avg_wait_time_ms, 0.0);
    }

    #[test]
    fn test_memory_stats() {
        let stats = MemoryStats::default();

        assert_eq!(stats.cache_memory_bytes, 0);
        assert_eq!(stats.pool_memory_bytes, 0);
        assert_eq!(stats.peak_memory_bytes, 0);
    }
}
