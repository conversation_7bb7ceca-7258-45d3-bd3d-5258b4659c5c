//! # 统计信息命令
//!
//! 提供知识库统计信息和分析功能。

use crate::error::Result;
use clap::Args;
use orion_knowledge::{KnowledgeBase, Config as KnowledgeConfig};
use serde::{Deserialize, Serialize};
use std::path::PathBuf;
use tracing::info;

/// 统计信息命令
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct StatsCommand {
    /// 知识库配置文件
    #[arg(short, long)]
    pub config: Option<PathBuf>,
    
    /// 显示详细统计信息
    #[arg(short, long)]
    pub verbose: bool,
    
    /// 显示语言分布
    #[arg(long)]
    pub languages: bool,
    
    /// 显示性能指标
    #[arg(long)]
    pub performance: bool,
    
    /// 显示最近活动
    #[arg(long)]
    pub recent: bool,
    
    /// 输出格式 (table, json, csv)
    #[arg(short, long, default_value = "table")]
    pub format: String,
    
    /// 输出到文件
    #[arg(short, long)]
    pub output: Option<PathBuf>,
}

impl StatsCommand {
    /// 执行统计命令
    pub async fn execute(&self) -> Result<()> {
        info!("获取知识库统计信息");
        
        let config = self.load_config().await?;
        let kb = KnowledgeBase::new(config).await?;
        
        let stats = kb.get_stats().await?;
        
        match self.format.as_str() {
            "json" => self.output_json(&stats).await?,
            "csv" => self.output_csv(&stats).await?,
            _ => self.output_table(&stats).await?,
        }
        
        Ok(())
    }

    /// 表格格式输出
    async fn output_table(&self, stats: &orion_knowledge::KnowledgeStats) -> Result<()> {
        let mut output = String::new();
        
        output.push_str("📊 知识库统计信息\n");
        output.push_str("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n");
        
        // 基础统计
        output.push_str("📈 基础统计\n");
        output.push_str(&format!("   📄 总代码块数: {:>12}\n", stats.total_chunks));
        output.push_str(&format!("   📁 总文件数:   {:>12}\n", stats.total_files));
        output.push_str(&format!("   🌐 语言数量:   {:>12}\n", stats.total_languages));
        output.push_str(&format!("   💾 数据库大小: {:>10.2} MB\n", stats.database_size as f64 / 1024.0 / 1024.0));
        output.push_str(&format!("   🕒 最后更新:   {}\n", stats.last_updated.format("%Y-%m-%d %H:%M:%S")));
        output.push_str("\n");
        
        // 文件状态统计
        output.push_str("📋 文件状态\n");
        output.push_str(&format!("   ✅ 已索引文件: {:>12}\n", stats.file_stats.indexed_files));
        output.push_str(&format!("   🔄 待更新文件: {:>12}\n", stats.file_stats.pending_updates));
        output.push_str(&format!("   ❌ 错误文件:   {:>12}\n", stats.file_stats.error_files));
        output.push_str("\n");
        
        // 性能指标
        if self.performance || self.verbose {
            output.push_str("🚀 性能指标\n");
            output.push_str(&format!("   ⚡ 平均索引速度: {:>8.2} 文件/秒\n", stats.performance_metrics.avg_indexing_speed));
            output.push_str(&format!("   ⏱️  总索引时间:   {:>8.2} 秒\n", stats.performance_metrics.total_indexing_time));
            output.push_str(&format!("   🧠 峰值内存使用: {:>8.2} MB\n", stats.performance_metrics.peak_memory_usage));
            output.push_str(&format!("   🏃 最近索引耗时: {:>8.2} 秒\n", stats.performance_metrics.last_index_duration));
            output.push_str("\n");
        }
        
        // 语言分布
        if (self.languages || self.verbose) && !stats.language_distribution.is_empty() {
            output.push_str("🌐 语言分布 (按文件数排序)\n");
            output.push_str("   ┌──────────────┬────────────┬────────────┬─────────────────┐\n");
            output.push_str("   │ 语言         │   文件数   │  代码块数  │ 平均块大小(行)  │\n");
            output.push_str("   ├──────────────┼────────────┼────────────┼─────────────────┤\n");
            
            let mut langs: Vec<_> = stats.language_distribution.iter().collect();
            langs.sort_by(|a, b| b.1.file_count.cmp(&a.1.file_count));
            
            for (lang, lang_stats) in langs.iter().take(15) {
                output.push_str(&format!("   │ {:<12} │ {:>10} │ {:>10} │ {:>13.1}   │\n",
                    lang,
                    lang_stats.file_count,
                    lang_stats.chunk_count,
                    lang_stats.avg_chunk_size
                ));
            }
            
            if langs.len() > 15 {
                output.push_str(&format!("   │ ... 其他 {} 种语言                                      │\n", langs.len() - 15));
            }
            
            output.push_str("   └──────────────┴────────────┴────────────┴─────────────────┘\n\n");
        }
        
        // 最近活动
        if (self.recent || self.verbose) && !stats.file_stats.recent_files.is_empty() {
            output.push_str("📝 最近索引的文件\n");
            for (i, file) in stats.file_stats.recent_files.iter().take(10).enumerate() {
                output.push_str(&format!("   {}. 📄 {}\n", i + 1, file));
            }
            if stats.file_stats.recent_files.len() > 10 {
                output.push_str(&format!("   ... 还有 {} 个文件\n", stats.file_stats.recent_files.len() - 10));
            }
            output.push_str("\n");
        }
        
        // 输出结果
        if let Some(output_path) = &self.output {
            tokio::fs::write(output_path, &output).await?;
            println!("📄 统计信息已保存到: {}", output_path.display());
        } else {
            print!("{}", output);
        }
        
        Ok(())
    }

    /// JSON格式输出
    async fn output_json(&self, stats: &orion_knowledge::KnowledgeStats) -> Result<()> {
        let json_output = serde_json::to_string_pretty(stats)?;
        
        if let Some(output_path) = &self.output {
            tokio::fs::write(output_path, &json_output).await?;
            println!("📄 JSON统计信息已保存到: {}", output_path.display());
        } else {
            println!("{}", json_output);
        }
        
        Ok(())
    }

    /// CSV格式输出
    async fn output_csv(&self, stats: &orion_knowledge::KnowledgeStats) -> Result<()> {
        let mut csv_output = String::new();
        
        // 基础统计CSV
        csv_output.push_str("metric,value\n");
        csv_output.push_str(&format!("total_chunks,{}\n", stats.total_chunks));
        csv_output.push_str(&format!("total_files,{}\n", stats.total_files));
        csv_output.push_str(&format!("total_languages,{}\n", stats.total_languages));
        csv_output.push_str(&format!("database_size_mb,{:.2}\n", stats.database_size as f64 / 1024.0 / 1024.0));
        csv_output.push_str(&format!("indexed_files,{}\n", stats.file_stats.indexed_files));
        csv_output.push_str(&format!("pending_updates,{}\n", stats.file_stats.pending_updates));
        csv_output.push_str(&format!("error_files,{}\n", stats.file_stats.error_files));
        csv_output.push_str(&format!("avg_indexing_speed,{:.2}\n", stats.performance_metrics.avg_indexing_speed));
        csv_output.push_str(&format!("total_indexing_time,{:.2}\n", stats.performance_metrics.total_indexing_time));
        csv_output.push_str(&format!("peak_memory_usage_mb,{:.2}\n", stats.performance_metrics.peak_memory_usage));
        csv_output.push_str(&format!("last_update,{}\n", stats.last_updated.format("%Y-%m-%d %H:%M:%S")));
        
        // 语言分布CSV
        if !stats.language_distribution.is_empty() {
            csv_output.push_str("\nlanguage,file_count,chunk_count,avg_chunk_size\n");
            let mut langs: Vec<_> = stats.language_distribution.iter().collect();
            langs.sort_by(|a, b| b.1.file_count.cmp(&a.1.file_count));
            
            for (lang, lang_stats) in langs {
                csv_output.push_str(&format!("{},{},{},{:.1}\n",
                    lang,
                    lang_stats.file_count,
                    lang_stats.chunk_count,
                    lang_stats.avg_chunk_size
                ));
            }
        }
        
        if let Some(output_path) = &self.output {
            tokio::fs::write(output_path, &csv_output).await?;
            println!("📄 CSV统计信息已保存到: {}", output_path.display());
        } else {
            println!("{}", csv_output);
        }
        
        Ok(())
    }

    /// 加载知识库配置
    async fn load_config(&self) -> Result<KnowledgeConfig> {
        match &self.config {
            Some(path) => {
                info!("使用配置文件: {}", path.display());
                Ok(KnowledgeConfig::from_file(path)?)
            }
            None => {
                let default_paths = [
                    "knowledge.toml",
                    ".orion/knowledge.toml",
                    "orion-knowledge.toml"
                ];
                
                for path in &default_paths {
                    if std::path::Path::new(path).exists() {
                        info!("使用配置文件: {}", path);
                        return Ok(KnowledgeConfig::from_file(path)?);
                    }
                }
                
                info!("未找到配置文件，使用默认配置");
                Ok(KnowledgeConfig::default())
            }
        }
    }
}