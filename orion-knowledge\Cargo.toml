[package]
name = "orion-knowledge"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true
repository.workspace = true
description = "Orion AI Agent 智能代码知识库 - RAG系统与语义检索"
keywords.workspace = true
categories.workspace = true

[dependencies]
# 核心库依赖
orion-core = { path = "../orion-core" }

# 异步运行时
tokio = { workspace = true }

# 序列化
serde = { workspace = true }
serde_json = { workspace = true }

# 错误处理
anyhow = { workspace = true }
thiserror = { workspace = true }

# 数据库
rusqlite = { workspace = true }

# UUID 生成
uuid = { workspace = true }

# 时间处理
chrono = { workspace = true }

# 日志系统
tracing = { workspace = true }

# Tree-sitter 解析器
tree-sitter = "0.25.8"
tree-sitter-rust = "0.24.0"
tree-sitter-javascript = "0.23.1"
tree-sitter-typescript = "0.23.2"
tree-sitter-python = "0.23.6"
tree-sitter-java = "0.23.5"
tree-sitter-cpp = "0.23.4"
tree-sitter-c = "0.24.1"
tree-sitter-go = "0.23.4"

# 向量数学
nalgebra = "0.33.2"

# 文件系统操作
walkdir = "2.4"
ignore = "0.4"

# 正则表达式
regex = { workspace = true }

# 哈希算法
md5 = "0.7"

# 异步 trait
async-trait = "0.1"

# 序列化格式
toml = { workspace = true }

# 机器学习和嵌入模型
candle-core = "0.8.0"
candle-nn = "0.8.0"
candle-transformers = "0.8.0"
tokenizers = "0.20.4"
hf-hub = { version = "0.3.2", features = ["tokio"] }

# HTTP 客户端（用于下载模型和 API 调用）
reqwest = { version = "0.12", features = ["json", "stream"] }

# 速率限制
governor = "0.6"

# 重试机制
backoff = { version = "0.4", features = ["tokio"] }

# 随机数生成
rand = "0.8"

# 文本处理
unicode-normalization = "0.1"
unicode-segmentation = "1.12"

# 并行处理
rayon = "1.10"

# 内存映射文件
memmap2 = "0.9"

# 系统目录
dirs = "5.0"

# 异步工具
futures = "0.3"

[dev-dependencies]
# 测试工具
tempfile = "3.0"
tracing-subscriber = "0.3"
