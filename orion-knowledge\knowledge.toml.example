# Orion Knowledge Base 配置文件示例

[database]
# 数据库文件路径
path = "./.orion/knowledge.db"
# 连接池大小
pool_size = 10
# 查询超时时间（秒）
query_timeout = 30
# 启用 WAL 模式以提高并发性能
enable_wal = true
# 缓存大小（MB）
cache_size_mb = 64

[parser]
# 支持的编程语言列表
supported_languages = [
    "rust",
    "javascript", 
    "typescript",
    "python",
    "java",
    "cpp",
    "c",
    "go"
]
# 最大文件大小限制（10MB）
max_file_size = 10485760
# 分块策略：Semantic（语义分块）、FixedSize（固定大小）、SlidingWindow（滑动窗口）、Hybrid（混合）
chunking_strategy = "Semantic"
# 分块大小（字符数）
chunk_size = 1000
# 分块重叠大小（字符数）
chunk_overlap = 200

[embedding]
# 嵌入模型类型：Local（本地）、OpenAI、Ollama、Custom（自定义）
model_type = "Ollama"
# 模型路径或 API 端点
model_path = "http://localhost:11434"
# 向量维度
dimension = 384
# 批处理大小
batch_size = 32
# API 密钥（如果使用远程服务）
# api_key = "your-api-key-here"

[query]
# 默认搜索结果数量
default_top_k = 10
# 最大搜索结果数量
max_top_k = 100
# 相似度阈值（0.0-1.0）
similarity_threshold = 0.7
# 混合搜索权重（向量搜索 vs 关键词搜索）
hybrid_weight = 0.7

[context]
# 最大上下文长度（tokens）
max_context_length = 4096
# 上下文窗口大小
context_window = 2048
# 启用智能压缩
enable_compression = true
# 压缩比例
compression_ratio = 0.8