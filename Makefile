# Orion CLI Makefile
# 参考 gemini-cli 的构建脚本

.PHONY: help build test install dev-install start clean release

# 默认目标
help:
	@echo "Orion CLI 开发工具"
	@echo ""
	@echo "可用命令:"
	@echo "  make build        - 构建项目"
	@echo "  make test         - 运行测试"
	@echo "  make install      - 安装到全局"
	@echo "  make dev-install  - 开发模式安装"
	@echo "  make start ARGS=  - 开发模式运行 (例: make start ARGS='--help')"
	@echo "  make clean        - 清理构建产物"
	@echo "  make release      - 发布版本构建"

# 构建项目
build:
	@echo "Building Orion CLI..."
	cd orion-cli && cargo build

# 运行测试
test:
	@echo "Running tests..."
	cargo test --workspace

# 安装到全局
install:
	@echo "Installing Orion CLI globally..."
	cd orion-cli && cargo install --path .

# 开发模式安装
dev-install:
	@echo "Installing Orion CLI for development..."
	cd orion-cli && cargo install --path . --force
	@echo "Testing installation..."
	orion --version

# 开发模式运行
start:
	@echo "Starting Orion CLI in development mode..."
	cd orion-cli && cargo run -- $(ARGS)

# 清理构建产物
clean:
	@echo "Cleaning build artifacts..."
	cargo clean

# 发布版本构建
release:
	@echo "Building release version..."
	cd orion-cli && cargo build --release

# 格式化代码
format:
	@echo "Formatting code..."
	cargo fmt

# 检查代码
lint:
	@echo "Linting code..."
	cargo clippy -- -D warnings

# 完整的预发布检查
preflight: clean format lint test build
	@echo "✅ All checks passed!"