//! # 简化测试
//! 
//! 测试知识库基本功能

use orion_knowledge::{Config, KnowledgeBase};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 使用默认配置
    let config = Config::default();
    let kb = KnowledgeBase::new(config).await?;
    
    println!("✅ 知识库初始化成功！");
    
    // 获取基本统计信息
    let stats = kb.get_stats().await?;
    println!("📊 当前统计：");
    println!("  - 代码块数量: {}", stats.total_chunks);
    println!("  - 文件数量: {}", stats.total_files);
    println!("  - 编程语言数量: {}", stats.total_languages);
    
    Ok(())
}