//! # 嵌入系统性能测试
//!
//! 测试嵌入系统的性能特性，包括吞吐量、延迟、并发性能等。

#[cfg(test)]
mod tests {
    use crate::embedding::{
        model_registry::{ModelRegistry, ModelConfig, ModelMetrics},
        performance::{PerformanceConfig, PerformanceMetrics},
        memory_pool::{VectorMemoryPool, MemoryPoolConfig},
        manager::EmbedderType,
    };
    use std::sync::Arc;
    use std::time::{Duration, Instant};
    use tokio::time::sleep;

    /// 性能测试配置
    struct PerformanceTestConfig {
        /// 测试持续时间
        duration_seconds: u64,
        /// 并发数
        concurrency: usize,
        /// 批量大小
        batch_size: usize,
        /// 向量维度
        vector_dimension: usize,
    }

    impl Default for PerformanceTestConfig {
        fn default() -> Self {
            Self {
                duration_seconds: 5,
                concurrency: 4,
                batch_size: 32,
                vector_dimension: 768,
            }
        }
    }

    #[tokio::test]
    async fn test_model_registry_performance() {
        let registry = Arc::new(ModelRegistry::new());
        let test_config = PerformanceTestConfig::default();
        
        println!("开始模型注册表性能测试...");
        let start_time = Instant::now();
        
        // 并发注册大量模型
        let mut handles = vec![];
        let models_per_task = 100;
        
        for task_id in 0..test_config.concurrency {
            let registry_clone = registry.clone();
            let handle = tokio::spawn(async move {
                let mut operations = 0;
                
                for i in 0..models_per_task {
                    let model_id = format!("perf-model-{}-{}", task_id, i);
                    let config = ModelConfig::new(
                        model_id.clone(),
                        EmbedderType::Local,
                        format!("test-model-{}", i),
                        768,
                    );
                    
                    if registry_clone.register_model(config).await.is_ok() {
                        operations += 1;
                    }
                    
                    // 更新一些指标
                    if i % 10 == 0 {
                        let _ = registry_clone.update_model_metrics(&model_id, 100.0, true).await;
                    }
                }
                
                operations
            });
            handles.push(handle);
        }
        
        // 等待所有任务完成
        let mut total_operations = 0;
        for handle in handles {
            total_operations += handle.await.unwrap();
        }
        
        let elapsed = start_time.elapsed();
        let ops_per_second = total_operations as f64 / elapsed.as_secs_f64();
        
        println!("模型注册表性能测试结果:");
        println!("  总操作数: {}", total_operations);
        println!("  耗时: {:?}", elapsed);
        println!("  操作/秒: {:.2}", ops_per_second);
        
        // 验证所有模型都已注册
        let models = registry.list_models().await;
        assert_eq!(models.len(), total_operations);
        
        // 性能断言（这些值可能需要根据实际环境调整）
        assert!(ops_per_second > 100.0, "注册操作性能过低: {:.2} ops/sec", ops_per_second);
    }

    #[tokio::test]
    async fn test_concurrent_model_selection() {
        let registry = Arc::new(ModelRegistry::new());
        
        // 预先注册一些模型
        for i in 0..50 {
            let config = ModelConfig::new(
                format!("select-model-{}", i),
                EmbedderType::Local,
                format!("test-model-{}", i),
                768,
            ).with_priority(i % 10);
            
            registry.register_model(config).await.unwrap();
            registry.update_model_status(
                &format!("select-model-{}", i),
                crate::embedding::model_registry::ModelStatus::Available
            ).await.unwrap();
            
            // 添加随机性能指标
            let response_time = 50.0 + (i as f64 * 2.0);
            registry.update_model_metrics(&format!("select-model-{}", i), response_time, true).await.unwrap();
        }
        
        println!("开始并发模型选择性能测试...");
        let start_time = Instant::now();
        let test_duration = Duration::from_secs(3);
        
        // 并发执行模型选择
        let mut handles = vec![];
        
        for _ in 0..8 {
            let registry_clone = registry.clone();
            let handle = tokio::spawn(async move {
                let mut selections = 0;
                let task_start = Instant::now();
                
                while task_start.elapsed() < test_duration {
                    let criteria = crate::embedding::model_registry::ModelSelectionCriteria {
                        selection_strategy: crate::embedding::model_registry::ModelSelectionStrategy::LowestLatency,
                        ..Default::default()
                    };
                    
                    if registry_clone.get_best_model(&criteria).await.is_some() {
                        selections += 1;
                    }
                    
                    // 小延迟避免过度占用CPU
                    if selections % 100 == 0 {
                        tokio::task::yield_now().await;
                    }
                }
                
                selections
            });
            handles.push(handle);
        }
        
        // 收集结果
        let mut total_selections = 0;
        for handle in handles {
            total_selections += handle.await.unwrap();
        }
        
        let elapsed = start_time.elapsed();
        let selections_per_second = total_selections as f64 / elapsed.as_secs_f64();
        
        println!("并发模型选择性能测试结果:");
        println!("  总选择次数: {}", total_selections);
        println!("  耗时: {:?}", elapsed);
        println!("  选择/秒: {:.2}", selections_per_second);
        
        // 性能断言
        assert!(selections_per_second > 1000.0, "模型选择性能过低: {:.2} selections/sec", selections_per_second);
    }

    #[tokio::test]
    async fn test_memory_pool_performance() {
        let config = MemoryPoolConfig {
            initial_capacity: 100,
            max_pool_size: 500,
            vector_dimension: 768,
            enable_warmup: true,
            cleanup_interval_seconds: 60,
        };
        
        let pool = Arc::new(VectorMemoryPool::new(config.clone()).await);

        println!("开始内存池性能测试...");
        let start_time = Instant::now();
        let test_duration = Duration::from_secs(3);

        // 并发获取和释放向量
        let mut handles = vec![];

        for _ in 0..4 {
            let pool_clone = pool.clone();
            let handle = tokio::spawn(async move {
                let mut operations = 0;
                let task_start = Instant::now();
                
                while task_start.elapsed() < test_duration {
                    // 获取向量
                    let guard = pool_clone.acquire_vector(768).await;
                    
                    // 模拟使用向量
                    let _vector = guard.as_ref();
                    operations += 1;
                    
                    // guard 在这里自动释放
                    
                    if operations % 100 == 0 {
                        tokio::task::yield_now().await;
                    }
                }
                
                operations
            });
            handles.push(handle);
        }
        
        // 收集结果
        let mut total_operations = 0;
        for handle in handles {
            total_operations += handle.await.unwrap();
        }
        
        let elapsed = start_time.elapsed();
        let ops_per_second = total_operations as f64 / elapsed.as_secs_f64();
        
        // 获取池统计信息
        let stats = pool.get_stats().await;
        
        println!("内存池性能测试结果:");
        println!("  总操作数: {}", total_operations);
        println!("  耗时: {:?}", elapsed);
        println!("  操作/秒: {:.2}", ops_per_second);
        println!("  池命中率: {:.2}%", stats.hit_rate * 100.0);
        println!("  内存使用: {} bytes", stats.memory_usage_bytes);
        
        // 性能断言
        assert!(ops_per_second > 5000.0, "内存池性能过低: {:.2} ops/sec", ops_per_second);
        assert!(stats.hit_rate > 0.5, "池命中率过低: {:.2}", stats.hit_rate);
    }

    #[tokio::test]
    async fn test_metrics_collection_performance() {
        let registry = Arc::new(ModelRegistry::new());
        
        // 注册测试模型
        let model_id = "metrics-perf-test";
        let config = ModelConfig::new(
            model_id.to_string(),
            EmbedderType::Local,
            "test-model".to_string(),
            768,
        );
        registry.register_model(config).await.unwrap();
        
        println!("开始指标收集性能测试...");
        let start_time = Instant::now();
        let test_duration = Duration::from_secs(2);
        
        // 并发更新指标
        let mut handles = vec![];
        
        for _ in 0..6 {
            let registry_clone = registry.clone();
            let handle = tokio::spawn(async move {
                let mut updates = 0;
                let task_start = Instant::now();
                
                while task_start.elapsed() < test_duration {
                    let response_time = 50.0 + (updates as f64 % 100.0);
                    let success = updates % 10 != 0; // 90% 成功率
                    
                    if registry_clone.update_model_metrics(model_id, response_time, success).await.is_ok() {
                        updates += 1;
                    }
                    
                    if updates % 1000 == 0 {
                        tokio::task::yield_now().await;
                    }
                }
                
                updates
            });
            handles.push(handle);
        }
        
        // 收集结果
        let mut total_updates = 0;
        for handle in handles {
            total_updates += handle.await.unwrap();
        }
        
        let elapsed = start_time.elapsed();
        let updates_per_second = total_updates as f64 / elapsed.as_secs_f64();
        
        // 验证指标计算正确性
        let metrics = registry.get_model_metrics(model_id).await.unwrap();
        
        println!("指标收集性能测试结果:");
        println!("  总更新数: {}", total_updates);
        println!("  耗时: {:?}", elapsed);
        println!("  更新/秒: {:.2}", updates_per_second);
        println!("  最终指标:");
        println!("    总请求: {}", metrics.total_requests);
        println!("    错误率: {:.2}%", metrics.error_rate * 100.0);
        println!("    平均响应时间: {:.2}ms", metrics.avg_response_time_ms);
        
        // 性能断言
        assert!(updates_per_second > 10000.0, "指标更新性能过低: {:.2} updates/sec", updates_per_second);
        assert_eq!(metrics.total_requests, total_updates as u64);
        assert!(metrics.error_rate > 0.05 && metrics.error_rate < 0.15); // 大约10%错误率
    }

    #[tokio::test]
    async fn test_concurrent_registry_operations() {
        let registry = Arc::new(ModelRegistry::new());
        
        println!("开始并发注册表操作性能测试...");
        let start_time = Instant::now();
        let test_duration = Duration::from_secs(3);
        
        // 混合操作：注册、状态更新、指标更新、查询
        let mut handles = vec![];
        
        // 注册任务
        for task_id in 0..2 {
            let registry_clone = registry.clone();
            let handle = tokio::spawn(async move {
                let mut operations = 0;
                let task_start = Instant::now();
                
                while task_start.elapsed() < test_duration {
                    let model_id = format!("concurrent-{}-{}", task_id, operations);
                    let config = ModelConfig::new(
                        model_id,
                        EmbedderType::Local,
                        format!("test-{}", operations),
                        768,
                    );
                    
                    if registry_clone.register_model(config).await.is_ok() {
                        operations += 1;
                    }
                    
                    if operations % 50 == 0 {
                        tokio::task::yield_now().await;
                    }
                }
                
                ("register", operations)
            });
            handles.push(handle);
        }
        
        // 查询任务
        for _ in 0..2 {
            let registry_clone = registry.clone();
            let handle = tokio::spawn(async move {
                let mut operations = 0;
                let task_start = Instant::now();
                
                while task_start.elapsed() < test_duration {
                    let _models = registry_clone.list_models().await;
                    let _summary = registry_clone.get_registry_summary().await;
                    operations += 2;
                    
                    if operations % 100 == 0 {
                        tokio::task::yield_now().await;
                    }
                }
                
                ("query", operations)
            });
            handles.push(handle);
        }
        
        // 收集结果
        let mut results = std::collections::HashMap::new();
        for handle in handles {
            let (op_type, count) = handle.await.unwrap();
            *results.entry(op_type).or_insert(0) += count;
        }
        
        let elapsed = start_time.elapsed();
        let total_ops: i32 = results.values().sum();
        let ops_per_second = total_ops as f64 / elapsed.as_secs_f64();
        
        println!("并发注册表操作性能测试结果:");
        println!("  总操作数: {}", total_ops);
        println!("  耗时: {:?}", elapsed);
        println!("  操作/秒: {:.2}", ops_per_second);
        for (op_type, count) in results {
            println!("  {}: {}", op_type, count);
        }
        
        // 验证数据一致性
        let final_models = registry.list_models().await;
        let summary = registry.get_registry_summary().await;
        assert_eq!(final_models.len(), summary.total_models);
        
        // 性能断言
        assert!(ops_per_second > 500.0, "并发操作性能过低: {:.2} ops/sec", ops_per_second);
    }

    #[test]
    fn test_performance_config_optimization() {
        // 测试不同性能配置的影响
        let configs = vec![
            ("default", PerformanceConfig::default()),
            ("high_cache", PerformanceConfig {
                cache_max_entries: 50000,
                cache_ttl_seconds: 7200,
                ..Default::default()
            }),
            ("high_concurrency", PerformanceConfig {
                max_concurrency: 16,
                batch_size: 64,
                ..Default::default()
            }),
            ("memory_optimized", PerformanceConfig {
                enable_memory_pool: true,
                cache_max_entries: 1000,
                ..Default::default()
            }),
        ];
        
        for (name, config) in configs {
            // 验证配置的合理性
            assert!(config.cache_max_entries > 0, "{}: 缓存大小必须大于0", name);
            assert!(config.max_concurrency > 0, "{}: 并发数必须大于0", name);
            assert!(config.batch_size > 0, "{}: 批量大小必须大于0", name);
            
            // 测试序列化
            let serialized = serde_json::to_string(&config).unwrap();
            let deserialized: PerformanceConfig = serde_json::from_str(&serialized).unwrap();
            assert_eq!(config.cache_max_entries, deserialized.cache_max_entries);
            
            println!("配置 '{}' 验证通过", name);
        }
    }

    #[tokio::test]
    async fn test_memory_usage_patterns() {
        let pool = VectorMemoryPool::new(MemoryPoolConfig {
            initial_capacity: 10,
            max_pool_size: 100,
            vector_dimension: 1024,
            enable_warmup: true,
            cleanup_interval_seconds: 60,
        }).await;
        
        println!("开始内存使用模式测试...");
        
        // 测试内存增长模式
        let mut peak_memory = 0;
        let mut guards = vec![];
        
        for i in 0..50 {
            let guard = pool.acquire_vector(1024).await;
            guards.push(guard);
            
            let stats = pool.get_stats().await;
            peak_memory = peak_memory.max(stats.memory_usage_bytes);
            
            if i % 10 == 0 {
                println!("  第{}次分配，内存使用: {} bytes", i + 1, stats.memory_usage_bytes);
            }
        }
        
        println!("峰值内存使用: {} bytes", peak_memory);
        
        // 释放所有向量
        guards.clear();
        
        // 等待一小段时间让异步清理完成
        sleep(Duration::from_millis(100)).await;
        
        let final_stats = pool.get_stats().await;
        println!("释放后内存使用: {} bytes", final_stats.memory_usage_bytes);
        
        // 验证内存管理
        assert!(peak_memory > 0, "应该有内存使用");
        assert!(final_stats.memory_usage_bytes <= peak_memory, "释放后内存不应该增加");
    }
}
