//! # 备份管理命令
//!
//! 提供知识库备份、恢复和管理功能。

use crate::error::Result;
use clap::{Args, Subcommand};
use orion_knowledge::{KnowledgeBase, config::Config as KnowledgeConfig};
use serde::{Deserialize, Serialize};
use std::path::PathBuf;
use tracing::info;

/// 备份管理命令
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct BackupCommand {
    #[command(subcommand)]
    pub action: BackupAction,
}

/// 备份操作
#[derive(Debug, Clone, Subcommand, Serialize, Deserialize)]
pub enum BackupAction {
    /// 创建备份
    Create(CreateArgs),
    /// 恢复备份
    Restore(RestoreArgs),
    /// 列出备份
    List(ListArgs),
    /// 删除备份
    Delete(DeleteArgs),
    /// 清理旧备份
    Cleanup(CleanupArgs),
    /// 验证备份
    Verify(VerifyArgs),
}

/// 创建备份参数
#[derive(Debug, <PERSON><PERSON>, Args, Serialize, Deserialize)]
pub struct CreateArgs {
    /// 备份保存路径
    #[arg(short, long)]
    pub path: PathBuf,
    
    /// 知识库配置文件
    #[arg(short, long)]
    pub config: Option<PathBuf>,
    
    /// 备份描述
    #[arg(short, long)]
    pub description: Option<String>,
    
    /// 显示详细输出
    #[arg(short, long)]
    pub verbose: bool,
}

/// 恢复备份参数
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct RestoreArgs {
    /// 备份路径
    #[arg(short, long)]
    pub path: PathBuf,
    
    /// 知识库配置文件
    #[arg(short, long)]
    pub config: Option<PathBuf>,
    
    /// 跳过确认提示
    #[arg(short, long)]
    pub yes: bool,
    
    /// 显示详细输出
    #[arg(short, long)]
    pub verbose: bool,
}

/// 列出备份参数
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct ListArgs {
    /// 备份目录
    #[arg(short, long, default_value = "./backups")]
    pub backup_dir: PathBuf,
    
    /// 知识库配置文件
    #[arg(short, long)]
    pub config: Option<PathBuf>,
    
    /// 显示详细信息
    #[arg(short, long)]
    pub verbose: bool,
}

/// 删除备份参数
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct DeleteArgs {
    /// 备份路径
    #[arg(short, long)]
    pub path: PathBuf,
    
    /// 跳过确认提示
    #[arg(short, long)]
    pub yes: bool,
}

/// 清理备份参数
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct CleanupArgs {
    /// 备份目录
    #[arg(short, long, default_value = "./backups")]
    pub backup_dir: PathBuf,
    
    /// 保留备份数量
    #[arg(short = 'k', long, default_value = "5")]
    pub keep: usize,
    
    /// 知识库配置文件
    #[arg(short, long)]
    pub config: Option<PathBuf>,
    
    /// 跳过确认提示
    #[arg(short, long)]
    pub yes: bool,
    
    /// 显示详细输出
    #[arg(short, long)]
    pub verbose: bool,
}

/// 验证备份参数
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct VerifyArgs {
    /// 备份路径
    #[arg(short, long)]
    pub path: PathBuf,
    
    /// 知识库配置文件
    #[arg(short, long)]
    pub config: Option<PathBuf>,
    
    /// 显示详细输出
    #[arg(short, long)]
    pub verbose: bool,
}

impl BackupCommand {
    /// 执行备份命令
    pub async fn execute(&self) -> Result<()> {
        match &self.action {
            BackupAction::Create(args) => self.create_backup(args).await,
            BackupAction::Restore(args) => self.restore_backup(args).await,
            BackupAction::List(args) => self.list_backups(args).await,
            BackupAction::Delete(args) => self.delete_backup(args).await,
            BackupAction::Cleanup(args) => self.cleanup_backups(args).await,
            BackupAction::Verify(args) => self.verify_backup(args).await,
        }
    }

    /// 创建备份
    async fn create_backup(&self, args: &CreateArgs) -> Result<()> {
        info!("创建知识库备份到: {}", args.path.display());
        
        let config = self.load_config(&args.config).await?;
        let kb = KnowledgeBase::new(config).await?;
        
        let start_time = std::time::Instant::now();
        let backup_info = kb.create_backup(&args.path).await?;
        let duration = start_time.elapsed();
        
        println!("✅ 备份创建成功!");
        println!("   📁 备份路径: {}", backup_info.path.display());
        println!("   📊 备份大小: {:.2} MB", backup_info.size as f64 / 1024.0 / 1024.0);
        println!("   📄 文件数量: {}", backup_info.file_count);
        println!("   🔒 校验和: {}", &backup_info.checksum[..16]);
        println!("   ⏱️  耗时: {:.2} 秒", duration.as_secs_f64());
        
        if let Some(desc) = &args.description {
            println!("   📝 描述: {}", desc);
        }
        
        if args.verbose {
            println!("   🏷️  版本: {}", backup_info.version);
            println!("   🕒 创建时间: {}", backup_info.created_at.format("%Y-%m-%d %H:%M:%S"));
            println!("   🔒 完整校验和: {}", backup_info.checksum);
        }
        
        Ok(())
    }

    /// 恢复备份
    async fn restore_backup(&self, args: &RestoreArgs) -> Result<()> {
        if !args.yes {
            print!("⚠️  确定要从备份恢复吗？这将覆盖当前所有数据 [y/N]: ");
            use std::io::{self, Write};
            io::stdout().flush()?;
            
            let mut input = String::new();
            io::stdin().read_line(&mut input)?;
            
            if !input.trim().to_lowercase().starts_with('y') {
                println!("取消恢复");
                return Ok(());
            }
        }
        
        info!("从备份恢复: {}", args.path.display());
        
        let config = self.load_config(&args.config).await?;
        let mut kb = KnowledgeBase::new(config).await?;
        
        let start_time = std::time::Instant::now();
        kb.restore_from_backup(&args.path).await?;
        let duration = start_time.elapsed();
        
        println!("✅ 备份恢复成功!");
        println!("   📁 备份路径: {}", args.path.display());
        println!("   ⏱️  耗时: {:.2} 秒", duration.as_secs_f64());
        
        // 显示恢复后的统计信息
        let stats = kb.get_stats().await?;
        println!("\n📊 恢复后的知识库状态:");
        println!("   📄 代码块: {} 个", stats.total_chunks);
        println!("   📁 文件: {} 个", stats.total_files);
        println!("   🌐 语言: {} 种", stats.total_languages);
        
        Ok(())
    }

    /// 列出备份
    async fn list_backups(&self, args: &ListArgs) -> Result<()> {
        let config = self.load_config(&args.config).await?;
        let kb = KnowledgeBase::new(config).await?;
        
        let backups = kb.list_backups(&args.backup_dir).await?;
        
        if backups.is_empty() {
            println!("📂 备份目录 {} 中没有找到备份", args.backup_dir.display());
            return Ok(());
        }
        
        println!("📂 备份列表 ({} 个备份):", backups.len());
        
        for (i, backup) in backups.iter().enumerate() {
            println!("\n{}. 📦 {}", i + 1, backup.path.file_name().unwrap_or_default().to_string_lossy());
            println!("   📁 路径: {}", backup.path.display());
            println!("   🕒 创建时间: {}", backup.created_at.format("%Y-%m-%d %H:%M:%S"));
            println!("   📊 大小: {:.2} MB", backup.size as f64 / 1024.0 / 1024.0);
            println!("   📄 文件数: {}", backup.file_count);
            
            if args.verbose {
                println!("   🏷️  版本: {}", backup.version);
                println!("   🔒 校验和: {}", backup.checksum);
            }
        }
        
        Ok(())
    }

    /// 删除备份
    async fn delete_backup(&self, args: &DeleteArgs) -> Result<()> {
        if !args.yes {
            print!("⚠️  确定要删除备份 {} 吗？ [y/N]: ", args.path.display());
            use std::io::{self, Write};
            io::stdout().flush()?;
            
            let mut input = String::new();
            io::stdin().read_line(&mut input)?;
            
            if !input.trim().to_lowercase().starts_with('y') {
                println!("取消删除");
                return Ok(());
            }
        }
        
        info!("删除备份: {}", args.path.display());
        
        if args.path.is_dir() {
            tokio::fs::remove_dir_all(&args.path).await?;
        } else {
            tokio::fs::remove_file(&args.path).await?;
        }
        
        println!("✅ 备份已删除: {}", args.path.display());
        
        Ok(())
    }

    /// 清理旧备份
    async fn cleanup_backups(&self, args: &CleanupArgs) -> Result<()> {
        let config = self.load_config(&args.config).await?;
        let kb = KnowledgeBase::new(config).await?;
        
        let backups = kb.list_backups(&args.backup_dir).await?;
        
        if backups.len() <= args.keep {
            println!("📂 当前备份数量 ({}) 不超过保留数量 ({}), 无需清理", 
                     backups.len(), args.keep);
            return Ok(());
        }
        
        let to_delete = backups.len() - args.keep;
        
        if !args.yes {
            print!("⚠️  将删除 {} 个旧备份，保留最新的 {} 个，确定继续吗？ [y/N]: ", 
                   to_delete, args.keep);
            use std::io::{self, Write};
            io::stdout().flush()?;
            
            let mut input = String::new();
            io::stdin().read_line(&mut input)?;
            
            if !input.trim().to_lowercase().starts_with('y') {
                println!("取消清理");
                return Ok(());
            }
        }
        
        info!("清理旧备份，保留最新 {} 个", args.keep);
        
        let deleted_backups = kb.cleanup_backups(&args.backup_dir, args.keep).await?;
        
        println!("✅ 备份清理完成!");
        println!("   🗑️  删除: {} 个旧备份", deleted_backups.len());
        println!("   📦 保留: {} 个最新备份", args.keep);
        
        if args.verbose && !deleted_backups.is_empty() {
            println!("\n已删除的备份:");
            for backup_path in &deleted_backups {
                println!("   🗑️  {}", backup_path.display());
            }
        }
        
        Ok(())
    }

    /// 验证备份
    async fn verify_backup(&self, args: &VerifyArgs) -> Result<()> {
        info!("验证备份完整性: {}", args.path.display());
        
        let config = self.load_config(&args.config).await?;
        let _kb = KnowledgeBase::new(config).await?;
        
        // 读取备份信息
        let backup_info_path = args.path.join("backup_info.json");
        if !backup_info_path.exists() {
            println!("❌ 备份信息文件不存在: {}", backup_info_path.display());
            return Ok(());
        }
        
        let backup_info_json = tokio::fs::read_to_string(&backup_info_path).await?;
        let backup_info: orion_knowledge::BackupInfo = serde_json::from_str(&backup_info_json)?;
        
        println!("🔍 验证备份: {}", args.path.display());
        println!("   🏷️  版本: {}", backup_info.version);
        println!("   🕒 创建时间: {}", backup_info.created_at.format("%Y-%m-%d %H:%M:%S"));
        println!("   📊 预期大小: {:.2} MB", backup_info.size as f64 / 1024.0 / 1024.0);
        println!("   📄 预期文件数: {}", backup_info.file_count);
        
        let start_time = std::time::Instant::now();
        
        // 这里应该调用知识库的验证方法，但由于访问限制，我们手动实现
        let mut actual_size = 0u64;
        let mut actual_files = 0usize;
        let mut hasher = md5::Context::new();
        
        for entry in walkdir::WalkDir::new(&args.path) {
            let entry = entry.map_err(|e| crate::error::CliError::io_error(format!("备份目录遍历错误: {}", e)))?;
            if entry.file_type().is_file() && entry.file_name() != "backup_info.json" {
                let content = std::fs::read(entry.path())?;
                let metadata = entry.metadata().map_err(|e| crate::error::CliError::io_error(format!("获取文件元数据失败: {}", e)))?;
                
                hasher.consume(&content);
                actual_size += metadata.len();
                actual_files += 1;
            }
        }
        
        let calculated_checksum = format!("{:x}", hasher.compute());
        let duration = start_time.elapsed();
        
        println!("\n🔍 验证结果 (耗时: {:.2} 秒):", duration.as_secs_f64());
        
        let size_match = actual_size == backup_info.size;
        let checksum_match = calculated_checksum == backup_info.checksum;
        
        if size_match && checksum_match {
            println!("✅ 备份完整性验证通过");
            println!("   📊 实际大小: {:.2} MB ✓", actual_size as f64 / 1024.0 / 1024.0);
            println!("   🔒 校验和: {} ✓", &calculated_checksum[..16]);
        } else {
            println!("❌ 备份完整性验证失败");
            println!("   📊 大小匹配: {} (预期: {} MB, 实际: {:.2} MB)", 
                     if size_match { "✓" } else { "✗" },
                     backup_info.size as f64 / 1024.0 / 1024.0,
                     actual_size as f64 / 1024.0 / 1024.0);
            println!("   🔒 校验和匹配: {} (预期: {}, 实际: {})", 
                     if checksum_match { "✓" } else { "✗" },
                     &backup_info.checksum[..16],
                     &calculated_checksum[..16]);
        }
        
        if args.verbose {
            println!("\n📋 详细信息:");
            println!("   📄 实际文件数: {}", actual_files);
            println!("   🔒 完整校验和: {}", calculated_checksum);
        }
        
        Ok(())
    }

    /// 加载知识库配置
    async fn load_config(&self, config_path: &Option<PathBuf>) -> Result<KnowledgeConfig> {
        match config_path {
            Some(path) => {
                info!("使用配置文件: {}", path.display());
                Ok(KnowledgeConfig::from_file(path)?)
            }
            None => {
                let default_paths = [
                    "knowledge.toml",
                    ".orion/knowledge.toml",
                    "orion-knowledge.toml"
                ];
                
                for path in &default_paths {
                    if std::path::Path::new(path).exists() {
                        info!("使用配置文件: {}", path);
                        return Ok(KnowledgeConfig::from_file(path)?);
                    }
                }
                
                info!("未找到配置文件，使用默认配置");
                Ok(KnowledgeConfig::default())
            }
        }
    }
}