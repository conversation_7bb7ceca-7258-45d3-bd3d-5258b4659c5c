//! # 索引管理命令
//!
//! 提供代码索引的创建、更新和管理功能。

use crate::error::Result;
use clap::{Args, Subcommand};
use orion_knowledge::{KnowledgeBase, config::Config as KnowledgeConfig};
use serde::{Deserialize, Serialize};
use std::path::PathBuf;
use tracing::{info, warn};

/// 索引管理命令
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct IndexCommand {
    #[command(subcommand)]
    pub action: IndexAction,
}

/// 索引操作
#[derive(Debug, Clone, Subcommand, Serialize, Deserialize)]
pub enum IndexAction {
    /// 创建索引
    Create(CreateArgs),
    /// 更新索引
    Update(UpdateArgs),
    /// 重建索引
    Rebuild(RebuildArgs),
    /// 清理索引
    Clear(ClearArgs),
    /// 优化索引
    Optimize(OptimizeArgs),
    /// 索引状态
    Status(StatusArgs),
}

/// 创建索引参数
#[derive(Debug, <PERSON><PERSON>, Args, Serialize, Deserialize)]
pub struct CreateArgs {
    /// 要索引的目录路径
    #[arg(short, long, default_value = ".")]
    pub path: PathBuf,
    
    /// 知识库配置文件
    #[arg(short, long)]
    pub config: Option<PathBuf>,
    
    /// 强制覆盖现有索引
    #[arg(short, long)]
    pub force: bool,
    
    /// 显示详细输出
    #[arg(short, long)]
    pub verbose: bool,
}

/// 更新索引参数
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct UpdateArgs {
    /// 要更新的目录路径
    #[arg(short, long, default_value = ".")]
    pub path: PathBuf,
    
    /// 知识库配置文件
    #[arg(short, long)]
    pub config: Option<PathBuf>,
    
    /// 显示详细输出
    #[arg(short, long)]
    pub verbose: bool,
    
    /// 检查但不执行更新
    #[arg(long)]
    pub dry_run: bool,
}

/// 重建索引参数
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct RebuildArgs {
    /// 要重建的目录路径
    #[arg(short, long, default_value = ".")]
    pub path: PathBuf,
    
    /// 知识库配置文件
    #[arg(short, long)]
    pub config: Option<PathBuf>,
    
    /// 跳过确认提示
    #[arg(short, long)]
    pub yes: bool,
    
    /// 显示详细输出
    #[arg(short, long)]
    pub verbose: bool,
}

/// 清理索引参数
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct ClearArgs {
    /// 知识库配置文件
    #[arg(short, long)]
    pub config: Option<PathBuf>,
    
    /// 跳过确认提示
    #[arg(short, long)]
    pub yes: bool,
}

/// 优化索引参数
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct OptimizeArgs {
    /// 知识库配置文件
    #[arg(short, long)]
    pub config: Option<PathBuf>,
    
    /// 显示详细输出
    #[arg(short, long)]
    pub verbose: bool,
}

/// 索引状态参数
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct StatusArgs {
    /// 要检查的目录路径
    #[arg(short, long, default_value = ".")]
    pub path: PathBuf,
    
    /// 知识库配置文件
    #[arg(short, long)]
    pub config: Option<PathBuf>,
    
    /// 显示详细信息
    #[arg(short, long)]
    pub verbose: bool,
}

impl IndexCommand {
    /// 执行索引命令
    pub async fn execute(&self) -> Result<()> {
        match &self.action {
            IndexAction::Create(args) => self.create_index(args).await,
            IndexAction::Update(args) => self.update_index(args).await,
            IndexAction::Rebuild(args) => self.rebuild_index(args).await,
            IndexAction::Clear(args) => self.clear_index(args).await,
            IndexAction::Optimize(args) => self.optimize_index(args).await,
            IndexAction::Status(args) => self.show_status(args).await,
        }
    }

    /// 创建索引
    async fn create_index(&self, args: &CreateArgs) -> Result<()> {
        info!("创建知识库索引: {}", args.path.display());
        
        let config = self.load_config(&args.config).await?;
        let mut kb = KnowledgeBase::new(config).await?;
        
        // 检查是否已存在索引
        let stats = kb.get_stats().await?;
        if stats.total_chunks > 0 && !args.force {
            warn!("索引已存在，使用 --force 强制覆盖或使用 update 命令更新");
            return Ok(());
        }
        
        if args.force {
            info!("强制重建索引...");
            kb.clear().await?;
        }
        
        // 开始索引
        let start_time = std::time::Instant::now();
        kb.index_directory(&args.path).await?;
        let duration = start_time.elapsed();
        
        // 显示结果
        let stats = kb.get_stats().await?;
        println!("✅ 索引创建完成!");
        println!("   📁 索引文件: {} 个", stats.total_files);
        println!("   📄 代码块: {} 个", stats.total_chunks);
        println!("   🌐 语言: {} 种", stats.total_languages);
        println!("   ⏱️  耗时: {:.2} 秒", duration.as_secs_f64());
        
        if args.verbose {
            println!("\n📊 语言分布:");
            for (lang, lang_stats) in &stats.language_distribution {
                println!("   {} {} 文件, {} 代码块", lang, lang_stats.file_count, lang_stats.chunk_count);
            }
        }
        
        Ok(())
    }

    /// 更新索引
    async fn update_index(&self, args: &UpdateArgs) -> Result<()> {
        info!("更新知识库索引: {}", args.path.display());
        
        let config = self.load_config(&args.config).await?;
        let mut kb = KnowledgeBase::new(config).await?;
        
        // 获取需要更新的文件
        let pending_files = kb.get_pending_updates(&args.path).await?;
        
        if pending_files.is_empty() {
            println!("✅ 索引已是最新，无需更新");
            return Ok(());
        }
        
        println!("🔄 发现 {} 个文件需要更新", pending_files.len());
        
        if args.verbose {
            println!("\n待更新文件:");
            for file in &pending_files {
                println!("   📄 {}", file.display());
            }
        }
        
        if args.dry_run {
            println!("🔍 试运行模式，未执行实际更新");
            return Ok(());
        }
        
        // 执行增量更新
        let start_time = std::time::Instant::now();
        let updated_files = kb.incremental_update(&args.path).await?;
        let duration = start_time.elapsed();
        
        println!("✅ 索引更新完成!");
        println!("   🔄 更新文件: {} 个", updated_files.len());
        println!("   ⏱️  耗时: {:.2} 秒", duration.as_secs_f64());
        
        if args.verbose && !updated_files.is_empty() {
            println!("\n已更新文件:");
            for file in &updated_files {
                println!("   ✓ {}", file);
            }
        }
        
        Ok(())
    }

    /// 重建索引
    async fn rebuild_index(&self, args: &RebuildArgs) -> Result<()> {
        if !args.yes {
            print!("⚠️  确定要重建索引吗？这将删除所有现有数据 [y/N]: ");
            use std::io::{self, Write};
            io::stdout().flush()?;
            
            let mut input = String::new();
            io::stdin().read_line(&mut input)?;
            
            if !input.trim().to_lowercase().starts_with('y') {
                println!("取消重建");
                return Ok(());
            }
        }
        
        info!("重建知识库索引: {}", args.path.display());
        
        let config = self.load_config(&args.config).await?;
        let mut kb = KnowledgeBase::new(config).await?;
        
        let start_time = std::time::Instant::now();
        kb.rebuild_index(&args.path).await?;
        let duration = start_time.elapsed();
        
        let stats = kb.get_stats().await?;
        println!("✅ 索引重建完成!");
        println!("   📁 索引文件: {} 个", stats.total_files);
        println!("   📄 代码块: {} 个", stats.total_chunks);
        println!("   🌐 语言: {} 种", stats.total_languages);
        println!("   ⏱️  耗时: {:.2} 秒", duration.as_secs_f64());
        
        Ok(())
    }

    /// 清理索引
    async fn clear_index(&self, args: &ClearArgs) -> Result<()> {
        if !args.yes {
            print!("⚠️  确定要清理所有索引数据吗？ [y/N]: ");
            use std::io::{self, Write};
            io::stdout().flush()?;
            
            let mut input = String::new();
            io::stdin().read_line(&mut input)?;
            
            if !input.trim().to_lowercase().starts_with('y') {
                println!("取消清理");
                return Ok(());
            }
        }
        
        let config = self.load_config(&args.config).await?;
        let mut kb = KnowledgeBase::new(config).await?;
        
        kb.clear().await?;
        println!("✅ 索引数据已清理");
        
        Ok(())
    }

    /// 优化索引
    async fn optimize_index(&self, args: &OptimizeArgs) -> Result<()> {
        info!("优化知识库索引");
        
        let config = self.load_config(&args.config).await?;
        let mut kb = KnowledgeBase::new(config).await?;
        
        let start_time = std::time::Instant::now();
        kb.optimize().await?;
        let duration = start_time.elapsed();
        
        println!("✅ 索引优化完成 (耗时: {:.2} 秒)", duration.as_secs_f64());
        
        Ok(())
    }

    /// 显示索引状态
    async fn show_status(&self, args: &StatusArgs) -> Result<()> {
        let config = self.load_config(&args.config).await?;
        let kb = KnowledgeBase::new(config).await?;
        
        let stats = kb.get_stats().await?;
        let pending_files = kb.get_pending_updates(&args.path).await?;
        
        println!("📊 知识库状态");
        println!("   📁 总文件数: {}", stats.total_files);
        println!("   📄 代码块数: {}", stats.total_chunks);
        println!("   🌐 支持语言: {} 种", stats.total_languages);
        println!("   💾 数据库大小: {:.2} MB", stats.database_size as f64 / 1024.0 / 1024.0);
        println!("   🕒 最后更新: {}", stats.last_updated.format("%Y-%m-%d %H:%M:%S"));
        println!();
        
        println!("📈 文件状态");
        println!("   ✅ 已索引: {} 个", stats.file_stats.indexed_files);
        println!("   🔄 待更新: {} 个", pending_files.len());
        println!("   ❌ 错误文件: {} 个", stats.file_stats.error_files);
        println!();
        
        if args.verbose {
            println!("🚀 性能指标");
            println!("   ⚡ 平均索引速度: {:.2} 文件/秒", stats.performance_metrics.avg_indexing_speed);
            println!("   ⏱️  总索引时间: {:.2} 秒", stats.performance_metrics.total_indexing_time);
            println!("   🧠 峰值内存: {:.2} MB", stats.performance_metrics.peak_memory_usage);
            println!();
            
            if !stats.language_distribution.is_empty() {
                println!("🌐 语言分布");
                let mut langs: Vec<_> = stats.language_distribution.iter().collect();
                langs.sort_by(|a, b| b.1.file_count.cmp(&a.1.file_count));
                
                for (lang, lang_stats) in langs.iter().take(10) {
                    println!("   {} {} 文件, {} 代码块 (平均 {:.1} 行/块)", 
                             lang, 
                             lang_stats.file_count, 
                             lang_stats.chunk_count,
                             lang_stats.avg_chunk_size);
                }
            }
            
            if !stats.file_stats.recent_files.is_empty() {
                println!("\n📝 最近索引的文件");
                for file in stats.file_stats.recent_files.iter().take(5) {
                    println!("   📄 {}", file);
                }
            }
        }
        
        if !pending_files.is_empty() {
            println!("⚠️  提示: 运行 'orion knowledge index update' 更新索引");
        }
        
        Ok(())
    }

    /// 加载知识库配置
    async fn load_config(&self, config_path: &Option<PathBuf>) -> Result<KnowledgeConfig> {
        match config_path {
            Some(path) => {
                info!("使用配置文件: {}", path.display());
                Ok(KnowledgeConfig::from_file(path)?)
            }
            None => {
                // 查找默认配置文件
                let default_paths = [
                    "knowledge.toml",
                    ".orion/knowledge.toml", 
                    "orion-knowledge.toml"
                ];
                
                for path in &default_paths {
                    if std::path::Path::new(path).exists() {
                        info!("使用配置文件: {}", path);
                        return Ok(KnowledgeConfig::from_file(path)?);
                    }
                }
                
                info!("未找到配置文件，使用默认配置");
                Ok(KnowledgeConfig::default())
            }
        }
    }
}