//! # 搜索命令
//!
//! 提供代码语义搜索和关键词搜索功能。

use crate::error::Result;
use clap::{Args, Subcommand};
use orion_knowledge::{KnowledgeBase, config::Config as KnowledgeConfig};
use serde::{Deserialize, Serialize};
use std::path::PathBuf;
use tracing::{info, warn};

/// 搜索命令
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct SearchCommand {
    #[command(subcommand)]
    pub action: SearchAction,
}

/// 搜索操作
#[derive(Debug, Clone, Subcommand, Serialize, Deserialize)]
pub enum SearchAction {
    /// 语义搜索
    Semantic(SemanticArgs),
    /// 关键词搜索
    Keyword(KeywordArgs),
    /// 混合搜索
    Hybrid(HybridArgs),
    /// 文件搜索
    File(FileArgs),
    /// 符号搜索
    Symbol(SymbolArgs),
}

/// 语义搜索参数
#[derive(Debug, <PERSON><PERSON>, <PERSON>rg<PERSON>, Serialize, Deserialize)]
pub struct SemanticArgs {
    /// 搜索查询
    pub query: String,
    
    /// 知识库配置文件
    #[arg(short, long)]
    pub config: Option<PathBuf>,
    
    /// 返回结果数量
    #[arg(short = 'n', long, default_value = "10")]
    pub limit: usize,
    
    /// 最小相似度阈值 (0.0-1.0)
    #[arg(short, long, default_value = "0.6")]
    pub threshold: f32,
    
    /// 显示详细结果
    #[arg(short, long)]
    pub verbose: bool,
    
    /// 仅显示文件路径
    #[arg(long)]
    pub files_only: bool,
    
    /// 包含上下文
    #[arg(long)]
    pub with_context: bool,
}

/// 关键词搜索参数
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct KeywordArgs {
    /// 搜索关键词
    pub keywords: Vec<String>,
    
    /// 知识库配置文件
    #[arg(short, long)]
    pub config: Option<PathBuf>,
    
    /// 返回结果数量
    #[arg(short = 'n', long, default_value = "10")]
    pub limit: usize,
    
    /// 显示详细结果
    #[arg(short, long)]
    pub verbose: bool,
    
    /// 仅显示文件路径
    #[arg(long)]
    pub files_only: bool,
}

/// 混合搜索参数
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct HybridArgs {
    /// 搜索查询
    pub query: String,
    
    /// 知识库配置文件
    #[arg(short, long)]
    pub config: Option<PathBuf>,
    
    /// 返回结果数量
    #[arg(short = 'n', long, default_value = "10")]
    pub limit: usize,
    
    /// 语义搜索权重 (0.0-1.0)
    #[arg(long, default_value = "0.7")]
    pub semantic_weight: f32,
    
    /// 关键词搜索权重 (0.0-1.0)
    #[arg(long, default_value = "0.3")]
    pub keyword_weight: f32,
    
    /// 显示详细结果
    #[arg(short, long)]
    pub verbose: bool,
    
    /// 仅显示文件路径
    #[arg(long)]
    pub files_only: bool,
    
    /// 包含上下文
    #[arg(long)]
    pub with_context: bool,
}

/// 文件搜索参数
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct FileArgs {
    /// 文件路径模式
    pub pattern: String,
    
    /// 知识库配置文件
    #[arg(short, long)]
    pub config: Option<PathBuf>,
    
    /// 返回结果数量
    #[arg(short = 'n', long, default_value = "10")]
    pub limit: usize,
    
    /// 显示详细结果
    #[arg(short, long)]
    pub verbose: bool,
}

/// 符号搜索参数
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct SymbolArgs {
    /// 符号名称
    pub symbol: String,
    
    /// 知识库配置文件
    #[arg(short, long)]
    pub config: Option<PathBuf>,
    
    /// 返回结果数量
    #[arg(short = 'n', long, default_value = "10")]
    pub limit: usize,
    
    /// 显示详细结果
    #[arg(short, long)]
    pub verbose: bool,
}

impl SearchCommand {
    /// 执行搜索命令
    pub async fn execute(&self) -> Result<()> {
        match &self.action {
            SearchAction::Semantic(args) => self.semantic_search(args).await,
            SearchAction::Keyword(args) => self.keyword_search(args).await,
            SearchAction::Hybrid(args) => self.hybrid_search(args).await,
            SearchAction::File(args) => self.file_search(args).await,
            SearchAction::Symbol(args) => self.symbol_search(args).await,
        }
    }

    /// 语义搜索
    async fn semantic_search(&self, args: &SemanticArgs) -> Result<()> {
        info!("执行语义搜索: {}", args.query);
        
        let config = self.load_config(&args.config).await?;
        let kb = KnowledgeBase::new(config).await?;
        
        let results = kb.search(&args.query).await?;
        
        if results.is_empty() {
            println!("🔍 未找到匹配的结果");
            return Ok(());
        }
        
        // 过滤低于阈值的结果
        let filtered_results: Vec<_> = results.into_iter()
            .filter(|r| r.score >= args.threshold)
            .take(args.limit)
            .collect();
        
        if filtered_results.is_empty() {
            println!("🔍 未找到相似度高于 {:.2} 的结果", args.threshold);
            return Ok(());
        }
        
        println!("🔍 找到 {} 个相关结果:", filtered_results.len());
        
        if args.with_context {
            let context = kb.get_context(&filtered_results).await?;
            println!("\n📝 合成上下文 (置信度: {:.2}):", context.confidence);
            println!("{}", context.synthesized_context);
            println!();
        }
        
        for (i, result) in filtered_results.iter().enumerate() {
            if args.files_only {
                println!("{}", result.file_path);
            } else {
                println!("\n{}. 📄 {} (相似度: {:.3})", i + 1, result.file_path, result.score);
                
                if let Some(symbol) = &result.symbol_name {
                    println!("   🔧 符号: {}", symbol);
                }
                
                println!("   📍 行号: {}-{}", result.start_line, result.end_line);
                println!("   🌐 语言: {}", result.language);
                
                if args.verbose {
                    println!("   📄 内容:");
                    let lines: Vec<&str> = result.content.lines().collect();
                    for (line_idx, line) in lines.iter().enumerate() {
                        let line_num = result.start_line + line_idx;
                        println!("      {:4} | {}", line_num, line);
                    }
                }
            }
        }
        
        Ok(())
    }

    /// 关键词搜索
    async fn keyword_search(&self, args: &KeywordArgs) -> Result<()> {
        info!("执行关键词搜索: {:?}", args.keywords);
        
        let config = self.load_config(&args.config).await?;
        let kb = KnowledgeBase::new(config).await?;
        
        // 构造查询字符串
        let query = args.keywords.join(" ");
        let results = kb.search(&query).await?;
        
        if results.is_empty() {
            println!("🔍 未找到包含关键词的结果");
            return Ok(());
        }
        
        let limited_results: Vec<_> = results.into_iter().take(args.limit).collect();
        println!("🔍 找到 {} 个匹配结果:", limited_results.len());
        
        for (i, result) in limited_results.iter().enumerate() {
            if args.files_only {
                println!("{}", result.file_path);
            } else {
                println!("\n{}. 📄 {}", i + 1, result.file_path);
                
                if let Some(symbol) = &result.symbol_name {
                    println!("   🔧 符号: {}", symbol);
                }
                
                println!("   📍 行号: {}-{}", result.start_line, result.end_line);
                println!("   🌐 语言: {}", result.language);
                
                if args.verbose {
                    println!("   📄 内容:");
                    let lines: Vec<&str> = result.content.lines().collect();
                    for (line_idx, line) in lines.iter().enumerate() {
                        let line_num = result.start_line + line_idx;
                        
                        // 高亮关键词
                        let mut highlighted_line = line.to_string();
                        for keyword in &args.keywords {
                            highlighted_line = highlighted_line.replace(keyword, &format!("**{}**", keyword));
                        }
                        
                        println!("      {:4} | {}", line_num, highlighted_line);
                    }
                }
            }
        }
        
        Ok(())
    }

    /// 混合搜索
    async fn hybrid_search(&self, args: &HybridArgs) -> Result<()> {
        info!("执行混合搜索: {}", args.query);
        
        if (args.semantic_weight + args.keyword_weight - 1.0).abs() > 0.01 {
            warn!("语义权重和关键词权重之和应为1.0，当前为: {:.2}", 
                  args.semantic_weight + args.keyword_weight);
        }
        
        let config = self.load_config(&args.config).await?;
        let kb = KnowledgeBase::new(config).await?;
        
        let results = kb.search(&args.query).await?;
        
        if results.is_empty() {
            println!("🔍 未找到匹配的结果");
            return Ok(());
        }
        
        let limited_results: Vec<_> = results.into_iter().take(args.limit).collect();
        println!("🔍 混合搜索找到 {} 个结果:", limited_results.len());
        
        if args.with_context {
            let context = kb.get_context(&limited_results).await?;
            println!("\n📝 合成上下文 (置信度: {:.2}):", context.confidence);
            println!("{}", context.synthesized_context);
            println!();
        }
        
        for (i, result) in limited_results.iter().enumerate() {
            if args.files_only {
                println!("{}", result.file_path);
            } else {
                println!("\n{}. 📄 {} (综合分数: {:.3})", i + 1, result.file_path, result.score);
                
                if let Some(symbol) = &result.symbol_name {
                    println!("   🔧 符号: {}", symbol);
                }
                
                println!("   📍 行号: {}-{}", result.start_line, result.end_line);
                println!("   🌐 语言: {}", result.language);
                
                if args.verbose {
                    println!("   📄 内容:");
                    let lines: Vec<&str> = result.content.lines().collect();
                    for (line_idx, line) in lines.iter().enumerate() {
                        let line_num = result.start_line + line_idx;
                        println!("      {:4} | {}", line_num, line);
                    }
                }
            }
        }
        
        Ok(())
    }

    /// 文件搜索
    async fn file_search(&self, args: &FileArgs) -> Result<()> {
        info!("执行文件搜索: {}", args.pattern);
        
        let config = self.load_config(&args.config).await?;
        let kb = KnowledgeBase::new(config).await?;
        
        let results = kb.search_by_file(&args.pattern).await?;
        
        if results.is_empty() {
            println!("🔍 未找到匹配的文件");
            return Ok(());
        }
        
        let limited_results: Vec<_> = results.into_iter().take(args.limit).collect();
        println!("🔍 找到 {} 个匹配文件:", limited_results.len());
        
        for (i, result) in limited_results.iter().enumerate() {
            println!("\n{}. 📄 {}", i + 1, result.file_path);
            
            if let Some(symbol) = &result.symbol_name {
                println!("   🔧 符号: {}", symbol);
            }
            
            println!("   📍 行号: {}-{}", result.start_line, result.end_line);
            println!("   🌐 语言: {}", result.language);
            
            if args.verbose {
                println!("   📄 内容预览:");
                let lines: Vec<&str> = result.content.lines().take(5).collect();
                for (line_idx, line) in lines.iter().enumerate() {
                    let line_num = result.start_line + line_idx;
                    println!("      {:4} | {}", line_num, line);
                }
                if result.content.lines().count() > 5 {
                    println!("      ... ({} 行更多内容)", result.content.lines().count() - 5);
                }
            }
        }
        
        Ok(())
    }

    /// 符号搜索
    async fn symbol_search(&self, args: &SymbolArgs) -> Result<()> {
        info!("执行符号搜索: {}", args.symbol);
        
        let config = self.load_config(&args.config).await?;
        let kb = KnowledgeBase::new(config).await?;
        
        let results = kb.search_by_symbol(&args.symbol).await?;
        
        if results.is_empty() {
            println!("🔍 未找到匹配的符号");
            return Ok(());
        }
        
        let limited_results: Vec<_> = results.into_iter().take(args.limit).collect();
        println!("🔍 找到 {} 个匹配符号:", limited_results.len());
        
        for (i, result) in limited_results.iter().enumerate() {
            println!("\n{}. 🔧 {} ({})", i + 1, 
                     result.symbol_name.as_deref().unwrap_or("unnamed"),
                     result.file_path);
            
            println!("   📍 行号: {}-{}", result.start_line, result.end_line);
            println!("   🌐 语言: {}", result.language);
            
            if args.verbose {
                println!("   📄 内容:");
                let lines: Vec<&str> = result.content.lines().collect();
                for (line_idx, line) in lines.iter().enumerate() {
                    let line_num = result.start_line + line_idx;
                    println!("      {:4} | {}", line_num, line);
                }
            }
        }
        
        Ok(())
    }

    /// 加载知识库配置
    async fn load_config(&self, config_path: &Option<PathBuf>) -> Result<KnowledgeConfig> {
        match config_path {
            Some(path) => {
                info!("使用配置文件: {}", path.display());
                Ok(KnowledgeConfig::from_file(path)?)
            }
            None => {
                // 查找默认配置文件 
                let default_paths = [
                    "knowledge.toml",
                    ".orion/knowledge.toml",
                    "orion-knowledge.toml"
                ];
                
                for path in &default_paths {
                    if std::path::Path::new(path).exists() {
                        info!("使用配置文件: {}", path);
                        return Ok(KnowledgeConfig::from_file(path)?);
                    }
                }
                
                info!("未找到配置文件，使用默认配置");
                Ok(KnowledgeConfig::default())
            }
        }
    }
}