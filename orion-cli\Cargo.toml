[package]
name = "orion-cli"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true
repository.workspace = true
description = "Orion AI Agent 命令行界面 - 交互式Shell和配置管理"
keywords.workspace = true
categories.workspace = true

[dependencies]
# 核心库依赖
orion-core = { path = "../orion-core" }
orion-knowledge = { path = "../orion-knowledge" }

# 异步运行时
tokio = { workspace = true }

# CLI 解析
clap = { workspace = true }

# 序列化
serde = { workspace = true }
serde_json = { workspace = true }
toml = { workspace = true }

# 错误处理
anyhow = { workspace = true }
thiserror = { workspace = true }

# 序列化格式支持
serde_yaml = { workspace = true }

# 网络请求
reqwest = { workspace = true }

# 日志系统
tracing = { workspace = true }
tracing-subscriber = { workspace = true }
tracing-appender = { workspace = true }

# 交互式CLI
rustyline = { workspace = true }

# 终端检测
atty = "0.2"

# 终端操作
crossterm = "0.27"

# 时间处理
chrono = { workspace = true }

# UUID 生成
uuid = { version = "1.0", features = ["v4", "serde"] }

# 随机数生成
rand = "0.8"

# 目录操作
dirs = { workspace = true }

# 系统信息
num_cpus = { workspace = true }

# 文件系统遍历
walkdir = "2.4"

# 哈希算法
md5 = "0.7"

# Windows API (仅在 Windows 上)
[target.'cfg(windows)'.dependencies]
winapi = { workspace = true }

[build-dependencies]
# 构建时依赖
chrono = { workspace = true }

[dev-dependencies]
# 测试工具
tokio-test = "0.4"
tempfile = "3.0"

[[bin]]
name = "orion"
path = "src/main.rs"